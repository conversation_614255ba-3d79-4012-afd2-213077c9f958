package com.weifu.srm.bff.supplier;

import com.weifu.srm.common.config.MyLocaleResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

@EnableDiscoveryClient
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = {"com.weifu.srm.**.api"})
@EnableTransactionManagement
@EnableCaching
@EnableWebMvc
@Slf4j
@SpringBootApplication(scanBasePackages={"com.weifu.srm"}, exclude = {DataSourceAutoConfiguration.class})
public class SupplierBFFApplication {

    public static void main(String[] args) {
        SpringApplication.run(SupplierBFFApplication.class, args);
        log.info("启动成功ヽ(✿ﾟ▽ﾟ)ノ");
    }

    @Bean
    public LocaleResolver localeResolver() {
        return new MyLocaleResolver();
    }

}

