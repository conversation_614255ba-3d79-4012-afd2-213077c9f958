package com.weifu.srm.bff.supplier.controller;

import com.weifu.srm.bff.supplier.service.SupplierSwitchService;
import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.log.annotation.OperationLog;
import com.weifu.srm.log.constants.BehaviorConstants;
import com.weifu.srm.supplier.cio.api.SupplierAssessmentApi;
import com.weifu.srm.supplier.cio.request.assessment.SupplierAssessmentAcceptOrNotReqDTO;
import com.weifu.srm.supplier.cio.request.assessment.SupplierAssessmentPageReqDTO;
import com.weifu.srm.supplier.cio.response.assessment.SupplierAssessmentDetailRespDTO;
import com.weifu.srm.supplier.cio.response.assessment.SupplierAssessmentPageRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/9/13 21:16
 * @Description
 * @Version 1.0
 */
@Api(tags = "供应商索赔")
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class SupplierAssessmentBFFController {
private final SupplierAssessmentApi supplierAssessmentApi;
private final SupplierSwitchService supplierSwitchService;

    @ApiOperation("考核单列表分页查询")
    @PostMapping("/supplier/assessment/page")
    public ApiResponse<PageResponse<SupplierAssessmentPageRespDTO>> queryAdmissionQuestionnaire(@RequestBody SupplierAssessmentPageReqDTO req) {
        req.setUserName(SecurityContextHolder.getRealName());
        req.setUserId(SecurityContextHolder.getUserId());
        req.setSupplierCode(supplierSwitchService.getSapSupplierCodeByUserId());
        return supplierAssessmentApi.queryAssessmentPage(req);
    }
    @ApiOperation("考核单详情")
    @PostMapping("/supplier/assessment/detail/{assessmentNo}")
    ApiResponse<SupplierAssessmentDetailRespDTO> queryByAssessmentNo(@PathVariable("assessmentNo") @NotNull(message = "assessmentNo can not be null") String assessmentNo){
        return supplierAssessmentApi.queryByAssessmentNo(assessmentNo);
    }

    @OperationLog(behaviorCode = BehaviorConstants.SUPPLIER_ACCEPT_OR_DECLINE_EVALUATION, businessNo = "#result.data")
    @ApiOperation("供应商接受或者不接受考核")
    @PostMapping("/supplier/assessment/acceptOrNot")
    ApiResponse<String> acceptOrNot(@Valid @RequestBody SupplierAssessmentAcceptOrNotReqDTO reqDTO){
        reqDTO.setUserName(SecurityContextHolder.getRealName());
        reqDTO.setUserId(SecurityContextHolder.getUserId());
        return supplierAssessmentApi.acceptOrNot(reqDTO);
    }
}
