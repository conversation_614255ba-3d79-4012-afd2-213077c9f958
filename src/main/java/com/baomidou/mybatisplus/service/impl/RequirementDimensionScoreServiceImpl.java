package com.baomidou.mybatisplus.service.impl;

import com.baomidou.mybatisplus.entity.RequirementDimensionScore;
import com.baomidou.mybatisplus.mapper.RequirementDimensionScoreMapper;
import com.baomidou.mybatisplus.service.IRequirementDimensionScoreService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 需求文档指标评分表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class RequirementDimensionScoreServiceImpl extends ServiceImpl<RequirementDimensionScoreMapper, RequirementDimensionScore> implements IRequirementDimensionScoreService {

}
