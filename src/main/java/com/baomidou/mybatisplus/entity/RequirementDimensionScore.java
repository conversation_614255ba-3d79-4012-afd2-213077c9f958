package com.baomidou.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 需求文档指标评分表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@TableName("requirement_dimension_score")
public class RequirementDimensionScore implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 功能模块名称
     */
    private String moduleTitle;

    /**
     * 需求名称
     */
    private String requirementName;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 评分结果描述
     */
    private byte[] requirementDescription;

    /**
     * 评分指标
     */
    private String pointDimension;

    /**
     * 总分
     */
    private BigDecimal fullScore;

    /**
     * 评分
     */
    private BigDecimal score;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getModuleTitle() {
        return moduleTitle;
    }

    public void setModuleTitle(String moduleTitle) {
        this.moduleTitle = moduleTitle;
    }

    public String getRequirementName() {
        return requirementName;
    }

    public void setRequirementName(String requirementName) {
        this.requirementName = requirementName;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public byte[] getRequirementDescription() {
        return requirementDescription;
    }

    public void setRequirementDescription(byte[] requirementDescription) {
        this.requirementDescription = requirementDescription;
    }

    public String getPointDimension() {
        return pointDimension;
    }

    public void setPointDimension(String pointDimension) {
        this.pointDimension = pointDimension;
    }

    public BigDecimal getFullScore() {
        return fullScore;
    }

    public void setFullScore(BigDecimal fullScore) {
        this.fullScore = fullScore;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "RequirementDimensionScore{" +
            "id = " + id +
            ", fileName = " + fileName +
            ", moduleTitle = " + moduleTitle +
            ", requirementName = " + requirementName +
            ", contentType = " + contentType +
            ", requirementDescription = " + requirementDescription +
            ", pointDimension = " + pointDimension +
            ", fullScore = " + fullScore +
            ", score = " + score +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            "}";
    }
}
