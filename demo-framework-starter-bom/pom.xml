<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.demo.framework</groupId>
    <artifactId>demo-framework-starter-bom</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-common-util</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-cola</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-cloud</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-excel</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-file</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-file-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-file-mini-io</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-file-obs</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-file-oss</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-id</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-id-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-id-cosid</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-log</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-mq</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-mybatis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-cache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-cache-redis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-cache-redisson</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-nacos</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-rpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-security-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-security-impl</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-security-oauth2</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-swagger</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.demo.framework</groupId>
                <artifactId>demo-framework-starter-web</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>