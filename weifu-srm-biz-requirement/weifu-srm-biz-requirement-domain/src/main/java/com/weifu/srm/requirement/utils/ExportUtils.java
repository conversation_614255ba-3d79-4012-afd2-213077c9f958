package com.weifu.srm.requirement.utils;

import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.List;

public class ExportUtils {
    private ExportUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static XSSFWorkbook createWorkbook(List<String> sheetHead){
        XSSFWorkbook workbook = new XSSFWorkbook();
        ExportUtils.createSheet(workbook, sheetHead);
        return workbook;
    }

    public static void createSheet(XSSFWorkbook workbook, List<String> sheetType){
        String sheetName = sheetType.get(0);
        XSSFSheet sheet = workbook.createSheet(sheetName);
        // 写入表头
        XSSFRow indexRow = sheet.createRow(0);
        for (int i = 1; i < sheetType.size(); i++) {
            indexRow.createCell(i-1).setCellValue(sheetType.get(i));
        }
    }
}
