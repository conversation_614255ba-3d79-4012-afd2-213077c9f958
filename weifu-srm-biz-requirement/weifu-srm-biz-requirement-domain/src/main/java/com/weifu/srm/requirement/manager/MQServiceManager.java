package com.weifu.srm.requirement.manager;

import com.weifu.srm.mq.sender.manager.MqManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MQServiceManager {
    private final MqManager mqManager;

    public void sendMQ(String topic, String jsonStr) {
        log.info("start send MQ topic={} params={}", topic, jsonStr);
        mqManager.sendTopic(topic, jsonStr);
        log.info("end send MQ topic={} params={}", topic, jsonStr);
    }
    public void sendMQ(String topic,String key, String jsonStr) {
        log.info("start send MQ topic={} key={} params={}", topic, key, jsonStr);
        mqManager.sendTopic(topic, key, jsonStr);
        log.info("end send MQ topic={} key={} params={}", topic, key, jsonStr);
    }
}
