package com.weifu.srm.requirement.manager.project;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.requirement.atomicservice.ProjectMapperService;
import com.weifu.srm.requirement.atomicservice.ProjectTerminationMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.constants.RequirementBizConstants;
import com.weifu.srm.requirement.convert.BaseEntityConvert;
import com.weifu.srm.requirement.enums.ProjectNoticeTemplateEnum;
import com.weifu.srm.requirement.enums.*;
import com.weifu.srm.requirement.enums.project.ProjectLightStatusEnum;
import com.weifu.srm.requirement.enums.project.ProjectStatusEnum;
import com.weifu.srm.requirement.enums.project.ProjectTerminationResonEnum;
import com.weifu.srm.requirement.manager.AttachmentRecordManager;
import com.weifu.srm.requirement.manager.remote.category.CategoryManager;
import com.weifu.srm.requirement.manager.remote.user.SysDivisionManager;
import com.weifu.srm.requirement.po.*;
import com.weifu.srm.requirement.request.project.ProjectSubmitTicketReqDTO;
import com.weifu.srm.requirement.request.project.ProjectTerminationReqDTO;
import com.weifu.srm.requirement.service.biz.project.ProjectTimesBiz;
import com.weifu.srm.requirement.utils.BusinessNoUtils;
import com.weifu.srm.segment.service.IdService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectTaskManager {

    private final ProjectMapperService projectMapperService;
    private final ProjectUpdateManager projectUpdateManager;
    private final ProjectTerminationMapperService projectTerminationMapperService;
    private final KafkaTemplate<String,String> kafkaTemplate;
    private final ProjectSendMessageManager projectSendMessageManager;
    private final ProjectTimesBiz projectTimesBiz;
    private final AttachmentRecordManager attachmentRecordManager;
    private final LocaleMessage localeMessage;
    private final IdService idService;
    private final SysDivisionManager sysDivisionManager;
    private final CategoryManager categoryManager;
    private final RequirementMapperService requirementMapperService;
    private final RequirementPartsMapperService requirementPartsMapperService;


    @Transactional
    public String submitTicket(ProjectSubmitTicketReqDTO reqDTO) {
        // 保存或更新项目计划信息
        String projectNo = projectUpdateManager.addProject(reqDTO.getProjectSaveReqDTO());

        // 提交工单
        CreateTicketMQ createTicketMQ = new CreateTicketMQ();
        createTicketMQ.setBusinessNo(projectNo);
        createTicketMQ.setTicketType(TicketTypeEnum.PROJECT_INITIATION.getCode());
        createTicketMQ.setSubmitBy(reqDTO.getUserId());
        createTicketMQ.setSubmitName(reqDTO.getUserName());
        createTicketMQ.setSubmitDesc("关于" + reqDTO.getProjectSaveReqDTO().getProjectName() + "的立项审批流程");
        List<CreateTicketMQ.ApprovalProcessVar> processVars = getApprovalProcessVars(reqDTO.getSubmitUserAccount(),
                                                            reqDTO.getApproveUserAccount());
        createTicketMQ.setProcessVars(processVars);
        kafkaTemplate.send(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));

        // 更新项目计划状态到审批中
        ProjectPO one = projectMapperService.lambdaQuery().eq(ProjectPO::getProjectNo, projectNo).one();
        one.setStatus(ProjectStatusEnum.PROJECT_STATUS_REVIEW.getKey());
        BaseEntityConvert.setCommonForU(reqDTO, one, new Date());
        LambdaUpdateWrapper<ProjectPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjectPO::getProjectNo, projectNo);
        projectMapperService.update(one,wrapper);
        return projectNo;
    }

    public void changedTicket(String projectNo, String status, Long operateBy, String operateName){
        ProjectPO one = projectMapperService.lambdaQuery()
                                            .eq(ProjectPO::getProjectNo, projectNo)
                                            .one();
        if(ObjectUtil.isEmpty(one)){
            log.info("changedTicket not find project，projectNo is 【{}】", projectNo);
            return;
        }

        if(ProjectStatusEnum.PROJECT_STATUS_EDITOR.getKey().equals(one.getStatus())
                ||ProjectStatusEnum.PROJECT_STATUS_FINISH.getKey().equals(one.getStatus())){
            log.info("changedTicket project status is 【{}】", one.getStatus());
            return;
        }
        ProjectStatusInfo projectStatusInfo = new ProjectStatusInfo();
        ProjectPO projectPO = getProjectPO(status, one,projectStatusInfo);

        LambdaQueryWrapper<ProjectPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectPO::getProjectNo, projectNo);
        BaseEntityUtil.setCommonForU(projectPO, operateBy, operateName, new Date());
        projectMapperService.update(projectPO, wrapper);

        // 校验开始时间-是否已是完成
        if(projectStatusInfo.getCheckStatTime()){
            projectTimesBiz.checkAndUpdateProjectTimeStatTime(projectPO.getProjectNo(), operateBy, operateName);
        }

        // 项目已终止站内信
        if (ProjectStatusEnum.PROJECT_STATUS_END.getKey().equals(projectPO.getStatus())){
            projectSendMessageManager.boxNoticeDTO(ProjectNoticeTemplateEnum.PROJECT_TERMINATION, one.getProjectNo(),
                    one.getProjectLeaderId(), one.getProjectLeaderName(), one.getProjectName());
        }
    }

    @Data
    private class ProjectStatusInfo {

        private Boolean checkStatTime;
    }

    private ProjectPO getProjectPO(String status, ProjectPO one, ProjectStatusInfo projectStatusInfo) {
        String statusTemp = one.getStatus();
        String lightTemp = one.getProjectStatus();
        // 审批中 审批通过 - 》 执行中
        if (TicketStatusEnum.APPROVED.getCode().equals(status)
                && ProjectStatusEnum.PROJECT_STATUS_REVIEW.getKey().equals(one.getStatus())){
            statusTemp = ProjectStatusEnum.PROJECT_STATUS_IMPLEMENT.getKey();
            // 执行中为绿灯
            lightTemp = ProjectLightStatusEnum.LIGHT_GREEN.getKey();
            projectStatusInfo.setCheckStatTime(true);
        }
        // 审批中 审批驳回，撤回工单 - 》 编辑中
        if ((TicketStatusEnum.REJECTED.getCode().equals(status) || TicketStatusEnum.CANCELED.getCode().equals(status))
                && ProjectStatusEnum.PROJECT_STATUS_REVIEW.getKey().equals(one.getStatus())){
            statusTemp = ProjectStatusEnum.PROJECT_STATUS_EDITOR.getKey();
        }
        // 终止中 审批通过 - 》 已终止
        if (TicketStatusEnum.APPROVED.getCode().equals(status)
                && ProjectStatusEnum.PROJECT_STATUS_STOPPING.getKey().equals(one.getStatus())){
            statusTemp = ProjectStatusEnum.PROJECT_STATUS_END.getKey();
            // 终止的灯为灰
            lightTemp = ProjectLightStatusEnum.LIGHT_GRAY.getKey();
        }
        // 终止中 审批驳回，撤回工单 - 》 执行中
        if ((TicketStatusEnum.REJECTED.getCode().equals(status) || TicketStatusEnum.CANCELED.getCode().equals(status))
                && ProjectStatusEnum.PROJECT_STATUS_STOPPING.getKey().equals(one.getStatus())){
            statusTemp = ProjectStatusEnum.PROJECT_STATUS_IMPLEMENT.getKey();
        }
        one.setStatus(statusTemp);
        one.setProjectStatus(lightTemp);
        return one;
    }

    public void termination(ProjectTerminationReqDTO reqDTO) {
        // 参数校验
        if(ObjectUtil.isEmpty(reqDTO.getTerminateReason())
                || ObjectUtil.isEmpty(ProjectTerminationResonEnum.statOf(reqDTO.getTerminateReason()))){
            throw new BizFailException(localeMessage.getMessage("termination.reason.not.empty"));
        }
        if(ProjectTerminationResonEnum.TERMINATION_REASON_OTHER
                .equals(ProjectTerminationResonEnum.statOf(reqDTO.getTerminateReason()))
                && ObjectUtil.isEmpty(reqDTO.getOtherReason())){
            throw new BizFailException(localeMessage.getMessage("termination.other.reason.not.empty"));
        }
        // 判断项目是否可以终止
        checkeProject(reqDTO);

        // 提交工单
        submitTerminationTicket(reqDTO);

        // 更新项目计划状态到终止中
        updateProject(reqDTO);

        // 保存终止记录
        saveTerminationLog(reqDTO);
    }

    private void checkeProject(ProjectTerminationReqDTO reqDTO) {
        List<RequirementPO> requirementPOS = requirementMapperService.lambdaQuery()
                .eq(RequirementPO::getProjectNo, reqDTO.getProjectNo())
                .list();
        // 没有关联需求，允许关闭
        if(CollUtil.isEmpty(requirementPOS)){
            return;
        }
        List<String> requirementNoList = requirementPOS.stream()
                .map(RequirementPO::getRequirementNo)
                .distinct().collect(Collectors.toList());
        List<RequirementPartsPO> requirementPartsPOList = requirementPartsMapperService.lambdaQuery()
                .in(RequirementPartsPO::getRequirementNo, requirementNoList)
                .list();
        // 关联的需求没有零件采购计划，允许关闭
        if(CollUtil.isEmpty(requirementPartsPOList)){
            return;
        }
        for (RequirementPartsPO po : requirementPartsPOList){
            // 项目关联未完成、未关闭的需求，不允许提交终止项目申请
            if(ObjectUtil.equals(po.getStatus(), RequirementPartsStatusEnum.COMPLETED.getCode())
                    || ObjectUtil.equals(po.getStatus(), RequirementPartsStatusEnum.CLOSED.getCode())){
                continue;
            }
            throw new BizFailException(localeMessage.getMessage("project.not.have.requirement.not.close"));
        }
    }

    private void saveTerminationLog(ProjectTerminationReqDTO reqDTO) {
        Date date = new Date();
        ProjectTerminationPO projectTerminationPO = new ProjectTerminationPO();
        projectTerminationPO.setTerminationNo(BusinessNoUtils.generateProjectNo(idService,
                RequirementBizConstants.TERMINATION_NO_PREFIX));
        projectTerminationPO.setProjectNo(reqDTO.getProjectNo());
        projectTerminationPO.setTerminationType(reqDTO.getTerminateReason());
        projectTerminationPO.setOtherReason(reqDTO.getOtherReason());
        BaseEntityConvert.setCommon(reqDTO, projectTerminationPO, date);
        projectTerminationMapperService.save(projectTerminationPO);

        if(ObjectUtil.isEmpty(reqDTO.getFileName())){
            return;
        }
        AttachmentRecordPO attachmentRecordPO = new AttachmentRecordPO();
        attachmentRecordPO.setFileUrl(reqDTO.getFileUrl());
        attachmentRecordPO.setFileName(reqDTO.getFileName());
        attachmentRecordPO.setFileOriginalName(reqDTO.getFileOriginalName());
        attachmentRecordPO.setBusinessType(AttachmentRecordEnum.TERMINATION.getKey());
        attachmentRecordPO.setBusinessNo(projectTerminationPO.getTerminationNo());
        BaseEntityConvert.setCommon(reqDTO, attachmentRecordPO, date);
        attachmentRecordPO.setIsDelete(YesOrNoEnum.NO.getCode());
        attachmentRecordManager.save(attachmentRecordPO);
    }

    private void updateProject(ProjectTerminationReqDTO reqDTO) {
        Date date = new Date();
        ProjectPO projectPO = projectMapperService.lambdaQuery().eq(ProjectPO::getProjectNo, reqDTO.getProjectNo()).one();
        projectPO.setStatus(ProjectStatusEnum.PROJECT_STATUS_STOPPING.getKey());
        LambdaQueryWrapper<ProjectPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectPO::getProjectNo, reqDTO.getProjectNo());
        BaseEntityConvert.setCommonForU(reqDTO, projectPO, date);
        projectMapperService.update(projectPO, wrapper);
    }

    private void submitTerminationTicket(ProjectTerminationReqDTO reqDTO) {

        ProjectPO one = projectMapperService.lambdaQuery().eq(ProjectPO::getProjectNo, reqDTO.getProjectNo()).one();
        CreateTicketMQ createTicketMQ = new CreateTicketMQ();
        createTicketMQ.setBusinessNo(reqDTO.getProjectNo());
        createTicketMQ.setTicketType(TicketTypeEnum.PROJECT_TERMINATION.getCode());
        createTicketMQ.setSubmitBy(reqDTO.getUserId());
        createTicketMQ.setSubmitName(reqDTO.getUserName());
        createTicketMQ.setSubmitDesc("关于" + one.getProjectName() + "项目终止审批流程");
        String desc = ProjectTerminationResonEnum.statOf(reqDTO.getTerminateReason()).getValue() ;
        if (ProjectTerminationResonEnum.TERMINATION_REASON_OTHER.getKey().equals(reqDTO.getTerminateReason())){
            desc = desc + "-" + reqDTO.getOtherReason();
        }
        createTicketMQ.setSubmitRemark(desc);
        List<CreateTicketMQ.ApprovalProcessVar> processVars = getApprovalProcessVars(reqDTO.getSubmitUserAccount(),
                                                            reqDTO.getApproveUserAccount());
        createTicketMQ.setProcessVars(processVars);
        kafkaTemplate.send(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
    }

    private static List<CreateTicketMQ.ApprovalProcessVar> getApprovalProcessVars(String reqDTO, String reqDTO1) {
        List<CreateTicketMQ.ApprovalProcessVar> processVars = new ArrayList<>();
        CreateTicketMQ.ApprovalProcessVar sub = new CreateTicketMQ.ApprovalProcessVar();
        sub.setKey("SUBMIT_USER_ACCOUNT");
        sub.setV(reqDTO);
        processVars.add(sub);
        CreateTicketMQ.ApprovalProcessVar approval = new CreateTicketMQ.ApprovalProcessVar();
        approval.setKey("APPROVE_USER_ACCOUNT");
        approval.setV(reqDTO1);
        processVars.add(approval);
        return processVars;
    }

    public Boolean findIsTerminationByProjectNo(String projectNo, Long optId){
        ProjectPO one = projectMapperService.lambdaQuery().eq(ProjectPO::getProjectNo, projectNo).one();
        if(ObjectUtil.isEmpty(one)){
            return false;
        }
        if(!ObjectUtil.equals(one.getStatus(), ProjectStatusEnum.PROJECT_STATUS_IMPLEMENT.getKey())){
            return false;
        }

        // 品类项目
        if(ObjectUtil.isNotEmpty(one.getSubmitByThreeCategory())){
            return categoryManager.findIsTerminationByProjectNo(one.getSubmitByThreeCategory(),optId);
        }
        // bp项目
        return sysDivisionManager.findIsTerminationByProjectNo(one.getSubsidiaryCode(), optId);
    }

}
