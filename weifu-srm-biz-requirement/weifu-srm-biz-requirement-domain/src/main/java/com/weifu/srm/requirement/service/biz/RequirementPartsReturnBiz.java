package com.weifu.srm.requirement.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.requirement.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.constants.ErrorCodeConstants;
import com.weifu.srm.requirement.enums.AttachmentRecordEnum;
import com.weifu.srm.requirement.enums.RequirementPartsStatusEnum;
import com.weifu.srm.requirement.manager.RequirementManager;
import com.weifu.srm.requirement.manager.RequirementPartsManager;
import com.weifu.srm.requirement.po.AttachmentRecordPO;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import com.weifu.srm.requirement.request.RequirementPartsReturnReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/8/19 18:19
 * @Description
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequirementPartsReturnBiz {

    private final RequirementPartsMapperService requirementPartsMapperService;
    private final RequirementPartsManager requirementPartsManager;
    private final TransactionTemplate transactionTemplate;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final RequirementManager requirementManager;
    private final RequirementPartsPurchasePlanMapperService requirementPartsPurchasePlanMapperService;
    private final LocaleMessage localeMessage;

    public void returnByPartsNo(RequirementPartsReturnReqDTO req) {
        List<String> partNos = req.getRequirementPartsNo();
        log.info("退回零件需求开始，partNos={}，req={}", partNos, JacksonUtil.bean2Json(req));

        List<RequirementPartsPO> oldPartList = requirementPartsMapperService.listByNo(partNos);

        Date current = new Date();
        Set<String> requirementNos = new HashSet<>();
        List<RequirementPartsPO> updateParts = new ArrayList<>();
        String finalNextStatus = null;
        for (RequirementPartsPO oldPart:oldPartList) {
            String nextStatus = calcNextStatus(oldPart);
            if (nextStatus == null) {
                throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.AT_LEAST_ONE_REQ_CANNOT_RETURN));
            }
            if (finalNextStatus == null) {
                finalNextStatus = nextStatus;
            }
            if (!StringUtils.equals(finalNextStatus, nextStatus)) {
                throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.REQ_STATUS_CANNOT_RETURN_TOGETHER));
            }

            RequirementPartsPO updatePart = getUpdatePart(req, oldPart, finalNextStatus, current);
            updateParts.add(updatePart);
            requirementNos.add(oldPart.getRequirementNo());
        }

        List<AttachmentRecordPO> attachmentRecords = new ArrayList<>();
        oldPartList.forEach(oldPart -> {
            List<AttachmentRecordPO> subAttachmentRecords = getAttachmentRecord(req, oldPart.getRequirementPartsNo(), current);
            attachmentRecords.addAll(subAttachmentRecords);
        });

        String finalNextStatus1 = finalNextStatus;
        transactionTemplate.execute(p -> {
            // 更新零件需求状态
            requirementPartsMapperService.updateBatchById(updateParts);

            // BP退回时，需要更新总需求状态为“部分退回”
            if (RequirementPartsStatusEnum.BP_RETURN.equalsCode(finalNextStatus1)) {
                requirementManager.toPartialReturn(new ArrayList<>(requirementNos), req.getOperationBy(), req.getOperationByName(), current);
            }
            // 组长退回时，将负责的CPE组长清空
            if (RequirementPartsStatusEnum.MASTER_RETURN.equalsCode(finalNextStatus1)) {
                requirementPartsMapperService.lambdaUpdate()
                        .in(RequirementPartsPO::getRequirementPartsNo, partNos)
                        .set(RequirementPartsPO::getCpeMasterId, null)
                        .set(RequirementPartsPO::getCpeMasterName, null)
                        .update();
            }
            // CPE退回时，将负责的CPE、计划编号清空、删除零件需求关联的零件采购计划
            if (RequirementPartsStatusEnum.CPE_RETURN.equalsCode(finalNextStatus1)) {
                requirementPartsMapperService.lambdaUpdate()
                        .in(RequirementPartsPO::getRequirementPartsNo, partNos)
                        .set(RequirementPartsPO::getCpeId, null)
                        .set(RequirementPartsPO::getCpeName, null)
                        .set(RequirementPartsPO::getPlanNo, null)
                        .update();

                LambdaQueryWrapper delPlanWrapper = Wrappers.lambdaQuery(RequirementPartsPurchasePlanPO.class)
                        .in(RequirementPartsPurchasePlanPO::getRequirementPartsNo, partNos);
                requirementPartsPurchasePlanMapperService.remove(delPlanWrapper);
            }

            // 保存附件
            attachmentRecordMapperService.saveBatch(attachmentRecords);

            // 发送零件需求状态变化MQ
            oldPartList.forEach(oldPart -> {
                requirementPartsManager.sendStatusChangedMq(oldPart, finalNextStatus1, current,
                        req.getOperationBy(), req.getOperationByName());
            });
            return null;
        });
        log.info("退回零件需求完成，partNos={}", partNos);
    }

    private String calcNextStatus(RequirementPartsPO oldPart) {
        if (RequirementPartsStatusEnum.WAITING_MASTER_DISTRIBUTE.equalsCode(oldPart.getStatus())
                || RequirementPartsStatusEnum.CPE_RETURN.equalsCode(oldPart.getStatus())) {
            return RequirementPartsStatusEnum.MASTER_RETURN.getCode();
        }
        if (RequirementPartsStatusEnum.WAITING_BP_DISTRIBUTE.equalsCode(oldPart.getStatus())
                || RequirementPartsStatusEnum.MASTER_RETURN.equalsCode(oldPart.getStatus())) {
            return RequirementPartsStatusEnum.BP_RETURN.getCode();
        }
        if (RequirementPartsStatusEnum.PENDING.equalsCode(oldPart.getStatus())) {
            return RequirementPartsStatusEnum.CPE_RETURN.getCode();
        }
        return null;
    }

    private RequirementPartsPO getUpdatePart(RequirementPartsReturnReqDTO req, RequirementPartsPO part,
                                             String finalNextStatus, Date current) {
        RequirementPartsPO updatePart = new RequirementPartsPO();
        updatePart.setId(part.getId());
        updatePart.setReturnReason(req.getReturnReason());
        updatePart.setUpdateBy(req.getOperationBy());
        updatePart.setUpdateName(req.getOperationByName());
        updatePart.setUpdateTime(current);
        updatePart.setStatus(finalNextStatus);
        if (RequirementPartsStatusEnum.BP_RETURN.equalsCode(finalNextStatus)) {
            updatePart.setCurrentOperationBy(part.getCreateBy());
            updatePart.setCurrentOperationByName(part.getCreateName());
        } else if (RequirementPartsStatusEnum.MASTER_RETURN.equalsCode(finalNextStatus)) {
            updatePart.setCurrentOperationBy(part.getBpId());
            updatePart.setCurrentOperationByName(part.getBpName());
        } else if (RequirementPartsStatusEnum.CPE_RETURN.equalsCode(finalNextStatus)) {
            updatePart.setCurrentOperationBy(part.getCpeMasterId());
            updatePart.setCurrentOperationByName(part.getCpeMasterName());
        }
        return updatePart;
    }

    private List<AttachmentRecordPO> getAttachmentRecord(RequirementPartsReturnReqDTO req, String partNo, Date current) {
        List<AttachmentRecordPO> list = new ArrayList<>();
        Optional.ofNullable(req.getAttachments()).ifPresent(r->r.forEach(k->{
            AttachmentRecordPO attachmentRecordPO = new AttachmentRecordPO();
            attachmentRecordPO.setBusinessNo(partNo);
            attachmentRecordPO.setFileName(k.getFileName());
            attachmentRecordPO.setFileUrl(k.getFileUrl());
            attachmentRecordPO.setFileOriginalName(k.getFileOriginalName());
            BaseEntityUtil.setCommon(attachmentRecordPO, req.getOperationBy(), req.getOperationByName(), current);
            attachmentRecordPO.setBusinessType(AttachmentRecordEnum.REQUIREMENT_PARTS_RETURN_ATTACHMENT.getKey());
            list.add(attachmentRecordPO);
        }));
        return list;
    }

}