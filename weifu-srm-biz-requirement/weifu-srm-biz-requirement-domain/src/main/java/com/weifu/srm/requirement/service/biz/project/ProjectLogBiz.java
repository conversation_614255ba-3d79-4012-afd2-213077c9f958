package com.weifu.srm.requirement.service.biz.project;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.requirement.atomicservice.ProjectLogMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.convert.ProjectLogConvert;
import com.weifu.srm.requirement.enums.project.ProjectLogTypeEnum;
import com.weifu.srm.requirement.enums.project.ProjectModifyReasonEnum;
import com.weifu.srm.requirement.manager.remote.user.SysUserManager;
import com.weifu.srm.requirement.po.ProjectLogPO;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import com.weifu.srm.requirement.request.project.ProjectUpdateRecordListReqDTO;
import com.weifu.srm.requirement.response.project.ProjectLogResDTO;
import com.weifu.srm.requirement.utils.ExportUtils;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class ProjectLogBiz {

    private final ProjectLogMapperService projectLogMapperService;
    private final RequirementPartsPurchasePlanMapperService requirementPartsPurchasePlanMapperService;
    private final ProjectLogConvert projectLogConvert;
    private final List<String> headerList = Arrays.asList("采购计划修改日志", "序号","修改对象"
            ,"编号", "修改人","修改时间", "修改原因","备注");
    public void exportRecordList(HttpServletResponse response, ProjectUpdateRecordListReqDTO reqDTO){

        ArrayList<String> noList = CollUtil.newArrayList(reqDTO.getProjectNo());
        List<RequirementPartsPurchasePlanPO> purchasePlanPOS = requirementPartsPurchasePlanMapperService.lambdaQuery()
                .eq(RequirementPartsPurchasePlanPO::getProjectNo, reqDTO.getProjectNo())
                .list();
        if(CollUtil.isNotEmpty(purchasePlanPOS)){
            noList.addAll(purchasePlanPOS.stream()
                    .map(RequirementPartsPurchasePlanPO::getPlanNo).collect(Collectors.toList()));
        }

        List<ProjectLogPO> list = projectLogMapperService.lambdaQuery()
                .ge(ObjectUtil.isNotEmpty(reqDTO.getStartDate()), ProjectLogPO::getModifyDate, reqDTO.getStartDate())
                .le(ObjectUtil.isNotEmpty(reqDTO.getEndDate()), ProjectLogPO::getModifyDate, reqDTO.getEndDate())
                .in(ProjectLogPO::getNo, noList)
                .eq(ProjectLogPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .orderByDesc(ProjectLogPO::getCreateTime)
                .list();

        List<ProjectLogResDTO> respDTOList = projectLogConvert.toResult(list);
        try(OutputStream outputStream = response.getOutputStream();
            XSSFWorkbook workbook = ExportUtils.createWorkbook(headerList)) {
            writeSheetPurchasePlanUpdate(workbook, respDTOList);
            String downloadFileName = headerList.get(0)+"导出_" + DateUtil.formatDate(new Date()) + ".xlsx";
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + downloadFileName);
            // 将Excel文件写入响应输出流
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new BizFailException();
        }
    }



    private void writeSheetPurchasePlanUpdate(XSSFWorkbook workbook,  List<ProjectLogResDTO> list) {
        XSSFSheet sheet = workbook.getSheet(headerList.get(0));

        if (CollectionUtils.isNotEmpty(list)) {
            int indexNum = 1;
            for (ProjectLogResDTO po : list) {
                XSSFRow row = sheet.createRow(indexNum);
                int index = 0;
                row.createCell(index++).setCellValue(String.valueOf(indexNum));
                indexNum++;
                ProjectLogTypeEnum projectLogTypeEnum = ProjectLogTypeEnum.statOf(po.getType());
                row.createCell(index++).setCellValue(projectLogTypeEnum.getValue());
                row.createCell(index++).setCellValue(po.getNo());
                row.createCell(index++).setCellValue(po.getModifyName());
                row.createCell(index++).setCellValue(po.getModifyDate());
                ProjectModifyReasonEnum projectModifyReasonEnum = ProjectModifyReasonEnum.statOf(po.getModifyReason());
                String reason = projectModifyReasonEnum.getValue();
                if(ObjectUtil.equals(ProjectModifyReasonEnum.PROJECT_MODIFY_REASON_OTHER.getKey(), projectModifyReasonEnum.getKey())){
                    reason = reason + "-" + po.getOtherReason();
                }
                row.createCell(index++).setCellValue(reason);
                row.createCell(index).setCellValue(po.getRemark());
            }
        }
    }

}
