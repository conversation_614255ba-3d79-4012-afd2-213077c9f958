package com.weifu.srm.requirement.service.biz;

import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.enums.TodoClsEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.communication.mq.CreateTodoMQ;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.requirement.atomicservice.RequirementMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.enums.RequirementPartsStatusEnum;
import com.weifu.srm.requirement.enums.requirement.RequirementNoticeTemplateEnum;
import com.weifu.srm.requirement.enums.requirement.RequirementToDoTemplateEnum;
import com.weifu.srm.requirement.manager.project.ProjectTimesManager;
import com.weifu.srm.requirement.manager.remote.base.TodoListManager;
import com.weifu.srm.requirement.manager.remote.user.SysUserManager;
import com.weifu.srm.requirement.mq.RequirementPartStatusChangedMq;
import com.weifu.srm.requirement.po.RequirementPO;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import com.weifu.srm.requirement.utils.TodoBuUtils;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Hongyi
 * @Date 2024/10/25 15:33
 * @Description 处理零件需求状态变化
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HandleRequirementPartStatusChangedBiz {

    private static final String REQUIREMENT_DESC = "${requirement_desc}";
    private static final String MATERIAL_CODE = "${material_code}";
    private static final String RETURN_REASON = "${return_reason}";
    private static final String REQUIREMENT_PARTS_NO = "${requirementPartsNo}";
    private static final String MATERIAL_CODE_KEY = "${materialCode}";
    private static final String OPERATE_BY = "${operateBy}";

    private final ProjectTimesManager projectTimesManager;
    private final RequirementMapperService requirementMapperService;
    private final RequirementPartsMapperService requirementPartsMapperService;
    private final TodoListManager todoListManager;
    private final SysUserManager sysUserManager;
    private final MqManager mqManager;

    public void execute(RequirementPartStatusChangedMq mq) {
        String partNo = mq.getRequirementPartsNo();

        // 更新项目采购计划节点上的完成时间
        if (StringUtils.equalsAny(mq.getStatus(), RequirementPartsStatusEnum.PENDING.getCode(),
                RequirementPartsStatusEnum.CLOSED.getCode(),
                RequirementPartsStatusEnum.COMPLETED.getCode(),
                RequirementPartsStatusEnum.RECREATED.getCode())) {
            projectTimesManager.updateStageTimeWithPurchasePlan(partNo, mq.getOperateBy(), mq.getOperateName());
        }

        RequirementPartsPO part = requirementPartsMapperService.getByNo(partNo);

        // 关闭创建待办、发送站内信
        // 待BP分发（总需求审核通过、BP转派）
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.WAITING_BP_DISTRIBUTE.getCode())) {
            if (YesOrNoEnum.YES.equalsCode(mq.getIsTransfer())) {
                // 尝试关闭【零件需求处理_系统分发】待办
                tryCloseBpDistributeTodoTask(mq, part);
                // 关闭【零件需求处理_BP转派】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_BP_TRANSFER.getCode());
                // 发送站内信给被转派对象
                sendSiteMsgForTransfer(mq, part);
                // 发送【零件需求处理_BP转派】待办任务（被BP转派后）
                sendTodoTaskAfterBpTransfer(mq, part);
            } else {
                // 发送发送【零件需求处理_系统分发】待办任务，因为这个是总需求维度的，所以在处理采购寻源审批结果的流程里发送的
            }
            return;
        }
        // 待组长分发（BP分发）
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.WAITING_MASTER_DISTRIBUTE.getCode())) {
            // 关闭【零件需求退回处理】待办
            todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_RETURN.getCode());
            // 关闭【零件需求处理_BP转派】待办
            todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_BP_TRANSFER.getCode());
            // 尝试关闭【零件需求处理_系统分发】待办
            tryCloseBpDistributeTodoTask(mq, part);

            // 发送待办任务（被BP分发后）
            sendTodoTaskAfterBpDistribute(part);
            return;
        }
        // 待执行（CPE组长分发、CPE转派）
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.PENDING.getCode())) {
            if (YesOrNoEnum.YES.equalsCode(mq.getIsTransfer())) {
                // 关闭【零件需求处理_品类组长分发】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_MASTER_DISTRIBUTION.getCode());
                // 关闭【零件需求处理_CPE转派】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_TRANSFER.getCode());
                // 发送站内信给被转派对象
                sendSiteMsgForTransfer(mq, part);
                // 发送【零件需求处理_CPE转派】待办任务（被CPE转派后）
                sendTodoTaskAfterCpeTransfer(mq, part);
            } else {
                // 关闭【零件需求退回处理】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_RETURN.getCode());

                // 关闭【零件需求处理_BP分发】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_BP_DISTRIBUTION.getCode());

                // 发送待办任务（被CPE组长分发后）
                sendTodoTaskAfterMasterDistribute(part);
            }
            return;
        }
        // CPE已退回（CPE退回）
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.CPE_RETURN.getCode())) {
            // 关闭【零件需求处理_品类组长分发】待办
            todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_MASTER_DISTRIBUTION.getCode());
            // 关闭【零件需求处理_CPE转派】待办
            todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_TRANSFER.getCode());
            // 发送新的退回待办
            sendReturnTodoTask(part, part.getCpeMasterId());
            return;
        }
        executeOther(mq, part, partNo);
    }

    private void executeOther(RequirementPartStatusChangedMq mq, RequirementPartsPO part, String partNo) {
        // 组长已退回（CPE组长退回、BP转派）
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.MASTER_RETURN.getCode())) {
            if (YesOrNoEnum.YES.equalsCode(mq.getIsTransfer())) {
                // 发送站内信给被转派对象
                sendSiteMsgForTransfer(mq, part);
                // 发送【零件需求处理_BP转派】待办任务（被BP转派后）
                sendTodoTaskAfterBpTransfer(mq, part);
            } else {
                // 关闭【零件需求退回处理】待办
                todoListManager.closeTodo(partNo,TodoClsEnum.PART_REQUIREMENT_RETURN.getCode());
                // 关闭【零件需求处理_BP分发】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_BP_DISTRIBUTION.getCode());
                // 发送新的退回待办
                sendReturnTodoTask(part, part.getBpId());
            }
            return;
        }
        // BP退回（BP退回）
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.BP_RETURN.getCode())) {
            // 关闭【零件需求退回处理】待办
            todoListManager.closeTodo(partNo,TodoClsEnum.PART_REQUIREMENT_RETURN.getCode());
            // 关闭【零件需求处理_BP转派】待办
            todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_BP_TRANSFER.getCode());
            // 尝试关闭【零件需求处理_系统分发】待办
            tryCloseBpDistributeTodoTask(mq, part);
            // 发送新的退回待办
            sendReturnTodoTask(part, part.getCreateBy());
            return;
        }
        // 已关闭（需求申请人操作关闭）
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.CLOSED.getCode())) {
            // 关闭【零件需求退回处理】待办
            todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_RETURN.getCode());

            // 这里本应该还要发送站内信，但因为关闭零件需求时是批量处理，且收信人还要去重，所以目前在关闭需求的接口里发送的

            // 关闭【零件需求处理_品类组长分发】待办
            todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_MASTER_DISTRIBUTION.getCode());
            // 关闭【零件需求处理_CPE转派】待办
            todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_TRANSFER.getCode());
            return;
        }
        // 已重新发起（需求申请人操作重新发起）
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.RECREATED.getCode())) {
            // 关闭【零件需求退回处理】待办
            todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_RETURN.getCode());
            return;
        }
        // 已完成（标记为借用件、零件采购计划完成、样件订单生效）
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.COMPLETED.getCode())) {
            if (YesOrNoEnum.YES.equalsCode(part.getIsBorrowParts())) {
                // 关闭【零件需求退回处理】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_RETURN.getCode());
                // 关闭【零件需求处理_BP转派】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_BP_TRANSFER.getCode());
                // 关闭【零件需求处理_BP分发】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_BP_DISTRIBUTION.getCode());
                // 关闭【零件需求处理_品类组长分发】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_MASTER_DISTRIBUTION.getCode());
                // 关闭【零件需求处理_CPE转派】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_TRANSFER.getCode());
                // 尝试关闭【零件需求处理_系统分发】待办
                tryCloseBpDistributeTodoTask(mq, part);
                // 发送【借用件标记通知】待办任务（被标记为借用件后）
                sendTodoTaskAfterMark(mq, part);
            } else {
                // 关闭【零件需求处理_品类组长分发】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_MASTER_DISTRIBUTION.getCode());
                // 关闭【零件需求处理_CPE转派】待办
                todoListManager.closeTodo(partNo, TodoClsEnum.PART_REQUIREMENT_CPE_TRANSFER.getCode());
            }
            return;
        }
    }

    /**
     * 尝试关闭【零件需求处理_系统分发】待办
     * 同一总需求下作为操作人作为采购BP负责的零件需求，如果不存在【待BP分发】状态的零件需求时，就可以去关闭【零件需求处理_系统分发】待办
     * 因为可以转派，所以理论上存在着会触发关闭一个不存在的待办的情况
     */
    private void tryCloseBpDistributeTodoTask(RequirementPartStatusChangedMq mq, RequirementPartsPO changePart) {
        List<RequirementPartsPO> parts = requirementPartsMapperService.listByRequirementNo(changePart.getRequirementNo());
        // 同一总需求下作为操作人作为采购BP负责的零件需求，如果不存在【待BP分发】状态的零件需求时，就可以去关闭【零件需求处理_系统分发】待办

        boolean canClose = true;
        for (RequirementPartsPO part:parts) {
            if (!mq.getOperateBy().equals(part.getBpId())) {
                continue;
            }

            if (RequirementPartsStatusEnum.WAITING_BP_DISTRIBUTE.equalsCode(part.getStatus())) {
                canClose = false;
                break;
            }
        }

        // 关闭指定人员指定业务单下【零件需求处理_系统分发】待办
        if (canClose) {
            todoListManager.closeTodo(changePart.getRequirementNo(), TodoClsEnum.PART_REQUIREMENT_SYSTEM_DISTRIBUTION.getCode(), mq.getOperateBy());
        }
    }

    /**
     * 发送站内信给被转派对象
     */
    private void sendSiteMsgForTransfer(RequirementPartStatusChangedMq mq, RequirementPartsPO part) {
        List<CreateSiteMessageMQ> list = new ArrayList<>();
        String content = RequirementNoticeTemplateEnum.REQUIREMENT_PARTS_TRANSFER_CONTENT.getContent();
        content = content.replace("${requirement_parts_no}", part.getRequirementPartsNo());
        CreateSiteMessageMQ message = new CreateSiteMessageMQ();
        message.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        message.setBusinessNo(part.getRequirementPartsNo());
        message.setBusinessType(MessageClsEnum.REASSIGNMENT.getCode());
        message.setUserId(mq.getTransferTo());
        message.setUserName(mq.getTransferToName());
        message.setIconType(IconTypeEnum.GENERAL.getCode());
        message.setTitle(RequirementNoticeTemplateEnum.REQUIREMENT_PARTS_TRANSFER_CONTENT.getTitle());
        message.setContent(content);
        list.add(message);
        mqManager.sendTopic(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(list));
    }

    /**
     * 发送【零件需求处理_BP转派】待办任务（被BP转派后）
     */
    private void sendTodoTaskAfterBpTransfer(RequirementPartStatusChangedMq mq, RequirementPartsPO part) {
        BaseSysUserRespDTO user = sysUserManager.getUserDetailById(mq.getOperateBy());
        String content = RequirementToDoTemplateEnum.PART_REQUIREMENT_BP_TRANSFER.getContent();
        content = content.replace(REQUIREMENT_PARTS_NO, part.getRequirementPartsNo())
                .replace(MATERIAL_CODE_KEY, TodoBuUtils.genMaterialCode(part.getMaterialCode()))
                .replace(OPERATE_BY, TodoBuUtils.genUserName(user));
        sendTodoTask(TodoClsEnum.PART_REQUIREMENT_BP_TRANSFER.getCode(), part.getRequirementPartsNo(), mq.getTransferTo(), content);
    }

    /**
     * 发送【零件需求处理_CPE转派】待办任务（被CPE转派后）
     */
    private void sendTodoTaskAfterCpeTransfer(RequirementPartStatusChangedMq mq, RequirementPartsPO part) {
        BaseSysUserRespDTO user = sysUserManager.getUserDetailById(mq.getOperateBy());
        String content = RequirementToDoTemplateEnum.PART_REQUIREMENT_CPE_TRANSFER.getContent();
        content = content.replace(REQUIREMENT_PARTS_NO, part.getRequirementPartsNo())
                .replace(MATERIAL_CODE_KEY, TodoBuUtils.genMaterialCode(part.getMaterialCode()))
                .replace(OPERATE_BY, TodoBuUtils.genUserName(user));
        sendTodoTask(TodoClsEnum.PART_REQUIREMENT_CPE_TRANSFER.getCode(), part.getRequirementPartsNo(), mq.getTransferTo(), content);
    }

    /**
     * 发送待办任务（被CPE组长分发后）
     */
    private void sendTodoTaskAfterMasterDistribute(RequirementPartsPO part) {
        BaseSysUserRespDTO user = sysUserManager.getUserDetailById(part.getCpeMasterId());
        String content = RequirementToDoTemplateEnum.PART_REQUIREMENT_CPE_MASTER_DISTRIBUTION.getContent();
        content = content.replace(REQUIREMENT_PARTS_NO, part.getRequirementPartsNo())
                .replace(MATERIAL_CODE_KEY, TodoBuUtils.genMaterialCode(part.getMaterialCode()))
                .replace(OPERATE_BY, TodoBuUtils.genUserName(user));
        sendTodoTask(TodoClsEnum.PART_REQUIREMENT_CPE_MASTER_DISTRIBUTION.getCode(), part.getRequirementPartsNo(), part.getCpeId(), content);
    }

    /**
     * 发送待办任务（被BP分发后）
     */
    private void sendTodoTaskAfterBpDistribute(RequirementPartsPO part) {
        BaseSysUserRespDTO user = sysUserManager.getUserDetailById(part.getBpId());
        String content = RequirementToDoTemplateEnum.PART_REQUIREMENT_BP_DISTRIBUTION.getContent();
        content = content.replace(REQUIREMENT_PARTS_NO, part.getRequirementPartsNo())
                .replace(MATERIAL_CODE_KEY, TodoBuUtils.genMaterialCode(part.getMaterialCode()))
                .replace(OPERATE_BY, TodoBuUtils.genUserName(user));
        sendTodoTask(TodoClsEnum.PART_REQUIREMENT_BP_DISTRIBUTION.getCode(), part.getRequirementPartsNo(), part.getCpeMasterId(), content);
    }

    /**
     * 发送【借用件标记通知】待办任务（被标记为借用件后）
     */
    private void sendTodoTaskAfterMark(RequirementPartStatusChangedMq mq, RequirementPartsPO part) {
        BaseSysUserRespDTO user = sysUserManager.getUserDetailById(mq.getOperateBy());
        RequirementPO requirement = requirementMapperService.getByNo(part.getRequirementNo());

        String content = RequirementToDoTemplateEnum.PART_REQUIREMENT_MARK_BORROWED.getContent();
        content = content.replace(OPERATE_BY, TodoBuUtils.genUserName(user))
                .replace("${requirementNo}", part.getRequirementNo())
                .replace(REQUIREMENT_PARTS_NO, part.getRequirementPartsNo())
                .replace(MATERIAL_CODE_KEY, TodoBuUtils.genMaterialCode(part.getMaterialCode()));
        sendTodoTask(TodoClsEnum.PART_REQUIREMENT_MARK_BORROWED.getCode(), part.getRequirementPartsNo(), requirement.getOperationBy(), content);
    }

    /**
     * 发送退回待办任务
     */
    private void sendReturnTodoTask(RequirementPartsPO part, Long todoUserId) {
        RequirementPO requirement = requirementMapperService.getByNo(part.getRequirementNo());
        String content = RequirementToDoTemplateEnum.PART_REQUIREMENT_RETURN_CONTENT.getContent();
        content = content.replace(REQUIREMENT_DESC, requirement.getRequirementDesc())
                .replace(MATERIAL_CODE, StringUtils.defaultString(part.getMaterialCode()))
                .replace(RETURN_REASON, part.getReturnReason());
        sendTodoTask(TodoClsEnum.PART_REQUIREMENT_RETURN.getCode(), part.getRequirementPartsNo(), todoUserId, content);
    }

    /**
     * 发送待办任务
     */
    private void sendTodoTask(String businessType, String businessNo, Long todoUserId, String content) {
        CreateTodoMQ createTodoReq = new CreateTodoMQ();
        createTodoReq.setBusinessType(businessType);
        createTodoReq.setBusinessNo(businessNo);
        createTodoReq.setUserId(todoUserId);
        createTodoReq.setIconType(IconTypeEnum.GENERAL.getCode());
        createTodoReq.setContent(content);
        mqManager.sendTopic(CommunicationTopicConstants.CREATE_TODO, JacksonUtil.bean2Json(createTodoReq));
    }

}
