package com.weifu.srm.requirement.manager.remote.category;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.request.CategoryCEPCategoryReqDTO;
import com.weifu.srm.masterdata.request.CategoryQueryDTO;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.masterdata.response.CategoryResultDTO;
import com.weifu.srm.masterdata.response.CategoryWithAllLevelDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryManager {

    private final CategoryApi categoryApi;
    private final LocaleMessage localeMessage;

    private static final String ERROR_KEY_FOR_CATEGORY= "request.category.server.fail";

    public List<CategoryResultDTO> getCategoryList(List<String> categoryCodes) {
        ApiResponse<List<CategoryResultDTO>> result = categoryApi.batchDetail(categoryCodes);
        if (Boolean.FALSE.equals(result.getSucc())) {
            log.error("***** request masterdata service to get category error={} ",result);
            throw new BizFailException(localeMessage.getMessage(ERROR_KEY_FOR_CATEGORY,new String[]{result.getMsg()}));
        }
        return result.getData();
    }

    public List<CategoryResultDTO> queryCategoryTree(){
        ApiResponse<List<CategoryResultDTO>> result = categoryApi.queryAll(new CategoryQueryDTO());
        if (Boolean.FALSE.equals(result.getSucc())) {
            log.error("***** request masterdata service to get category tree error={} ",result);
            throw new BizFailException(localeMessage.getMessage(ERROR_KEY_FOR_CATEGORY,new String[]{result.getMsg()}));
        }
        return result.getData();
    }

    public List<CategoryEngineerResultDTO> queryCategoryEngineerByCategoryCodes(List<String> categoryCodes){
        ApiResponse<List<CategoryEngineerResultDTO>> result = categoryApi.queryCategoryEngineerByCategoryCodes(categoryCodes);
        if (Boolean.FALSE.equals(result.getSucc())) {
            log.error("***** request masterdata service to get category engineer error={} ",result);
            throw new BizFailException(localeMessage.getMessage(ERROR_KEY_FOR_CATEGORY,new String[]{result.getMsg()}));
        }
        return result.getData();
    }

    public List<CategoryEngineerResultDTO> queryCategoryEngineerByUserId(Long userId) {
        ApiResponse<List<CategoryEngineerResultDTO>> result = categoryApi.queryCategoryEngineerByUserId(userId);
        if (Boolean.FALSE.equals(result.getSucc())) {
            log.error("***** request masterdata service to get category engineer by userId error={} ",result);
            throw new BizFailException(localeMessage.getMessage(ERROR_KEY_FOR_CATEGORY,new String[]{result.getMsg()}));
        }
        return result.getData();
    }

    public List<CategoryEngineerResultDTO> queryCategoryEngineerByCategoryId(Long categoryId) {
        ApiResponse<List<CategoryEngineerResultDTO>> result = categoryApi.list(categoryId,null);
        if (Boolean.FALSE.equals(result.getSucc())) {
            log.error("***** request masterdata service to get category engineer by categoryId error={} ",result);
            throw new BizFailException(localeMessage.getMessage(ERROR_KEY_FOR_CATEGORY,new String[]{result.getMsg()}));
        }
        return result.getData();
    }

    public Boolean findIsTerminationByProjectNo(Long categoryId, Long optId) {
        List<CategoryEngineerResultDTO> categoryEngineerList = queryCategoryEngineerByCategoryId(categoryId);
        if(CollUtil.isEmpty(categoryEngineerList)){
            return false;
        }
        for (CategoryEngineerResultDTO categoryEngineerDTO : categoryEngineerList){
            if(ObjectUtil.equals(categoryEngineerDTO.getRoleId(), CategoryRoleEnum.CPE_MASTER.getCode())
                    && ObjectUtil.equals(categoryEngineerDTO.getUserId(), optId)){
                return true;
            }
            if(ObjectUtil.equals(categoryEngineerDTO.getRoleId(), CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.getCode())
                    && ObjectUtil.equals(categoryEngineerDTO.getUserId(), optId)){
                return true;
            }
        }
        return false;
    }

    public List<CategoryWithAllLevelDTO> listCategory(List<String> categoryCodes){
        ApiResponse<List<CategoryWithAllLevelDTO>> result = categoryApi.listCategory(categoryCodes);
        if (Boolean.FALSE.equals(result.getSucc())) {
            log.error("***** request masterdata service to get category all level by categoryCode error={} ",result);
            throw new BizFailException(localeMessage.getMessage(ERROR_KEY_FOR_CATEGORY,new String[]{result.getMsg()}));
        }
        return result.getData();
    }

    public CategoryResultDTO categoryIdToCategoryCode(Long categoryId) {
        if (categoryId == null) {
            return null;
        }

        CategoryCEPCategoryReqDTO categoryReq = new CategoryCEPCategoryReqDTO();
        categoryReq.setId(String.valueOf(categoryId));
        ApiResponse<CategoryResultDTO> result = categoryApi.findCategoryById(categoryReq);
        if (Boolean.FALSE.equals(result.getSucc())) {
            log.error("通过品类ID查询品类信息失败，categoryId={}，response={}", categoryId, JacksonUtil.bean2Json(result));
            throw new BizFailException(localeMessage.getMessage(ERROR_KEY_FOR_CATEGORY,new String[]{result.getMsg()}));
        }
        if (result.getData() != null) {
            return result.getData();
        }
        return null;
    }

}
