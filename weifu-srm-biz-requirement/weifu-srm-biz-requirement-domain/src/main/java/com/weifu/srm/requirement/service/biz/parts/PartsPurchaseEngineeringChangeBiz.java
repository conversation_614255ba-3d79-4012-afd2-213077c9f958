package com.weifu.srm.requirement.service.biz.parts;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.enums.PartsPurchasePlanNodeEnum;
import com.weifu.srm.requirement.enums.RequirementPartsPurchasePlanStatusEnum;
import com.weifu.srm.requirement.enums.RequirementPartsStatusEnum;
import com.weifu.srm.requirement.manager.PartsPurchasePlanManager;
import com.weifu.srm.requirement.manager.project.ProjectTimesManager;
import com.weifu.srm.requirement.mq.PartsPurchasePlanEngineeringChangeMq;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

@Slf4j
@RequiredArgsConstructor
@Service
public class PartsPurchaseEngineeringChangeBiz {

    private final RequirementPartsPurchasePlanMapperService requirementPartsPurchasePlanMapperService;
    private final PartsPurchasePlanManager partsPurchasePlanManager;
    private final TransactionTemplate transactionTemplate;
    private final ProjectTimesManager projectTimesManager;

    public void updatePatsPurchaseNodeTimeUpdate(PartsPurchasePlanEngineeringChangeMq mq) {
        RequirementPartsPurchasePlanPO planPO = requirementPartsPurchasePlanMapperService.lambdaQuery()
                .eq(RequirementPartsPurchasePlanPO::getRequirementPartsNo, mq.getRequirementPartsNo())
                .eq(RequirementPartsPurchasePlanPO::getStatus, RequirementPartsPurchasePlanStatusEnum.DRAFT.getCode())
                .one();
        if (ObjectUtils.isEmpty(planPO) || StringUtils.isBlank(planPO.getPlanNo())) {
            log.error("this requirementNo not exist or planPo not exist or status not support update {}", planPO);
            return;
        }
        UpdateWrapper<RequirementPartsPurchasePlanPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(RequirementPartsPurchasePlanPO::getPlanNo, planPO.getPlanNo())
                .eq(RequirementPartsPurchasePlanPO::getStatus, RequirementPartsPurchasePlanStatusEnum.DRAFT.getCode())
                .set(RequirementPartsPurchasePlanPO::getUpdateBy, mq.getOperationBy())
                .set(RequirementPartsPurchasePlanPO::getUpdateName, mq.getOperationName());
        PartsPurchasePlanNodeEnum node = null;
        if (ObjectUtils.isEmpty(mq.getOtsOutsourceSubmitCompleteTime())) {
            node = PartsPurchasePlanNodeEnum.OTS_OUTSOURCE_SUBMIT;
            updateWrapper.lambda()
                    .set(RequirementPartsPurchasePlanPO::getOtsOutsourceSubmitCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompleteCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapSubmitCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompletionCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getEarlyControlExitCompleteTime, null);
        } else if (ObjectUtils.isEmpty(mq.getSupplierPpapCompleteCompleteTime())) {
            node = PartsPurchasePlanNodeEnum.SUPPLIER_PPAP_PLAN;
            updateWrapper.lambda()
                    .set(RequirementPartsPurchasePlanPO::getOtsOutsourceSubmitCompleteTime, mq.getOtsOutsourceSubmitCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompleteCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapSubmitCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompletionCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getEarlyControlExitCompleteTime, null);
        } else if (ObjectUtils.isEmpty(mq.getSupplierPpapSubmitCompleteTime())) {
            node = PartsPurchasePlanNodeEnum.SUPPLIER_PPAP_SUBMIT;
            updateWrapper.lambda()
                    .set(RequirementPartsPurchasePlanPO::getOtsOutsourceSubmitCompleteTime, mq.getOtsOutsourceSubmitCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompleteCompleteTime, mq.getSupplierPpapCompleteCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapSubmitCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompletionCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getEarlyControlExitCompleteTime, null);
        } else if (ObjectUtils.isEmpty(mq.getSupplierPpapCompletionCompleteTime())) {
            node = PartsPurchasePlanNodeEnum.SUPPLIER_PPAP_COMPLETION;
            updateWrapper.lambda()
                    .set(RequirementPartsPurchasePlanPO::getOtsOutsourceSubmitCompleteTime, mq.getOtsOutsourceSubmitCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompleteCompleteTime, mq.getSupplierPpapCompleteCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapSubmitCompleteTime, mq.getSupplierPpapSubmitCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompletionCompleteTime, null)
                    .set(RequirementPartsPurchasePlanPO::getEarlyControlExitCompleteTime, null);
        } else {
            node = PartsPurchasePlanNodeEnum.EARLY_CONTROL_EXIT;
            updateWrapper.lambda()
                    .set(RequirementPartsPurchasePlanPO::getOtsOutsourceSubmitCompleteTime, mq.getOtsOutsourceSubmitCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompleteCompleteTime, mq.getSupplierPpapCompleteCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapSubmitCompleteTime, mq.getSupplierPpapSubmitCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getSupplierPpapCompletionCompleteTime, mq.getSupplierPpapCompletionCompleteTime())
                    .set(RequirementPartsPurchasePlanPO::getEarlyControlExitCompleteTime, null);
        }
        PartsPurchasePlanNodeEnum startNode = node;
        transactionTemplate.execute(status -> {
            // 更新采购计划
            requirementPartsPurchasePlanMapperService.update(updateWrapper);
            // 更新项目时间
            projectTimesManager.cleanProjectStageTimeFromPurchasePlan(planPO.getRequirementPartsNo(), null,startNode, mq.getOperationBy(), mq.getOperationName());
            return null;
        });
        // 更新节点状态
        partsPurchasePlanManager.modifyPurchasePlanNodeStatus(planPO.getPlanNo());
    }
}
