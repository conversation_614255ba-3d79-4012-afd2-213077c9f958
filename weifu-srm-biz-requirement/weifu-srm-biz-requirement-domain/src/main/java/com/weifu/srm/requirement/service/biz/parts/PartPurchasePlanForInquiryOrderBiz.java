package com.weifu.srm.requirement.service.biz.parts;

import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.enums.RequirementPartsPurchasePlanStatusEnum;
import com.weifu.srm.requirement.manager.PartsPurchasePlanManager;
import com.weifu.srm.requirement.manager.project.ProjectTimesManager;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import com.weifu.srm.sourcing.enums.InquiryOrderStatusEnum;
import com.weifu.srm.sourcing.mq.InquiryOrderMQ;
import com.weifu.srm.sourcing.mq.InquiryOrderMaterialLineMQ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 处理询价单变更对零件采购计划的影响
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PartPurchasePlanForInquiryOrderBiz {

    private final PartsPurchasePlanManager partsPurchasePlanManager;
    private final RequirementPartsPurchasePlanMapperService partsPurchasePlanMapperService;
    private final ProjectTimesManager projectTimesManager;

    public void handleInquiryOrderChange(InquiryOrderMQ mq) {
        if (!InquiryOrderStatusEnum.APPROVING.equalsByCode(mq.getStatus())) {
            log.debug("该询价单状态变更对零件采购计划无影响={}", mq);
            return;
        }
        List<String> requirementPartNos = mq.getInquiryMaterialLines().stream().map(InquiryOrderMaterialLineMQ::getRequirementPartsNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(requirementPartNos)) {
            log.error("询价单对应的零件需求编号为空 无需进行处理={}", mq);
            return;
        }
        List<RequirementPartsPurchasePlanPO> plans = partsPurchasePlanMapperService.lambdaQuery()
                .in(RequirementPartsPurchasePlanPO::getRequirementPartsNo, requirementPartNos)
                .eq(RequirementPartsPurchasePlanPO::getStatus, RequirementPartsPurchasePlanStatusEnum.DRAFT.getCode())
                .list();
        if (CollectionUtils.isEmpty(plans)) {
            log.info("零件需求对应的采购计划不存在或状态不支持修改，无需进行操作={}", mq);
            return;
        }
        // 更新对应的采购计划的节点时间
        partsPurchasePlanMapperService.lambdaUpdate()
                .in(RequirementPartsPurchasePlanPO::getRequirementPartsNo, requirementPartNos)
                .set(RequirementPartsPurchasePlanPO::getSourcingStartCompleteTime,
                        Date.from(mq.getFirstSubmitTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant()))
                .set(RequirementPartsPurchasePlanPO::getUpdateBy, mq.getOperateBy())
                .set(RequirementPartsPurchasePlanPO::getUpdateName, mq.getOperateName())
                .update();
        // 更新采购计划对应的节点状态以及项目节点时间
        plans.parallelStream().forEach(plan -> {
            partsPurchasePlanManager.modifyPurchasePlanNodeStatus(plan.getPlanNo());
            projectTimesManager.updateStageTimeWithPurchasePlan(plan.getRequirementPartsNo(), mq.getOperateBy(), mq.getOperateName());
        });

    }

}
