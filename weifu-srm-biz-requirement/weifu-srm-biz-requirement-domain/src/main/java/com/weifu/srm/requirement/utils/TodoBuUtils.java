package com.weifu.srm.requirement.utils;

import com.weifu.srm.user.response.BaseSysUserRespDTO;
import org.apache.commons.lang3.StringUtils;

public class TodoBuUtils {

    public static String genUserName(BaseSysUserRespDTO user) {
        if (user == null) {
            return "";
        }
        return user.getRealName() + "（" + user.getDomain() + "）";
    }

    public static String genMaterialCode(String materialCode) {
        return StringUtils.isNotEmpty(materialCode)?materialCode:"无";
    }

}
