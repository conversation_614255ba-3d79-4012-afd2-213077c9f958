package com.weifu.srm.requirement.service.biz.parts;

import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.enums.ProjectNoticeTemplateEnum;
import com.weifu.srm.requirement.enums.PartsPurchasePlanNodeEnum;
import com.weifu.srm.requirement.enums.RequirementPartsPurchasePlanStatusEnum;
import com.weifu.srm.requirement.enums.RequirementPartsStatusEnum;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class PartsPurchasePlanDelayRiskNoticeBiz {

    private final RequirementPartsPurchasePlanMapperService purchasePlanMapperService;
    private final RequirementPartsMapperService requirementPartsMapperService;
    private final MqManager mqManager;

    public void delayRiskNotice(long days) {
        List<RequirementPartsPurchasePlanPO> list = purchasePlanMapperService.lambdaQuery()
                .eq(RequirementPartsPurchasePlanPO::getStatus, RequirementPartsPurchasePlanStatusEnum.DRAFT.getCode())
                .list();
        List<CreateSiteMessageMQ> listMessage = list.parallelStream().map(po -> notice(po, days)).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
        mqManager.sendTopic(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(listMessage));
    }

    private String checkDelayNode(String names, Date compareDate, Date planTime, Date completeTime, PartsPurchasePlanNodeEnum node) {
        if (ObjectUtils.isNotEmpty(planTime) && ObjectUtils.isEmpty(completeTime)) {
            names = compareDate.compareTo(planTime) == 0 ? names.concat(node.getValue()).concat(" ") : names;
        }
        return names;
    }

    private CreateSiteMessageMQ notice(RequirementPartsPurchasePlanPO planPO, long days) {
        LocalDate localDate = LocalDate.now().plusDays(days);
        Date compareDate = Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        String nodeName = "";
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getNewSupplierAdmissionStartPlanTime(), planPO.getNewSupplierAdmissionStartCompleteTime(), PartsPurchasePlanNodeEnum.NEW_SUPPLIER_ADMISSION_START);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getNewSupplierAdmissionEndPlanTime(), planPO.getNewSupplierAdmissionEndCompleteTime(), PartsPurchasePlanNodeEnum.NEW_SUPPLIER_ADMISSION_END);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getSourcingStartPlanTime(), planPO.getSourcingStartCompleteTime(), PartsPurchasePlanNodeEnum.SOURCING_START);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getSampASubmitPlanTime(), planPO.getSampASubmitCompleteTime(), PartsPurchasePlanNodeEnum.SAMP_A_SUBMIT);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getBusinessDesignatedCompletePlanTime(), planPO.getBusinessDesignatedCompleteCompleteTime(), PartsPurchasePlanNodeEnum.BUSINESS_DESIGNATED_COMPLETE);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getSampBSubmitPlanTime(), planPO.getSampBSubmitCompleteTime(), PartsPurchasePlanNodeEnum.SAMP_B_SUBMIT);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getOtsOutsourceSubmitPlanTime(), planPO.getOtsOutsourceSubmitCompleteTime(), PartsPurchasePlanNodeEnum.OTS_OUTSOURCE_SUBMIT);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getSupplierPpapCompletePlanTime(), planPO.getSupplierPpapCompleteCompleteTime(), PartsPurchasePlanNodeEnum.SUPPLIER_PPAP_PLAN);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getSupplierPpapSubmitPlanTime(), planPO.getSupplierPpapSubmitCompleteTime(), PartsPurchasePlanNodeEnum.SUPPLIER_PPAP_SUBMIT);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getSupplierPpapCompletionPlanTime(), planPO.getSupplierPpapCompletionCompleteTime(), PartsPurchasePlanNodeEnum.SUPPLIER_PPAP_COMPLETION);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getFirstSampPlanDeliveryPlanTime(), planPO.getFirstSampPlanDeliveryCompleteTime(), PartsPurchasePlanNodeEnum.FIRST_SAMP_PLAN_DELIVERY);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getManufactureSopPlanTime(), planPO.getManufactureSopCompleteTime(), PartsPurchasePlanNodeEnum.MANUFACTURE_SOP);
        nodeName = checkDelayNode(nodeName, compareDate, planPO.getEarlyControlExitPlanTime(), planPO.getEarlyControlExitCompleteTime(), PartsPurchasePlanNodeEnum.EARLY_CONTROL_EXIT);
        if (StringUtils.isBlank(nodeName)) {
            return null;
        }
        RequirementPartsPO partsPO = requirementPartsMapperService.lambdaQuery().eq(RequirementPartsPO::getRequirementPartsNo, planPO.getRequirementPartsNo()).one();
        String content = ProjectNoticeTemplateEnum.NODE_WARN_CATEGORY.getContent();
        content = content.replace("${0}", partsPO.getMaterialCode())
                .replace("${1}", nodeName);
        return getCreateSiteMessageMQ(planPO, partsPO, content, ProjectNoticeTemplateEnum.NODE_WARN_CATEGORY);
    }


    private CreateSiteMessageMQ getCreateSiteMessageMQ(RequirementPartsPurchasePlanPO planPO, RequirementPartsPO requirementPartsPO, String content, ProjectNoticeTemplateEnum noticeTemplateEnum) {
        CreateSiteMessageMQ siteNotice = new CreateSiteMessageMQ();
        siteNotice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        siteNotice.setBusinessNo(planPO.getRequirementPartsNo());
        siteNotice.setBusinessType(noticeTemplateEnum.getMessageClsEnum().getCode());
        siteNotice.setUserId(requirementPartsPO.getCpeId());
        siteNotice.setUserName(requirementPartsPO.getCpeName());
        siteNotice.setIconType(noticeTemplateEnum.getIconTypeEnum().getCode());
        siteNotice.setTitle(noticeTemplateEnum.getTitle());
        siteNotice.setContent(content);
        return siteNotice;
    }
}
