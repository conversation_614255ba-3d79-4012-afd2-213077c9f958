package com.weifu.srm.requirement.convert;

import com.weifu.srm.requirement.po.AttachmentRecordPO;
import com.weifu.srm.requirement.request.AttachmentMessageReqDTO;
import com.weifu.srm.requirement.request.project.ProjectFileReqDTO;
import com.weifu.srm.requirement.response.project.ProjectFileResDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AttachmentRecordConvert {

    AttachmentRecordPO toPO(ProjectFileReqDTO reqDTO);

    ProjectFileResDTO toDTO(AttachmentRecordPO po);

    List<AttachmentMessageReqDTO> toDTOs(List<AttachmentRecordPO> pos);
    @Mapping(source = "fileOriginalName", target = "name")
    AttachmentMessageReqDTO toMessageDTO(AttachmentRecordPO pos);

    List<AttachmentRecordPO> toPOs(List<AttachmentMessageReqDTO> pos);

    AttachmentRecordPO toPO(AttachmentMessageReqDTO po);

    List<ProjectFileResDTO> toDTOList(List<AttachmentRecordPO> pos);

}
