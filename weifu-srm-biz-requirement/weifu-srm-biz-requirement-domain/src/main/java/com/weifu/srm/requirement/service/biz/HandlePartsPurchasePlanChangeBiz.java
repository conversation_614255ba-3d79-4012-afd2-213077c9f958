package com.weifu.srm.requirement.service.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.requirement.atomicservice.RequirementMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.constants.MQProjectTopicConstants;
import com.weifu.srm.requirement.constants.PurchasePlanOperationConstants;
import com.weifu.srm.requirement.enums.RequirementPartsStatusEnum;
import com.weifu.srm.requirement.manager.RequirementManager;
import com.weifu.srm.requirement.manager.RequirementPartsManager;
import com.weifu.srm.requirement.mq.PartsPurchasePlanChangeMq;
import com.weifu.srm.requirement.mq.RequirementPartStatusChangedMq;
import com.weifu.srm.requirement.po.RequirementPO;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class HandlePartsPurchasePlanChangeBiz {

    private final RequirementPartsMapperService requirementPartsMapperService;
    private final RequirementMapperService requirementMapperService;
    private final MqManager mqManager;
    private final RequirementPartsManager requirementPartsManager;
    private final RequirementManager requirementManager;
    private final TransactionTemplate transactionTemplate;

    /**
     * 将零件需求设置为“已完成”
     */
    public void execute(PartsPurchasePlanChangeMq mq) {
        if (!StringUtils.equals(mq.getOperation(), PurchasePlanOperationConstants.COMPLETE)) {
            log.info("零件采购计划不是完成操作，忽略此消息");
            return;
        }

        String partNo = mq.getRequirementPartsNo();
        RequirementPartsPO oldPart = requirementPartsMapperService.getByNo(partNo);
        if (!RequirementPartsStatusEnum.PENDING.equalsCode(oldPart.getStatus())
                && !RequirementPartsStatusEnum.PROCESSING.equalsCode(oldPart.getStatus())) {
            log.info("当前不是未执行或执行中的状态，忽略此消息");
            return;
        }

        transactionTemplate.execute(t -> {
            // 更新零件需求状态为“已完成”
            Date current = new Date();
            String partStatus = RequirementPartsStatusEnum.COMPLETED.getCode();
            LambdaUpdateWrapper<RequirementPartsPO> updateWrapper = buildUpdatePartWrapper(mq, oldPart, partStatus, current);
            requirementPartsMapperService.update(updateWrapper);

            // 更新总需求状态（使用更新后的零件需求状态计算）
            updateRequirementStatus(mq, oldPart, current);

            // 发送零件需求状态变化MQ
            sendStatusChangedMq(mq, oldPart, partStatus, current);
            return null;
        });
    }

    private LambdaUpdateWrapper<RequirementPartsPO> buildUpdatePartWrapper(PartsPurchasePlanChangeMq mq, RequirementPartsPO part,
                                                                           String nextStatus, Date current) {
        LambdaUpdateWrapper<RequirementPartsPO> updatePartWrapper = new LambdaUpdateWrapper<>();
        updatePartWrapper.eq(RequirementPartsPO::getId, part.getId());
        updatePartWrapper.set(RequirementPartsPO::getUpdateTime, current);
        updatePartWrapper.set(RequirementPartsPO::getUpdateBy, mq.getOperationBy());
        updatePartWrapper.set(RequirementPartsPO::getUpdateName, mq.getOperationName());
        updatePartWrapper.set(RequirementPartsPO::getStatus, nextStatus);
        return updatePartWrapper;
    }

    /**
     * 更新总需求状态（使用零件需求状态计算）
     */
    private void updateRequirementStatus(PartsPurchasePlanChangeMq mq, RequirementPartsPO part, Date current) {
        requirementMapperService.lambdaUpdate()
                .eq(RequirementPO::getRequirementNo, part.getRequirementNo())
                .set(RequirementPO::getStatus, requirementManager.calcRequirementStatus(part.getRequirementNo()))
                .set(RequirementPO::getUpdateBy, mq.getOperationBy())
                .set(RequirementPO::getUpdateName, mq.getOperationName())
                .set(RequirementPO::getUpdateTime, current)
                .update();
    }

    /**
     * 发送零件需求状态变化MQ（只包含原来的零件需求）
     */
    private void sendStatusChangedMq(PartsPurchasePlanChangeMq mq, RequirementPartsPO oldPart, String status, Date current) {
        requirementPartsManager.sendStatusChangedMq(oldPart, status, current, mq.getOperationBy(), mq.getOperationName());
    }

}