package com.weifu.srm.requirement.convert;

import com.weifu.srm.requirement.po.ProjectPO;
import com.weifu.srm.requirement.request.project.ProjectSaveReqDTO;
import com.weifu.srm.requirement.response.project.ProjectExportResDTO;
import com.weifu.srm.requirement.response.project.ProjectNameQueryResDTO;
import com.weifu.srm.requirement.response.project.ProjectPageResDTO;
import com.weifu.srm.requirement.response.project.ProjectResDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProjectConvert {

    ProjectPO toPo(ProjectSaveReqDTO reqDTO);
    ProjectExportResDTO toExport(ProjectPageResDTO reqDTO);

    List<ProjectPageResDTO> toResult(List<ProjectPO> projectPOS);
    List<ProjectExportResDTO> toExportResult(List<ProjectPageResDTO> projectPOS);

    @Mapping(source = "createTime", target = "createTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "projectStatus", target = "projectLightStatus")
    ProjectPageResDTO toPage(ProjectPO projectPO);

    ProjectResDTO toDTO(ProjectPO projectPO);

    List<ProjectNameQueryResDTO> toNameRespDTO(List<ProjectPO> po);
    @Mapping(source = "submitByThreeCategory", target = "operationByThreeCategory")
    @Mapping(source = "submitByThreeCategoryName", target = "operationByThreeCategoryName")
    @Mapping(source = "id", target = "projectId")
    @Mapping(source = "outsourceTotalTargetCost",target = "costTotalAmt")
    ProjectNameQueryResDTO toNameQueryResDTO(ProjectPO po);
}
