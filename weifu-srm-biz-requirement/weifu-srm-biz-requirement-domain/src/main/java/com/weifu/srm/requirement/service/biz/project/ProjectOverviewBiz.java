package com.weifu.srm.requirement.service.biz.project;

import cn.hutool.core.collection.CollUtil;
import com.weifu.srm.requirement.atomicservice.RequirementMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class ProjectOverviewBiz {

    private final RequirementMapperService requirementMapperService;
    private final RequirementPartsMapperService requirementPartsMapperService;

    public List<Long> getRequirementPartsCPEIdList(String projectNo){
        List<String> requirementNoList = requirementMapperService.getRequirementNosByProjectNo(CollUtil.newArrayList(projectNo));
        if(CollUtil.isEmpty(requirementNoList)){
            return CollUtil.newArrayList();
        }
        List<RequirementPartsPO> requirementPartsPOList = requirementPartsMapperService.lambdaQuery()
                .in(RequirementPartsPO::getRequirementNo, requirementNoList)
                .list();
        if (CollUtil.isEmpty(requirementPartsPOList)){
            return CollUtil.newArrayList();
        }
        return requirementPartsPOList.stream().map(RequirementPartsPO::getCpeId).collect(Collectors.toList());
    }

}
