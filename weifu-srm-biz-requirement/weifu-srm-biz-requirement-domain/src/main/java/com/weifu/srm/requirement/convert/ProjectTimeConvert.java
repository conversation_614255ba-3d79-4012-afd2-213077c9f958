package com.weifu.srm.requirement.convert;

import com.weifu.srm.requirement.po.ProjectTimePO;
import com.weifu.srm.requirement.request.project.ProjectTimeReqDTO;
import com.weifu.srm.requirement.response.project.ProjectTimeResDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ProjectTimeConvert {

    @Mapping(source = "startDate", target = "startDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "newSupplierAdmissionCompletionDate", target = "newSupplierAdmissionCompletionDate",
            dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "businessDesignatedCompletionDate", target = "businessDesignatedCompletionDate",
            dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "otsOutsourceSubmitDate", target = "otsOutsourceSubmitDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "supplierPpapSubmitDate", target = "supplierPpapSubmitDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "supplierPpapCompletionDate", target = "supplierPpapCompletionDate",
            dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "earlyControlExitDate", target = "earlyControlExitDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "manufactureSopDate", target = "manufactureSopDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    ProjectTimePO toPo(ProjectTimeReqDTO reqDTO);

    @Mapping(source = "startDate", target = "startDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "newSupplierAdmissionCompletionDate", target = "newSupplierAdmissionCompletionDate",
                    dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "businessDesignatedCompletionDate", target = "businessDesignatedCompletionDate",
                    dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "otsOutsourceSubmitDate", target = "otsOutsourceSubmitDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "supplierPpapSubmitDate", target = "supplierPpapSubmitDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "supplierPpapCompletionDate", target = "supplierPpapCompletionDate",
            dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "earlyControlExitDate", target = "earlyControlExitDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "manufactureSopDate", target = "manufactureSopDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    ProjectTimeResDTO toDTO(ProjectTimePO po);

}
