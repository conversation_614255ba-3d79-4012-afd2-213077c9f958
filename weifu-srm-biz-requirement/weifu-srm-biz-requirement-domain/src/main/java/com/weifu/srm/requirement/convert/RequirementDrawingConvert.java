package com.weifu.srm.requirement.convert;

import com.weifu.srm.requirement.po.RequirementPartsDrawingPO;
import com.weifu.srm.requirement.request.MaterialDrawingFileSaveReqDTO;
import com.weifu.srm.requirement.response.MaterialDrawingFileListRespDTO;
import com.weifu.srm.requirement.response.RequirementDrawingByMaterialRespDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/15 14:47
 * @Description
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface RequirementDrawingConvert {
    List<RequirementDrawingByMaterialRespDTO> toRespDTO(List<RequirementPartsDrawingPO> poList);

    List<RequirementPartsDrawingPO> toEntities(List<MaterialDrawingFileSaveReqDTO> req);

    List<MaterialDrawingFileListRespDTO> toRespList (List<RequirementPartsDrawingPO> poList);
    MaterialDrawingFileListRespDTO toResp (RequirementPartsDrawingPO po);

}
