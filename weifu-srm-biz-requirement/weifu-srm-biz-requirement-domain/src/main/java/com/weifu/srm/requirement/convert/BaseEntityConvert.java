package com.weifu.srm.requirement.convert;

import com.weifu.srm.mybatis.base.BaseEntity;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.requirement.request.project.ProjectBasePageReqDTO;
import com.weifu.srm.requirement.request.project.ProjectBaseReqDTO;

import java.util.Date;

public class BaseEntityConvert {

    private BaseEntityConvert(){
    }

    public static void setCommon(ProjectBaseReqDTO reqDTO, BaseEntity baseEntity, Date date){
        BaseEntityUtil.setCommon(baseEntity,reqDTO.getUserId(), reqDTO.getUserName(),date);
    }

    public static void setCommonForU(ProjectBaseReqDTO reqDTO, BaseEntity baseEntity, Date date){
        BaseEntityUtil.setCommonForU(baseEntity,reqDTO.getUserId(), reqDTO.getUserName(),date);
    }

    public static void setCommon(ProjectBasePageReqDTO reqDTO, BaseEntity baseEntity, Date date){
        BaseEntityUtil.setCommon(baseEntity,reqDTO.getUserId(), reqDTO.getUserName(),date);
    }

    public static void setCommonForU(ProjectBasePageReqDTO reqDTO, BaseEntity baseEntity, Date date){
        BaseEntityUtil.setCommonForU(baseEntity,reqDTO.getUserId(), reqDTO.getUserName(),date);
    }
}
