package com.weifu.srm.requirement.utils;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.requirement.enums.AnalysisExelFileTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class AnalysisExelFileUtils {

    private AnalysisExelFileUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static List<String[]> analysisExelFile(MultipartFile file, AnalysisExelFileTypeEnum fileTypeEnum){
        String[] split = Objects.requireNonNull(file.getOriginalFilename()).split("\\.");
        if (!split[split.length - 1].equals("xlsx")) {
            log.error("模板文件不正确");
        }
        List<String[]> rowList = new ArrayList<>();
        try(InputStream is = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(is);
            Sheet sheet = workbook.getSheetAt(0);
            Row titleRow = sheet.getRow(0);
            String title = titleRow.getCell(0).getStringCellValue();
            if(!ObjectUtil.equals(fileTypeEnum.getName(), title)){
                log.error("模板文件不正确");
            }
            int rowIndex = 2;
            int rowCount = sheet.getPhysicalNumberOfRows();
            for (; rowIndex < rowCount; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if(ObjectUtil.isEmpty(row)){
                    break;
                }

                String[] cellArray = new String[fileTypeEnum.getCellNum()];
                boolean flag = true;
                flag = isFlag(fileTypeEnum, cellArray, row, flag);
                if (flag){
                    break;
                }
                rowList.add(cellArray);
            }
        }catch (Exception e){
            log.error("RequirementUploadBiz uploadFile has error", e);
            throw new BizFailException(e.getMessage(),e);
        }
        return rowList;
    }

    private static boolean isFlag(AnalysisExelFileTypeEnum fileTypeEnum, String[] cellArray, Row row, boolean flag) {
        for (int i = 0; i < fileTypeEnum.getCellNum(); i++) {
            String numPattern = "0";
            if (i == fileTypeEnum.getRequirementQtyIndex()) {
                numPattern = "0.##";
            } else if (i == fileTypeEnum.getPurchaseExpectQtyIndex()) {
                numPattern = "0.###";
            } else if (i == fileTypeEnum.getTargetUntaxedPriceIndex()) {
                numPattern = "0.##";
            } else if (i == fileTypeEnum.getProductionRequireMthQtyIndex()) {
                numPattern = "0.###";
            }
            cellArray[i] = getValue(row.getCell(i), numPattern);
            if(ObjectUtil.isNotEmpty(cellArray[i])){
                flag = false;
            }
        }
        return flag;
    }

    private static String getValue(Cell cell, String numPattern) {
        String obj = null;
        DecimalFormat df = new DecimalFormat(numPattern); // 格式化number String字符
        SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd"); // 日期格式化

        if (ObjectUtil.isEmpty(cell)) {
            return obj;
        }
        switch (cell.getCellType()) {
            case BOOLEAN:
                obj = String.valueOf(cell.getBooleanCellValue());
                break;
            case ERROR:
                obj = String.valueOf(cell.getErrorCellValue());
                break;
            case NUMERIC:
                if ("General".equals(cell.getCellStyle().getDataFormatString())) {
                    obj = df.format(cell.getNumericCellValue());
                } else if ("m/d/yy".equals(cell.getCellStyle().getDataFormatString())) {
                    obj = sdf.format(cell.getDateCellValue());
                } else {
                    obj = df.format(cell.getNumericCellValue());
                }
                break;
            case STRING:
                obj = cell.getRichStringCellValue().getString().trim();
                break;
            default:
                break;
        }
        return obj;
    }
}
