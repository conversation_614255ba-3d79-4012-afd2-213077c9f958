package com.weifu.srm.requirement.manager.remote.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.requirement.constants.ErrorCodeConstants;
import com.weifu.srm.user.api.SysDivisionApi;
import com.weifu.srm.user.response.division.SysDivisionFindByDivisionResDTO;
import com.weifu.srm.user.response.division.SysDivisionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysDivisionManager {

    private final SysDivisionApi sysDivisionApi;
    private final LocaleMessage localeMessage;

    public List<SysDivisionRespDTO> getSysDivisionRespDTO(){
        ApiResponse<List<SysDivisionRespDTO>> result = sysDivisionApi.getAllDivisions();
        if (Boolean.FALSE.equals(result.getSucc())) {
            log.error("***** request user service to check user with phone exist error={} ",result);
            throw new BizFailException(result.getMsg());
        }
        return result.getData();
    }

    public Map<String, SysDivisionRespDTO> getSysDivisionMap(){
        List<SysDivisionRespDTO> sysDivisionRespDTO = getSysDivisionRespDTO();
        if (CollUtil.isEmpty(sysDivisionRespDTO)){
            return Map.of();
        }
        return sysDivisionRespDTO.stream().collect(Collectors.toMap(SysDivisionRespDTO::getDivisionId, v -> v));
    }

    public SysDivisionFindByDivisionResDTO findByDivisionId(String divisionId){
        ApiResponse<SysDivisionFindByDivisionResDTO> data = sysDivisionApi.findByDivisionId(divisionId);
        if (Boolean.FALSE.equals(data.getSucc())) {
            log.error("***** request user service to find division error={} ",data);
            return null;
        }
        return data.getData();
    }


    public Boolean findIsTerminationByProjectNo(String divisionId, Long optId) {
        SysDivisionFindByDivisionResDTO divisionResDTO = findByDivisionId(divisionId);
        if(ObjectUtil.isEmpty(divisionResDTO)){
            return false;
        }
        // bp组长
        if(ObjectUtil.equals(optId, divisionResDTO.getProjectBpMasterUserId())){
            return true;
        }
        // 采购处长
        if(ObjectUtil.equals(optId, divisionResDTO.getProjectDirectorUserId())){
            return true;
        }
        return false;
    }

    /**
     * 根据事业部编码查询事业部对应的项目BP
     */
    public String findProjectBpBydivisionId(String divisionId) {
        if (StringUtils.isEmpty(divisionId)) {
            return null;
        }

        ApiResponse<SysDivisionFindByDivisionResDTO> apiResponse = sysDivisionApi.findByDivisionId(divisionId);
        if (!apiResponse.getSucc()) {
            log.error("查询事业部项目BP失败，apiResponse={}", JacksonUtil.bean2Json(apiResponse));
            throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.QUERY_DIVISION_PROJECT_BP_FAILED));
        }
        if (apiResponse.getData() != null) {
            return apiResponse.getData().getProjectBpUserId();
        }
        return null;
    }

    /**
     * 根据事业部编码查询事业部对应的管控人员信息
     */
    public SysDivisionFindByDivisionResDTO findByDivisionId2(String divisionId) {
        ApiResponse<SysDivisionFindByDivisionResDTO> apiResponse = sysDivisionApi.findByDivisionId(divisionId);
        if (!apiResponse.getSucc()) {
            log.error("查询事业部管控关系失败，apiResponse={}", JacksonUtil.bean2Json(apiResponse));
            throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.QUERY_DIVISION_CONTROL_RELATION_FAILED));
        }
        if (apiResponse.getData() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.DIVISION_CONTROL_RELATION_NOT_CONFIGURED, new String[]{divisionId}));
        }
        return apiResponse.getData();
    }

}