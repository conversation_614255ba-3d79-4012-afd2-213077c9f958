package com.weifu.srm.requirement.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.requirement.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.requirement.atomicservice.ProjectLogMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.convert.AttachmentRecordConvert;
import com.weifu.srm.requirement.convert.ProjectLogConvert;
import com.weifu.srm.requirement.enums.project.ProjectModifyReasonEnum;
import com.weifu.srm.requirement.po.AttachmentRecordPO;
import com.weifu.srm.requirement.po.ProjectLogPO;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import com.weifu.srm.requirement.request.project.ProjectUpdateRecordListReqDTO;
import com.weifu.srm.requirement.response.project.ProjectLogResDTO;
import com.weifu.srm.requirement.service.ProjectLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ProjectLogServiceImpl implements ProjectLogService {

    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final ProjectLogMapperService projectLogMapperService;
    private final ProjectLogConvert projectLogConvert;
    private final AttachmentRecordConvert attachmentRecordConvert;
    private final RequirementPartsPurchasePlanMapperService requirementPartsPurchasePlanMapperService;

    @Override
    public PageResponse<ProjectLogResDTO> queryRecordList(ProjectUpdateRecordListReqDTO reqDTO) {
        ArrayList<String> noList = CollUtil.newArrayList(reqDTO.getProjectNo());
        List<RequirementPartsPurchasePlanPO> purchasePlanPOS = requirementPartsPurchasePlanMapperService.lambdaQuery()
                .eq(RequirementPartsPurchasePlanPO::getProjectNo, reqDTO.getProjectNo())
                .list();
        if(CollUtil.isNotEmpty(purchasePlanPOS)){
            noList.addAll(purchasePlanPOS.stream()
                    .map(RequirementPartsPurchasePlanPO::getRequirementPartsNo).collect(Collectors.toList()));
        }

        LambdaQueryWrapper<ProjectLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjectLogPO::getNo, noList)
                .ge(ObjectUtil.isNotEmpty(reqDTO.getStartDate()), ProjectLogPO::getModifyDate, reqDTO.getStartDate())
                .le(ObjectUtil.isNotEmpty(reqDTO.getEndDate()), ProjectLogPO::getModifyDate, reqDTO.getEndDate())
                .eq(ProjectLogPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .orderByDesc(ProjectLogPO::getCreateTime);
        Page<ProjectLogPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());

        page = projectLogMapperService.page(page, queryWrapper);
        List<ProjectLogPO> records = page.getRecords();
        List<ProjectLogResDTO> respDTOList = projectLogConvert.toResult(records);

        // 查询附件并分组
        if(CollUtil.isNotEmpty(records)){
            List<String> logIds = records.stream().map(e->String.valueOf(e.getId())).collect(Collectors.toList());
            Map<String, AttachmentRecordPO> collect = getStringAttachmentRecordPOMap(logIds);

            for (ProjectLogResDTO projectLogResDTO : respDTOList){
                if (CollUtil.isNotEmpty(collect)){
                    projectLogResDTO.setProjectFileResDTO(attachmentRecordConvert.toDTO(collect.get(String.valueOf(projectLogResDTO.getId()))));
                }
                String reason = ProjectModifyReasonEnum.statOf(projectLogResDTO.getModifyReason()).getValue();
                if(ProjectModifyReasonEnum.PROJECT_MODIFY_REASON_OTHER.getValue().equals(reason)){
                    reason = reason + "-" + projectLogResDTO.getOtherReason();
                }
                projectLogResDTO.setModifyReason(reason);
            }
        }
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), page.getTotal(), respDTOList);
    }

    private Map<String, AttachmentRecordPO> getStringAttachmentRecordPOMap(List<String> logIds) {
        List<AttachmentRecordPO> list = attachmentRecordMapperService.lambdaQuery()
                .in(AttachmentRecordPO::getBusinessNo, logIds).list();
        Map<String, AttachmentRecordPO> collect = new HashMap<>();
        if(CollUtil.isNotEmpty(list)){
            collect = list.stream()
                    .collect(Collectors.toMap(AttachmentRecordPO::getBusinessNo, v -> v));
        }
        return collect;
    }
}
