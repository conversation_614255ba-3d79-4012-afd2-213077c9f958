package com.weifu.srm.requirement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.requirement.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.requirement.atomicservice.ProjectLogMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanPostponeRecordMapperService;
import com.weifu.srm.requirement.convert.AttachmentRecordConvert;
import com.weifu.srm.requirement.convert.PartsPurchasePlanConvert;
import com.weifu.srm.requirement.convert.ProjectLogConvert;
import com.weifu.srm.requirement.enums.RequirementPartsPurchasePlanStatusEnum;
import com.weifu.srm.requirement.enums.project.ProjectLogTypeEnum;
import com.weifu.srm.requirement.enums.project.ProjectModifyReasonEnum;
import com.weifu.srm.requirement.manager.PartPurchasePostponeManager;
import com.weifu.srm.requirement.manager.PartsPurchasePlanManager;
import com.weifu.srm.requirement.manager.project.ProjectTimesManager;
import com.weifu.srm.requirement.mq.PartsPurchasePlanEngineeringChangeMq;
import com.weifu.srm.requirement.mq.PartsPurchaseNodeTimeUpdateMq;
import com.weifu.srm.requirement.mq.RequirementPartStatusChangedMq;
import com.weifu.srm.requirement.po.AttachmentRecordPO;
import com.weifu.srm.requirement.po.ProjectLogPO;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPostponeRecordPO;
import com.weifu.srm.requirement.request.PartsPurchasePlanChangeRecordReqDTO;
import com.weifu.srm.requirement.request.PartsPurchasePlanNodeCompleteSaveReqDTO;
import com.weifu.srm.requirement.request.PartsPurchasePlanPostponeReqDTO;
import com.weifu.srm.requirement.request.PartsPurchasePlanSubmitReqDTO;
import com.weifu.srm.requirement.response.*;
import com.weifu.srm.requirement.response.project.ProjectFileResDTO;
import com.weifu.srm.requirement.service.PartsPurchasePlanService;
import com.weifu.srm.requirement.service.biz.parts.*;
import com.weifu.srm.sourcing.mq.BusinessDesignationOrderMQ;
import com.weifu.srm.sourcing.mq.InquiryOrderMQ;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class PartsPurchasePlanServiceImpl implements PartsPurchasePlanService {

    private final PartPurchasePlanSaveBiz partPurchasePlanSaveBiz;
    private final PartPurchasePostponeManager partPurchasePostponeManager;
    private final PartsPurchasePlanConvert partsPurchasePlanConvert;
    private final RequirementPartsPurchasePlanPostponeRecordMapperService postponeRecordMapperService;
    private final PartPurchaseNodeCompleteRegisterBiz partPurchaseNodeCompleteRegisterBiz;
    private final RequirementPartsPurchasePlanMapperService purchasePlanMapperService;
    private final PartsPurchasePlanManager partsPurchasePlanManager;
    private final PartPurchasePlanNodeDetailSearchBiz partPurchasePlanNodeDetailSearchBiz;
    private final PartsPurchasePlanDelayRiskNoticeBiz partsPurchasePlanDelayRiskNoticeBiz;
    private final ProjectLogMapperService projectLogMapperService;
    private final ProjectLogConvert projectLogConvert;
    private final PartsPurchasePlanDelayButNoProcessNoticeBiz partsPurchasePlanDelayButNoProcessNoticeBiz;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final AttachmentRecordConvert attachmentRecordConvert;
    private final PartsPurchasePlanChangeRecordListBiz partsPurchasePlanChangeRecordListBiz;
    private final PartsPurchaseNodeExternalTimeUpdateBiz partsPurchaseNodeExternalTimeUpdateBiz;
    private final PartsPurchaseEngineeringChangeBiz partsPurchaseEngineeringChangeBiz;
    private final ProjectTimesManager projectTimesManager;
    private final PartsPurchaseChangeForPartsStatusChangeBiz partsPurchaseChangeForPartsStatusChangeBiz;
    private final PartPurchasePlanForInquiryOrderBiz partPurchasePlanForInquiryOrderBiz;
    private final PartPurchasePlanForBusinessDesignationBiz partPurchasePlanForBusinessDesignationBiz;

    @Override
    public PartsPurchaseSaveRespDTO savePurchasePlan(PartsPurchasePlanSubmitReqDTO partsPO) {
        PartsPurchaseSaveRespDTO partsPurchaseSaveRespDTO = partPurchasePlanSaveBiz.savePurchasePlan(partsPO);
        // 创建采购计划后， 立马触发项目节点状态更新
        projectTimesManager.updateStageTimeWithPurchasePlan(partsPO.getRequirementPartsNo(),partsPO.getUserId(),partsPO.getUserName());
        return partsPurchaseSaveRespDTO;
    }

    @Override
    public void savePostpone(PartsPurchasePlanPostponeReqDTO reqDTO) {
        partPurchasePostponeManager.savePostpone(reqDTO);
    }

    @Override
    public List<PartsPurchasePlanPostponeSearchRespDTO> searchPostponeList(String planNo, String planNode) {
        List<RequirementPartsPurchasePlanPostponeRecordPO> list = postponeRecordMapperService.lambdaQuery()
                .eq(RequirementPartsPurchasePlanPostponeRecordPO::getPlanNo, planNo)
                .eq(RequirementPartsPurchasePlanPostponeRecordPO::getPostponeNode, planNode)
                .orderByDesc(RequirementPartsPurchasePlanPostponeRecordPO::getId)
                .list();
        return partsPurchasePlanConvert.toPostponeRespDTOList(list);
    }

    @Override
    public void registerPlanNodeComplete(PartsPurchasePlanNodeCompleteSaveReqDTO reqDTO) {
        partPurchaseNodeCompleteRegisterBiz.registerPlanNodeComplete(reqDTO);
    }

    @Override
    public void scheduleForUpdatePurchaseStatus() {
        List<RequirementPartsPurchasePlanPO> list = purchasePlanMapperService.lambdaQuery()
                .eq(RequirementPartsPurchasePlanPO::getStatus, RequirementPartsPurchasePlanStatusEnum.DRAFT.getCode())
                .list();
        list.parallelStream().forEach(po -> partsPurchasePlanManager.modifyPurchasePlanNodeStatus(po.getPlanNo()));
    }

    @Override
    public void scheduleForPurchasePlanPostponeNotice() {
        List<RequirementPartsPurchasePlanPO> list = purchasePlanMapperService.lambdaQuery()
                .eq(RequirementPartsPurchasePlanPO::getStatus, RequirementPartsPurchasePlanStatusEnum.DRAFT.getCode())
                .list();
        list.parallelStream().forEach(po -> partPurchasePostponeManager.checkPostpone(po.getPlanNo()));
    }

    @Override
    public void scheduleForPurchasePlanPostponeRiskNotice(long days) {
        partsPurchasePlanDelayRiskNoticeBiz.delayRiskNotice(days);
    }

    @Override
    public PartsPurchasePlanDetailRespDTO searchPlanNode(String requirementPartsNo,String planNo) {
        return partPurchasePlanNodeDetailSearchBiz.searchPlanNode(requirementPartsNo,planNo);
    }

    @Override
    public void scheduleForPurchasePlanPostponeButNotProcessNotice(long days) {
        partsPurchasePlanDelayButNoProcessNoticeBiz.delayButNoProcessNotice(days);
    }

    @Override
    public List<PartsPurchaseChangeLogRespDTO> searchChangeLog(PartsPurchasePlanChangeRecordReqDTO reqDTO) {
        List<ProjectLogPO> list = projectLogMapperService.lambdaQuery().eq(ProjectLogPO::getNo, reqDTO.getRequirementPartsNo())
                .ge(ObjectUtil.isNotEmpty(reqDTO.getStartDate()), ProjectLogPO::getCreateTime, reqDTO.getStartDate())
                .le(ObjectUtil.isNotEmpty(reqDTO.getEndDate()), ProjectLogPO::getCreateTime, reqDTO.getEndDate())
                .orderByDesc(ProjectLogPO::getCreateTime)
                .list();
        for (int i = 0; i < list.size(); i++) {
            ProjectLogPO e = list.get(i);
            String reason = ProjectModifyReasonEnum.statOf(e.getModifyReason()).getValue();
            if(ProjectModifyReasonEnum.PROJECT_MODIFY_REASON_OTHER.getValue().equals(reason)){
                reason = reason + "-" + e.getOtherReason();
            }
            e.setId((long) i+1);
            e.setModifyReason(reason);
            e.setType(ObjectUtils.isEmpty(ProjectLogTypeEnum.statOf(e.getType()))? "":ProjectLogTypeEnum.statOf(e.getType()).getValue());

        }
        return projectLogConvert.toExportRespDTOs(list);
    }

    @Override
    public PartsPurchaseMessageWithRequirementRespDTO searchPurchasePlanMessageByRequirementPartsNo(String requirementPartsNo) {
        return partsPurchasePlanManager.searchPurchasePlanMessageByRequirementPartsNo(requirementPartsNo);
    }

    @Override
    public List<ProjectFileResDTO> searchPlanNodeAttachmentList(String planNo, String planNode) {
        List<AttachmentRecordPO> list = attachmentRecordMapperService.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessNo, planNo)
                .eq(AttachmentRecordPO::getBusinessType, planNode)
                .list();
        return attachmentRecordConvert.toDTOList(list);
    }

    @Override
    public PageResponse<PartsPurchasePlanChangeLogResDTO> queryRecordList(PartsPurchasePlanChangeRecordReqDTO reqDTO) {
        return partsPurchasePlanChangeRecordListBiz.queryRecordList(reqDTO);
    }

    @Override
    public void updatePatsPurchaseNodeTimeUpdate(PartsPurchaseNodeTimeUpdateMq mq) {
        partsPurchaseNodeExternalTimeUpdateBiz.updatePatsPurchaseNodeTimeUpdate(mq);
    }

    @Override
    public void purchasePlanEngineeringChange(PartsPurchasePlanEngineeringChangeMq mq) {
        partsPurchaseEngineeringChangeBiz.updatePatsPurchaseNodeTimeUpdate(mq);
    }

    @Override
    public void handleRequirementPartStatusChanged(RequirementPartStatusChangedMq mq) {
        partsPurchaseChangeForPartsStatusChangeBiz.execute(mq);
    }

    @Override
    public void handleInquiryOrderChange(InquiryOrderMQ mq) {
        partPurchasePlanForInquiryOrderBiz.handleInquiryOrderChange(mq);
    }

    @Override
    public void handleBusinessDesignationChange(BusinessDesignationOrderMQ mq) {
        partPurchasePlanForBusinessDesignationBiz.handleBusinessDesignationChange(mq);
    }


}
