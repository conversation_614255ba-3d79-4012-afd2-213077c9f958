package com.weifu.srm.requirement.enums;

import com.weifu.srm.requirement.constants.RequirementBizConstants;
import lombok.Getter;

import java.util.List;

@Getter
public enum RequirementExportFileTypeEnum {

    ALL_REQUIREMENT("ALL_REQUIREMENT",RequirementBizConstants.REQUIREMENT_INFO,"总需求",
            List.of("需求编号","需求概述", "状态", "需求类型","采购需求类型","是否关联项目","项目名称","事业部/子公司",
                    "采购组织","需求导向/侧重点","外购件总目标成本", "客户名称", "产品名称","产品型号","外购件数量","提交人角色",
                    "三级品类","需求创建人","创建时间")),

    MATERIAL_REQUIREMENT("MATERIAL_REQUIREMENT",RequirementBizConstants.REQUIREMENT_INFO,"零件需求",
            List.of("需求编号","零件需求编号","状态","工厂","物料号","物料描述","一级品类","二级品类","三级品类","采购BP",
                    "品类组长","品类工程师","质量负责人","样件需求数量","样件需求交付时间","基本单位","推荐供应商名称","推荐供应商属性",
                    "收货地址","采购业务类型","技术联系人","关联物料号","每月产能需求","预计年采购量","目标价格（未税）","材质",
                    "批产后生命周期（预测）","成本中心","项目订单号（研发类）","内部订单号","试制申请单号","首批样件需求交付时间",
                    "推荐品类工程师","推荐质量负责人","需求创建人","需求创建时间","关闭原因","询价单号","订单号")),

    MATERIAL_PURCHASE_REQUIREMENT("MATERIAL_PURCHASE_REQUIREMENT", RequirementBizConstants.REQUIREMENT_INFO,"零件采购计划",
            List.of("需求编号","零件需求编号","项目启动时间","新供应商准入开始时间","新供应商准入完成间","寻源开始时间",
                    "A样外购件提交时间","商务定点完成时间","首次样件预计交付时间","B样外购件提交时间","OTS外购件提交",
                    "供应商PPAP计划制订完成","供应商PPAP提交","供应商PPAP放行","量产SOP","批产早期控制退出"));

    private String key;
    private String fileName;
    private String sheetName;
    private List<String> head;

    RequirementExportFileTypeEnum(String key, String fileName, String sheetName, List<String> head){
        this.key = key;
        this.fileName = fileName;
        this.sheetName = sheetName;
        this.head = head;
    }
}
