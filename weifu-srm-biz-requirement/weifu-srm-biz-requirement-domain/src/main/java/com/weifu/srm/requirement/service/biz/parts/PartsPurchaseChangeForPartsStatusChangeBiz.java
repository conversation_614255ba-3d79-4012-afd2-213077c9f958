package com.weifu.srm.requirement.service.biz.parts;

import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.enums.RequirementPartsPurchasePlanStatusEnum;
import com.weifu.srm.requirement.enums.RequirementPartsStatusEnum;
import com.weifu.srm.requirement.manager.PartsPurchasePlanManager;
import com.weifu.srm.requirement.manager.project.ProjectTimesManager;
import com.weifu.srm.requirement.mq.RequirementPartStatusChangedMq;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 已完成、已关闭、已重新发起、待执行 这四者状态会对零件采购计划产生影响
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PartsPurchaseChangeForPartsStatusChangeBiz {

    private final ProjectTimesManager projectTimesManager;
    private final RequirementPartsPurchasePlanMapperService purchasePlanMapperService;
    private final PartsPurchasePlanManager partsPurchasePlanManager;
    private final TransactionTemplate transactionTemplate;
    private final RequirementPartsMapperService requirementPartsMapperService;

    public void execute(RequirementPartStatusChangedMq mq) {
        // 更新项目采购计划节点上的完成时间
        if (StringUtils.equalsAny(mq.getStatus(), RequirementPartsStatusEnum.PENDING.getCode(),
                RequirementPartsStatusEnum.CLOSED.getCode(),
                RequirementPartsStatusEnum.COMPLETED.getCode(),
                RequirementPartsStatusEnum.RECREATED.getCode())) {
            projectTimesManager.updateStageTimeWithPurchasePlan(mq.getRequirementPartsNo(), mq.getOperateBy(), mq.getOperateName());
        }
        // 涉及到零件需求关闭,需要同时关闭计划
        if (StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.CLOSED.getCode())) {
            purchasePlanMapperService.lambdaUpdate()
                    .eq(RequirementPartsPurchasePlanPO::getRequirementPartsNo, mq.getRequirementPartsNo())
                    .set(RequirementPartsPurchasePlanPO::getStatus, RequirementPartsPurchasePlanStatusEnum.CLOSED.getCode())
                    .set(RequirementPartsPurchasePlanPO::getUpdateBy, mq.getOperateBy())
                    .set(RequirementPartsPurchasePlanPO::getUpdateName, mq.getOperateName())
                    .update();
        }
        // 需求释放
        if (StringUtils.equals(mq.getPreStatus(), RequirementPartsStatusEnum.PROCESSING.getCode())
                && StringUtils.equals(mq.getStatus(), RequirementPartsStatusEnum.PENDING.getCode())) {
            RequirementPartsPurchasePlanPO planPO = purchasePlanMapperService.lambdaQuery()
                    .eq(RequirementPartsPurchasePlanPO::getRequirementPartsNo, mq.getRequirementPartsNo())
                    .eq(RequirementPartsPurchasePlanPO::getStatus, RequirementPartsPurchasePlanStatusEnum.DRAFT.getCode())
                    .one();
            if (ObjectUtils.isEmpty(planPO)) {
                log.info("this RequirementPartsNo have no purchase plan requirementPartsNo={} data={}", mq.getRequirementPartsNo(), planPO);
                return;
            }
            // 清空采购计划
            transactionTemplate.executeWithoutResult(status -> {
                // 清除采购计划，重新创建
                purchasePlanMapperService.lambdaUpdate()
                        .eq(RequirementPartsPurchasePlanPO::getRequirementPartsNo, mq.getRequirementPartsNo())
                        .remove();
                requirementPartsMapperService.lambdaUpdate()
                        .eq(RequirementPartsPO::getRequirementPartsNo, mq.getRequirementPartsNo())
                        .set(RequirementPartsPO::getPlanNo, null)
                        .update();
                // 清除项目上的完成时间
                projectTimesManager.cleanProjectStageTimeFromPurchasePlan(planPO.getRequirementPartsNo(), null, null, mq.getOperateBy(), mq.getOperateName());
            });

        }
    }

}
