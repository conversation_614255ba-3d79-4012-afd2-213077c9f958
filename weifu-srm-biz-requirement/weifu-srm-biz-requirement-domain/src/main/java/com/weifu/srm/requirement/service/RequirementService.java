package com.weifu.srm.requirement.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.requirement.request.RequirementQueryPageReqDTO;
import com.weifu.srm.requirement.request.RequirementSaveReqDTO;
import com.weifu.srm.requirement.response.RequirementDrawingByMaterialRespDTO;
import com.weifu.srm.requirement.response.RequirementQueryByNoRespDTO;
import com.weifu.srm.requirement.response.RequirementQueryPageRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/12 11:21
 * @Description 需求管理
 * @Version 1.0
 */
public interface RequirementService {

    String saveRequirement(RequirementSaveReqDTO req);

    String submitRequirement(RequirementSaveReqDTO req);

    PageResponse<RequirementQueryPageRespDTO> queryPage(RequirementQueryPageReqDTO reqDTO);

    RequirementQueryByNoRespDTO queryRequirementByNo(String requirementNo);

    void deleteRequirement(String requirementNo);

    List<RequirementDrawingByMaterialRespDTO> queryRequirementDrawingByMaterial(String materialCode);

    /**
     * 处理采购寻源需求审批工单审批结果
     */
    void handleRequirementApproveRst(TicketStatusChangedMQ mq);

}
