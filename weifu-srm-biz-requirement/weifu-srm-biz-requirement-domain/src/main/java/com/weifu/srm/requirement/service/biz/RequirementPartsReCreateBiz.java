package com.weifu.srm.requirement.service.biz;

import cn.hutool.core.bean.BeanUtil;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.requirement.atomicservice.RequirementMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.constants.ErrorCodeConstants;
import com.weifu.srm.requirement.enums.RequirementPartsStatusEnum;
import com.weifu.srm.requirement.enums.RequirementStatusEnum;
import com.weifu.srm.requirement.manager.RequirementManager;
import com.weifu.srm.requirement.manager.RequirementPartsManager;
import com.weifu.srm.requirement.po.RequirementPO;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import com.weifu.srm.requirement.request.RequirementPartsReCreateReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Date 2024/8/21 22:55
 * @Description
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequirementPartsReCreateBiz {

    private final RequirementPartsMapperService requirementPartsMapperService;
    private final RequirementMapperService requirementMapperService;
    private final RequirementManager requirementManager;
    private final TransactionTemplate transactionTemplate;
    private final RequirementPartsManager requirementPartsManager;
    private final LocaleMessage localeMessage;

    private final AtomicInteger counter = new AtomicInteger(1);

    public String reCreateRequirementParts(RequirementPartsReCreateReqDTO req) {
        List<String> partNos = req.getRequirementPartsNo();
        log.info("零件需求重新发起开始，partNos={}，req={}", partNos, JacksonUtil.bean2Json(req));

        List<RequirementPartsPO> oriParts = requirementPartsMapperService.listByNo(partNos);

        Set<String> requirementNos = new HashSet<>();
        oriParts.forEach(part -> {
            RequirementPartsStatusEnum partsStatusEnum = RequirementPartsStatusEnum.getByCode(part.getStatus());
            if (!RequirementPartsStatusEnum.BP_RETURN.equals(partsStatusEnum)) {
                throw new BizFailException("the parts requirement " + part.getRequirementPartsNo() + " can not be reCreated of this status");
            }
            requirementNos.add(part.getRequirementNo());
        });

        if (requirementNos.size() > 1) {
            throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.BATCH_RECREATE_REQUIRE_SAME_TOTAL_REQ));
        }

        String oriRequirementNo = oriParts.get(0).getRequirementNo();
        RequirementPO oriRequirement = requirementMapperService.getByNo(oriRequirementNo);

        Date current = new Date();
        String newRequirementNo = requirementManager.generateRequirementNo();
        RequirementPO newRequirement = new RequirementPO();
        BeanUtil.copyProperties(oriRequirement, newRequirement);
        newRequirement.setRequirementNo(newRequirementNo);
        newRequirement.setStatus(RequirementStatusEnum.DRAFT.getCode());
        BaseEntityUtil.setCommon(newRequirement, req.getOperationBy(), req.getOperationByName(), current);

        newRequirement.setOriRequirementNo(oriRequirement.getRequirementNo());
        List<RequirementPartsPO> newParts = buildNewParts(req, oriParts, newRequirementNo, current);
        List<RequirementPartsPO> updateOldParts = buildUpdateParts(req, oriParts, current);
        transactionTemplate.execute(r -> {
            // 保存新的总需求
            requirementMapperService.save(newRequirement);
            requirementPartsMapperService.saveBatch(newParts);

            // 更新原来的零件需求状态为“已重新发起”
            requirementPartsMapperService.updateBatchById(updateOldParts);
            // 尝试修改总需求的状态为“已完成”
            requirementManager.toCompleted(oriRequirementNo, req.getOperationBy(), req.getOperationByName(), current);

            // 发送零件需求状态变化MQ（只包含原来的零件需求）
            oriParts.forEach(oldPart -> {
                requirementPartsManager.sendStatusChangedMq(oldPart, RequirementPartsStatusEnum.RECREATED.getCode(), current,
                        req.getOperationBy(), req.getOperationByName());
            });
            return null;
        });
        log.info("零件需求重新发起完成，partNos={}", partNos);
        return newRequirementNo;
    }

    private List<RequirementPartsPO> buildNewParts(RequirementPartsReCreateReqDTO req,
                                                   List<RequirementPartsPO> oriPartsPos,
                                                   String requirementNo, Date current) {
        List<RequirementPartsPO> newParts = new ArrayList<>();
        for (RequirementPartsPO oriPart:oriPartsPos) {
            String requirementPartsNo = requirementNo + "_" + counter.getAndIncrement();
            String oriPartsNo = oriPart.getRequirementPartsNo();
            RequirementPartsPO newPart = new RequirementPartsPO();
            BeanUtil.copyProperties(oriPart, newPart);
            newPart.setOriPartsNo(oriPartsNo);
            newPart.setRequirementNo(requirementNo);
            newPart.setRequirementPartsNo(requirementPartsNo);
            newPart.setStatus(RequirementPartsStatusEnum.DRAFT.getCode());
            BaseEntityUtil.setCommon(newPart, req.getOperationBy(), req.getOperationByName(), current);
            newPart.setCurrentOperationBy(req.getOperationBy());
            newPart.setCurrentOperationByName(req.getOperationByName());
            newPart.setCpeMasterId(null);
            newPart.setCpeMasterId(null);
            newPart.setBpId(null);
            newPart.setSqeId(null);
            newPart.setRecommendedCpe(null);
            newPart.setRecommendedSqe(null);
            newPart.setRecommendedCpeName(null);
            newPart.setRecommendedSqeName(null);
            newParts.add(newPart);
        }
        return newParts;
    }

    private List<RequirementPartsPO> buildUpdateParts(RequirementPartsReCreateReqDTO req,
                                                      List<RequirementPartsPO> oriParts,
                                                      Date current) {
        List<RequirementPartsPO> updateParts = new ArrayList<>();
        for (RequirementPartsPO oriPart:oriParts) {
            RequirementPartsPO updatePart = new RequirementPartsPO();
            updatePart.setId(oriPart.getId());
            updatePart.setStatus(RequirementPartsStatusEnum.RECREATED.getCode());
            BaseEntityUtil.setCommonForU(updatePart, req.getOperationBy(), req.getOperationByName(), current);
            updateParts.add(updatePart);
        }
        return updateParts;
    }

}
