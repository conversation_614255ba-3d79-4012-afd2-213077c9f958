package com.weifu.srm.requirement.manager.project;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.requirement.atomicservice.ProjectLogMapperService;
import com.weifu.srm.requirement.atomicservice.ProjectMapperService;
import com.weifu.srm.requirement.atomicservice.ProjectTimeMapperService;
import com.weifu.srm.requirement.constants.RequirementBizConstants;
import com.weifu.srm.requirement.convert.BaseEntityConvert;
import com.weifu.srm.requirement.convert.ProjectConvert;
import com.weifu.srm.requirement.convert.ProjectTimeConvert;
import com.weifu.srm.requirement.enums.ProjectNoticeTemplateEnum;
import com.weifu.srm.requirement.enums.project.*;
import com.weifu.srm.requirement.manager.AttachmentRecordManager;
import com.weifu.srm.requirement.manager.remote.user.SysUserManager;
import com.weifu.srm.requirement.po.ProjectLogPO;
import com.weifu.srm.requirement.po.ProjectPO;
import com.weifu.srm.requirement.po.ProjectTimePO;
import com.weifu.srm.requirement.request.project.ProjectDeleteReqDTO;
import com.weifu.srm.requirement.request.project.ProjectSaveReqDTO;
import com.weifu.srm.requirement.request.project.ProjectTransferReqDTO;
import com.weifu.srm.requirement.utils.BusinessNoUtils;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Locale;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectUpdateManager {

    private final ProjectMapperService projectMapperService;
    private final ProjectTimeMapperService projectTimeMapperService;
    private final ProjectLogMapperService projectLogMapperService;
    private final AttachmentRecordManager attachmentRecordManager;
    private final ProjectConvert projectConvert;
    private final ProjectTimeConvert projectTimeConvert;
    private final ProjectSendMessageManager projectSendMessageManager;
    private final LocaleMessage localeMessage;
    private final IdService idService;
    private final SysUserManager sysUserManager;


    public String addProject(ProjectSaveReqDTO reqDTO) {

        ProjectPO one = projectMapperService.lambdaQuery().eq(ProjectPO::getProjectName, reqDTO.getProjectName()).one();

        // 项目名称不可重复
        if((ObjectUtil.isEmpty(reqDTO.getProjectNo()) && ObjectUtil.isNotEmpty(one)
                || (ObjectUtil.isNotEmpty(reqDTO.getProjectNo())
                    && ObjectUtil.isNotEmpty(one)
                    && !one.getProjectNo().equals(reqDTO.getProjectNo())))){
            throw new BizFailException(localeMessage.getMessage("project.name.is.exist", Locale.SIMPLIFIED_CHINESE));
        }

        // 保存项目信息
        ProjectPO projectPO = saveProject(reqDTO);

        // 保存时间线
        saveProjectTimes(reqDTO, projectPO);

        //保存附件信息
        attachmentRecordManager.saveFileToProject(reqDTO.getFileList(),projectPO.getProjectNo(),
                reqDTO.getUserId(),reqDTO.getUserName());

        return projectPO.getProjectNo();
    }

    private ProjectPO saveProject(ProjectSaveReqDTO reqDTO) {
        ProjectPO projectPO = projectConvert.toPo(reqDTO);
        // 获取项目编号,编号识别项目计划的唯一标识不可重复
        saveProjectNo(reqDTO, projectPO);
        // 编制中的灯为灰
        projectPO.setProjectStatus(ProjectLightStatusEnum.LIGHT_GRAY.getKey());
        projectPO.setStatus(ProjectStatusEnum.PROJECT_STATUS_EDITOR.getKey());
        LambdaQueryWrapper<ProjectPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectPO::getProjectNo, projectPO.getProjectNo());
        projectMapperService.saveOrUpdate(projectPO,wrapper);
        log.info("saveProject info:【{}】", projectPO);
        return projectPO;
    }

    private void saveProjectNo(ProjectSaveReqDTO reqDTO, ProjectPO projectPO) {
        Date date = new Date();
        if(ObjectUtil.isEmpty(projectPO.getProjectNo())){
            projectPO.setProjectNo(BusinessNoUtils.generateProjectNo(idService,
                    RequirementBizConstants.PROJECT_NO_PREFIX));
            BaseEntityConvert.setCommon(reqDTO, projectPO, date);
            projectPO.setProjectLeaderName(reqDTO.getUserName());
            projectPO.setProjectLeaderId(reqDTO.getUserId());
        }else {
            ProjectPO one = projectMapperService.lambdaQuery().eq(ProjectPO::getProjectNo, reqDTO.getProjectNo()).one();
            BaseEntityConvert.setCommonForU(reqDTO, projectPO, date);
            if(ProjectStatusEnum.PROJECT_STATUS_EDITOR.getKey().equals(one.getStatus())
                    && !one.getProjectLeaderId().equals(reqDTO.getUserId())){
                throw new BizFailException(localeMessage.getMessage("project.preparation.can.edited.creator"));
            }

            projectPO.setProjectLeaderName(one.getProjectLeaderName());
            projectPO.setProjectLeaderId(one.getProjectLeaderId());
        }
    }

    private void saveProjectTimes(ProjectSaveReqDTO reqDTO, ProjectPO projectPO) {
        ProjectTimePO projectTimePO = projectTimeConvert.toPo(reqDTO.getTimes());
        projectTimePO.setProjectNo(projectPO.getProjectNo());
        projectTimePO.setStageType(ProjecTimeStageEnum.TIME_STAGE_PLAN.getKey());

        LambdaQueryWrapper<ProjectTimePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectTimePO::getProjectNo, projectTimePO.getProjectNo());
        wrapper.eq(ProjectTimePO::getStageType, projectTimePO.getStageType());
        BaseEntityConvert.setCommon(reqDTO, projectTimePO, new Date());
        projectTimeMapperService.saveOrUpdate(projectTimePO, wrapper);
        log.info("saveProjectTimes info:【{}】", projectPO);
    }

    public void deleteProject(ProjectDeleteReqDTO reqDTO) {
        ProjectPO entity = projectMapperService.lambdaQuery()
                                                .eq(ProjectPO::getProjectNo, reqDTO.getProjectNo())
                                                .one();
        if (!ProjectStatusEnum.PROJECT_STATUS_EDITOR.getKey().equals(entity.getStatus())){
            throw new BizFailException(localeMessage.getMessage("project.not.preparation.cannot.be.deleted"));
        }

        // 删除项目信息
        projectMapperService.removeById(entity.getId());

        // 删除时间轴
        LambdaQueryWrapper<ProjectTimePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectTimePO::getProjectNo, entity.getProjectNo());
        projectTimeMapperService.remove(wrapper);

        // 删除附件
        attachmentRecordManager.remove(entity.getProjectNo());
    }

    public void transfer(ProjectTransferReqDTO reqDTO) {
        ProjectPO entity = projectMapperService.lambdaQuery()
                .eq(ProjectPO::getProjectNo, reqDTO.getProjectNo())
                .one();
        if (!ProjectStatusEnum.PROJECT_STATUS_IMPLEMENT.getKey().equals(entity.getStatus())){
            throw new BizFailException(localeMessage.getMessage("project.not.execution.cannot.transferred"));
        }

        Date date = new Date();
        entity.setProjectLeaderId(reqDTO.getProjectLeaderId());
        entity.setProjectLeaderName(reqDTO.getProjectLeaderName());
        entity.setUpdateTime(date);
        LambdaQueryWrapper<ProjectPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjectPO::getId, entity.getId());
        // 更新负责人信息
        BaseEntityConvert.setCommonForU(reqDTO, entity, new Date());
        projectMapperService.update(entity, wrapper);


        // 记录日志
        ProjectLogPO po = new ProjectLogPO();
        po.setNo(entity.getProjectNo());
        po.setType(ProjectLogTypeEnum.PROJECT_LOG_TYPE_PROJECT.getKey());
        po.setModifyDate(date);
        po.setModifyReason(ProjectModifyReasonEnum.PROJECT_MODIFY_REASON_REQUIREMENT.getKey());
        BaseEntityConvert.setCommon(reqDTO, po, new Date());
        projectLogMapperService.save(po);
        log.info("transfer info:【{}】", reqDTO);

        // 查询操作人信息
        BaseSysUserRespDTO userDetailById = sysUserManager.getUserDetailById(reqDTO.getUserId());
        String name = String.format("%s(%s)", userDetailById.getRealName(), userDetailById.getUserName());
        String projectName = String.format("%s_%s", entity.getProjectName(), entity.getProjectNo());
        // 发送转派通知
        projectSendMessageManager.boxNoticeDTO(ProjectNoticeTemplateEnum.TRANSFER_NOTICE, entity.getProjectNo(),
                reqDTO.getProjectLeaderId(), reqDTO.getProjectLeaderName(),name, projectName);
    }
}
