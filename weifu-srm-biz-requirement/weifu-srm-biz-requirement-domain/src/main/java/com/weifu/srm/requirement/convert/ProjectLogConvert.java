package com.weifu.srm.requirement.convert;

import com.weifu.srm.requirement.po.ProjectLogPO;
import com.weifu.srm.requirement.response.PartsPurchaseChangeLogRespDTO;
import com.weifu.srm.requirement.response.PartsPurchasePlanChangeLogResDTO;
import com.weifu.srm.requirement.response.project.ProjectLogResDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProjectLogConvert {

    List<ProjectLogResDTO> toResult(List<ProjectLogPO> po);

    @Mapping(source = "modifyDate", target = "modifyDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    ProjectLogResDTO toDTO(ProjectLogPO po);

    @Mapping(source = "modifyDate", target = "modifyDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    PartsPurchaseChangeLogRespDTO toExportRespDTO(ProjectLogPO po);

    List<PartsPurchaseChangeLogRespDTO> toExportRespDTOs(List<ProjectLogPO> pos);

    @Mapping(source = "modifyDate", target = "modifyDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    PartsPurchasePlanChangeLogResDTO toPurchaseChangeLogRespDTO(ProjectLogPO po);

    List<PartsPurchasePlanChangeLogResDTO> toListPurchaseChangeLogRespDTOs(List<ProjectLogPO> pos);
}
