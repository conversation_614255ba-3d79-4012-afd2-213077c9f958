<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.weifu</groupId>
        <artifactId>weifu-srm-biz-requirement</artifactId>
        <version>1.0.1</version>
    </parent>

    <artifactId>weifu-srm-biz-requirement-domain</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-requirement-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-requirement-infrastructure</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-common-util</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>webjars-locator-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-audit-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-communication-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-id-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-segment</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-masterdata-api</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mq-sender</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-purchase-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-sourcing-api</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>

</project>