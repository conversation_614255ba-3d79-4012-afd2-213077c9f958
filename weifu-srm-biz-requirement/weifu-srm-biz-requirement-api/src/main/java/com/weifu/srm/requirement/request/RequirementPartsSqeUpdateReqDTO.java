package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/10/15 10:59
 * @Description 更新sqe工程师DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsSqeUpdateReqDTO {

    @NotBlank(message = "requirementPartsNo is required.")
    @ApiModelProperty(value = "零件需求编号", required = true)
    private String requirementPartsNo;

    @NotNull(message = "sqeId is required.")
    @ApiModelProperty(value = "质量工程师", required = true)
    private Long sqeId;

    @NotBlank(message = "sqeName is required.")
    @ApiModelProperty(value = "质量工程师名称", required = true)
    private String sqeName;

    @ApiModelProperty("操作人")
    private Long operationBy;

    @ApiModelProperty("操作人名称")
    private String operationByName;
}
