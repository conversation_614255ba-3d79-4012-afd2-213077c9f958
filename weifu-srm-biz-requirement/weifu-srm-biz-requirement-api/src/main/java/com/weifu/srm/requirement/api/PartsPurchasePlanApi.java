package com.weifu.srm.requirement.api;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.requirement.constants.ServiceConstants;
import com.weifu.srm.requirement.request.PartsPurchasePlanChangeRecordReqDTO;
import com.weifu.srm.requirement.request.PartsPurchasePlanNodeCompleteSaveReqDTO;
import com.weifu.srm.requirement.request.PartsPurchasePlanPostponeReqDTO;
import com.weifu.srm.requirement.request.PartsPurchasePlanSubmitReqDTO;
import com.weifu.srm.requirement.request.project.ProjectUpdateRecordListReqDTO;
import com.weifu.srm.requirement.response.*;
import com.weifu.srm.requirement.response.project.ProjectFileResDTO;
import com.weifu.srm.requirement.response.project.ProjectLogResDTO;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@FeignClient(value = ServiceConstants.APPLICATION_NAME)
public interface PartsPurchasePlanApi {
    /**
     * 保存零件采购计划信息
     * @param reqDTO PartsPurchasePlanSubmitReqDTO
     * @return PartsPurchasePlanDetailRespDTO
     */
    @PostMapping("/requirement/parts/purchasePlan/save")
    ApiResponse<PartsPurchaseSaveRespDTO> savePurchasePlan(@Valid @RequestBody PartsPurchasePlanSubmitReqDTO reqDTO);

    /**
     * 零件采购计划延期原因保存
     * @param reqDTO
     * @return
     */
    @PostMapping("/requirement/parts/purchasePlan/postponeSave")
    ApiResponse<Void> savePostpone(@Valid @RequestBody PartsPurchasePlanPostponeReqDTO reqDTO);

    /**
     * 零件采购计划延期原因查询
     * @param planNO
     * @param planNode
     * @return
     */
    @GetMapping("/requirement/parts/purchasePlan/postponeList")
    ApiResponse<List<PartsPurchasePlanPostponeSearchRespDTO>> searchPostPoneList(@RequestParam("planNo") String planNO, @RequestParam("planNode") String planNode);

    /**
     * 修改采购计划节点完成登记
     * @param reqDTO
     * @return
     */
    @PostMapping("/requirement/parts/purchasePlan/nodeComplete")
    ApiResponse<Void> registerPlanNodeComplete(@Valid @RequestBody PartsPurchasePlanNodeCompleteSaveReqDTO reqDTO);

    /**
     * 采购计划节点情况请查询
     * @param planNo
     * @return
     */
    @GetMapping("/requirement/parts/purchasePlan/query")
    ApiResponse<PartsPurchasePlanDetailRespDTO> searchPlanNode(@RequestParam(value = "requirementPartsNo",required = false) String requirementPartsNo,
                                                               @RequestParam(value = "planNo",required = false) String planNo);

    /**
     * 零件采购计划修改记录导出
     * @param reqDTO
     * @return
     */
    @PostMapping("/requirement/parts/purchasePlan/change/export")
    ApiResponse<List<PartsPurchaseChangeLogRespDTO>> exportChangeLog(@RequestBody PartsPurchasePlanChangeRecordReqDTO reqDTO);

    /**
     * 根据零件需求编号 查询采购计划信息
     * @param requirementPartsNo
     * @return
     */
    @GetMapping("/requirement/parts/purchasePlan/requirementNo")
    ApiResponse<PartsPurchaseMessageWithRequirementRespDTO> searchPurchasePlanMessage(@RequestParam("requirementPartsNo") String requirementPartsNo);

    /**
     * 根据 计采购计划编号 + 节点key查询附件
     * @param planNo
     * @param planNode
     * @return
     */
    @GetMapping("/requirement/parts/purchasePlan/attachmentList")
    ApiResponse<List<ProjectFileResDTO>> searchPlanNodeAttachmentList(@RequestParam("planNo") String planNo, @RequestParam("planNode") String planNode);

    /**
     * 查看采购计划修改记录
     */
    @PostMapping("/requirement/parts/purchasePlan/changeRecord")
    ApiResponse<PageResponse<PartsPurchasePlanChangeLogResDTO>> queryRecordList(@Valid @RequestBody PartsPurchasePlanChangeRecordReqDTO reqDTO);


}
