package com.weifu.srm.requirement.request.project;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ProjectTimeReqDTO extends ProjectBaseReqDTO {
    /** SRM项目编号 */
    private String projectNo ;
    /** 阶段类型（计划时间，变动时间，最终完成时间） */
    private String stageType ;
    /** 项目启动时间 */
    private String startDate ;
    /** 新供应商准入完成时间 */
    private String newSupplierAdmissionCompletionDate ;
    /** 商务定点完成时间 */
    private String businessDesignatedCompletionDate ;
    /** OTS外购件提交时间 */
    private String otsOutsourceSubmitDate ;
    /** 供应商PPAP提交时间 */
    private String supplierPpapSubmitDate ;
    /** 供应商PPAP放行完成时间 */
    private String supplierPpapCompletionDate ;
    /** 批产早期控制退出时间 */
    private String earlyControlExitDate ;
    /** 量产SOP时间 */
    private String manufactureSopDate ;

    /** 修改原因 */
    private String updateReason;
    /** 备注 */
    private String otherReason;
    /** 文件名称 */
    private String fileName;
    /** 文件真实名称 */
    private String fileOriginalName;
    /** 文件URL */
    private String fileUrl;
    /** 备注 */
    private String remark;
}
