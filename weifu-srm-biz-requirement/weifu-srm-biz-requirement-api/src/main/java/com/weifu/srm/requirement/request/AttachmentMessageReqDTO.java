package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class AttachmentMessageReqDTO implements Serializable {


    @ApiModelProperty("业务编号")
    private String businessNo ;
    @ApiModelProperty("附件类型")
    private String businessType ;
    /**
     * 附件真实名称
     */
    private String fileOriginalName;

    /**
     * 附件URL
     */
    private String fileUrl;

    /**
     * 附件Name
     */
    private String fileName;

    /**
     * 用于回显的名称
     */
    private String name;
}
