package com.weifu.srm.requirement.api;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.requirement.constants.ServiceConstants;
import com.weifu.srm.requirement.request.RequirementQueryPageReqDTO;
import com.weifu.srm.requirement.request.RequirementSaveReqDTO;
import com.weifu.srm.requirement.response.EnumResponse;
import com.weifu.srm.requirement.response.RequirementDrawingByMaterialRespDTO;
import com.weifu.srm.requirement.response.RequirementQueryByNoRespDTO;
import com.weifu.srm.requirement.response.RequirementQueryPageRespDTO;
import com.weifu.srm.requirement.response.upload.RequirementAnalysisResultResDTO;
import feign.Response;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/8/12 11:15
 * @Description 需求关联Api
 * @Version 1.0
 */
@FeignClient(value = ServiceConstants.APPLICATION_NAME)
public interface RequirementApi {
    /**
     * 新建需求
     * @param reqDTO newRequirementSaveReqDTO
     * @return 总需求编号
     */
    @PostMapping("/requirement/save")
    ApiResponse<String> saveRequirement(@RequestBody @Valid RequirementSaveReqDTO reqDTO);

    /**
     * 新建需求
     * @param reqDTO newRequirementSaveReqDTO
     * @return 总需求编号
     */
    @PostMapping("/requirement/submit")
    ApiResponse<String> submitRequirement(@RequestBody @Valid RequirementSaveReqDTO reqDTO);

    /**
     * 删除需求
     * @param   requirementNo
     * @return 删除需求
     */
    @DeleteMapping("/requirement/delete/{requirementNo}")
    ApiResponse<Void> deleteRequirement(@PathVariable("requirementNo") @NotNull(message = "requirementNo can not be null") String requirementNo);

    /**
     * 总需求列表分页查询
     * @param reqDTO
     * @return
     */
    @PostMapping("/requirement/page")
    ApiResponse<PageResponse<RequirementQueryPageRespDTO>> queryRequirementPage(@RequestBody @Valid RequirementQueryPageReqDTO reqDTO);

    /**
     * 导出需求列表
     * @param reqDTO
     * @return
     */
    @PostMapping("/requirement/exportRequirement")
    Response exportRequirement(@RequestBody RequirementQueryPageReqDTO reqDTO);

    /**
     * 根据需求编号查询 需求详情
     * @param requirementNo 需求编号
     * @return 需求详情
     */
    @GetMapping("/requirement/query/{requirementNo}")
    ApiResponse<RequirementQueryByNoRespDTO> queryRequirementByNo(@PathVariable("requirementNo") @NotNull(message = "requirementNo can not be null") String requirementNo);
    @GetMapping("/requirement/drawing/query/{materialCode}")
    ApiResponse<List<RequirementDrawingByMaterialRespDTO>> queryRequirementDrawingByMaterial(@PathVariable("materialCode") @NotNull(message = "requirementNo can not be null") String materialCode);

    @ApiOperation("需求订单导入")
    @PostMapping(value = "/requirement/save/import", consumes = "multipart/form-data")
    ApiResponse<RequirementAnalysisResultResDTO> priceAdjustApplyImport(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "fileType") String fileType);
}
