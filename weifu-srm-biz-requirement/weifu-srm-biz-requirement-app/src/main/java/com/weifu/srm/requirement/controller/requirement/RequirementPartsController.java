package com.weifu.srm.requirement.controller.requirement;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.requirement.api.RequirementPartsApi;
import com.weifu.srm.requirement.constants.RequirementBizConstants;
import com.weifu.srm.requirement.request.*;
import com.weifu.srm.requirement.response.RequirementPartsQueryByNoRespDTO;
import com.weifu.srm.requirement.response.RequirementSimpleRespDTO;
import com.weifu.srm.requirement.service.RequirementPartsService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "零件需求管理")
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class RequirementPartsController implements RequirementPartsApi {
    private final RequirementPartsService partsService;
    private final LocaleMessage localeMessage;

    @Override
    public ApiResponse<RequirementPartsQueryByNoRespDTO> queryByPartsNo(String requirementPartsNo) {
        return ApiResponse.success(localeMessage.getMessage(RequirementBizConstants.COMMON_SUCCESS),partsService.queryRequirementPartsByNo(requirementPartsNo));
    }

    @Override
    public ApiResponse<PageResponse<RequirementPartsQueryByNoRespDTO>> queryByPartsPage(RequirementPartsQueryPageReqDTO reqDTO) {
        PageResponse<RequirementPartsQueryByNoRespDTO> pageResult = partsService.queryPage(reqDTO);
        return ApiResponse.success(localeMessage.getMessage(RequirementBizConstants.COMMON_SUCCESS),pageResult);
    }

    @Override
    public ApiResponse<List<RequirementPartsQueryByNoRespDTO>> queryByPartsNos(RequirementPartsQueryByNosReqDTO reqDTO) {
        List<RequirementPartsQueryByNoRespDTO> partsList = partsService.queryByPartsNos(reqDTO);
        return ApiResponse.success(localeMessage.getMessage(RequirementBizConstants.COMMON_SUCCESS),partsList);
    }

    @Override
    public ApiResponse<String> returnByPartsNos(RequirementPartsReturnReqDTO reqDTO) {
        partsService.returnByPartsNos(reqDTO);
        return ApiResponse.success(localeMessage.getMessage(RequirementBizConstants.COMMON_SUCCESS),reqDTO.getRequirementNo());
    }

    @Override
    public ApiResponse<Void> closeByPartsNos(RequirementPartsCloseReqDTO reqDTO) {
        partsService.closeByPartsNos(reqDTO);
        return ApiResponse.success(localeMessage.getMessage(RequirementBizConstants.COMMON_SUCCESS), null);
    }

    @Override
    public ApiResponse<String> transferByPartsNo(RequirementPartsTransferReqDTO reqDTO) {
        partsService.transferByPartsNo(reqDTO);
        return ApiResponse.success(localeMessage.getMessage(RequirementBizConstants.COMMON_SUCCESS),reqDTO.getRequirementNo());
    }

    @Override
    public ApiResponse<String> distributeByPartsNo(RequirementPartsDistributeReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(RequirementBizConstants.COMMON_SUCCESS),
                partsService.distribute(reqDTO));
    }

    @Override
    public ApiResponse<String> recCreateRequirementParts(RequirementPartsReCreateReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(RequirementBizConstants.COMMON_SUCCESS),
                partsService.reCreateRequirementParts(reqDTO));
    }

    @Override
    public ApiResponse<String> borrowedRequirementParts(RequirementPartsBorrowReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage("requirement.borrowed.success"),
                partsService.borrowedRequirementParts(reqDTO));
    }

    @Override
    public ApiResponse<List<RequirementSimpleRespDTO>> queryRequirements(RequirementPartsQueryReqDTO param) {
        return ApiResponse.success(partsService.queryRequirements(param));
    }

    @Override
    public ApiResponse<List<RequirementSimpleRespDTO>> queryPendingByNos(List<String> requirementPartsNos) {
        return ApiResponse.success(partsService.queryPendingByNos(requirementPartsNos));
    }


    @Override
    public ApiResponse<Void> updateSqe(RequirementPartsSqeUpdateReqDTO reqDTO) {
        partsService.updateSqe(reqDTO);
        return ApiResponse.success();
    }
}
