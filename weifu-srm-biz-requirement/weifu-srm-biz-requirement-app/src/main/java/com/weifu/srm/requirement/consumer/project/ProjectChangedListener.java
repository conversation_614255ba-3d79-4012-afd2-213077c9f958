package com.weifu.srm.requirement.consumer.project;

import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.mq.constans.KafkaRetryConstants;
import com.weifu.srm.requirement.manager.project.ProjectTaskManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

/**
 * 监听准入邀请审批完成， 进行业务逻辑处理
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectChangedListener {

    private final ProjectTaskManager projectTaskManager;

    @KafkaListener(topics = AuditTopicConstants.TICKET_STATUS_CHANGED, groupId = "project",
            containerFactory = KafkaRetryConstants.RETRY_HANDLER_CONTAINER_FACTORY)
    public void listen(String message) {
        TicketStatusChangedMQ mq = JacksonUtil.json2Bean(message, TicketStatusChangedMQ.class);
        log.info("logic consumer topic=【{}】， group = project， businessNo=【{}】，status =【{}】",
                AuditTopicConstants.TICKET_STATUS_CHANGED, mq.getBusinessNo(), mq.getStatus());
        if(TicketStatusEnum.APPROVING.getCode().equals(mq.getStatus())){
            return;
        }
        projectTaskManager.changedTicket(mq.getBusinessNo(), mq.getStatus(), mq.getOperateBy(), mq.getOperateName());
    }
}
