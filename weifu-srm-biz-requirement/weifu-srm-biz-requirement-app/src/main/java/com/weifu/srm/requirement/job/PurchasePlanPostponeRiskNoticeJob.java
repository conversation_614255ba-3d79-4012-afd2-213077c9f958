package com.weifu.srm.requirement.job;

import com.weifu.srm.requirement.service.PartsPurchasePlanService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class PurchasePlanPostponeRiskNoticeJob {

    @Value("${environment.params.parts-purchase-plan-risk-delay:3L}")
    private Long delayDays;

    private final PartsPurchasePlanService partsPurchasePlanService;

    /**
     * 采购计划节点延期风险通知(提前几天通知)
     */
    @XxlJob("PurchasePlanPostponeRiskNotice-Requirement")
    public void runTask() {
        XxlJobHelper.log("PurchasePlanPostponeRiskNotice#runTask()");
        try {
            partsPurchasePlanService.scheduleForPurchasePlanPostponeRiskNotice(delayDays);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            log.error("采购计划节点延期风险通知(提前几天通知)任务执行异常", e);
            XxlJobHelper.handleFail("异常信息：" + e.getMessage());
        }
    }
}
