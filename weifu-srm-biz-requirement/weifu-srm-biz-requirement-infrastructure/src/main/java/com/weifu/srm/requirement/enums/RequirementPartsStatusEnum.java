package com.weifu.srm.requirement.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/8/12 11:37
 * @Description
 * @Version 1.0
 */
@Getter
public enum RequirementPartsStatusEnum {

    DRAFT("DRAFT", "Draft", "编制中"),
    APPROVING("APPROVING", "Approving", "审核中"),
    WAITING_BP_DISTRIBUTE("WAITING_BP_DISTRIBUTE", "Waiting BP Distribute", "待BP分发"),
    WAITING_MASTER_DISTRIBUTE("WAITING_MASTER_DISTRIBUTE", "Waiting Master Distribute", "待组长分发"),
    PENDING("PENDING", "Pending", "待执行"),
    CPE_RETURN("CPE_RETURN", "CPE Return", "CPE已退回"),
    MASTER_RETURN("MASTER_RETURN", "Master Return", "组长已退回"),
    BP_RETURN("BP_RETURN", "BP Return", "BP退回"),
    CLOSED("CLOSED", "Closed", "已关闭"),
    RECREATED("RECREATED", "Recreated", "已重新发起"),
    PROCESSING("PROCESSING", "Processing", "执行中"),
    COMPLETED("COMPLETED", "Completed", "已完成"),
    ;

    private String code;

    String t = "{\"orderNo\":\"ORD20241105000011\",\"status\":\"VALIDATION\",\"sampleOrderLines\":[{\"partsRequirementNo\":\"PRQ202410290003_7\"}],\"removeSampleOrderLine\":[],\"orderSource\":\"REQUIREMENT\",\"changeTime\":\"2024-11-05 13:52:00\",\"operateBy\":219,\"operateName\":\"李艳青\"}";

    private String englishName;

    private String chineseName;

    private RequirementPartsStatusEnum(String code, String englishName, String chineseName) {
        this.code = code;
        this.englishName = englishName;
        this.chineseName = chineseName;
    }

    private static final Map<String, RequirementPartsStatusEnum> lookup = new HashMap<>();

    static {
        for (RequirementPartsStatusEnum mode : values()) {
            lookup.put(mode.getCode(), mode);
        }
    }

    public static RequirementPartsStatusEnum getByCode(String code) {
        return lookup.get(code);
    }

    public static final List<String> VALID_STATUS = List.of(BP_RETURN.getCode(),WAITING_BP_DISTRIBUTE.getCode(),
            WAITING_MASTER_DISTRIBUTE.getCode(),MASTER_RETURN.getCode(),PENDING.getCode(),CPE_RETURN.getCode(),
            PROCESSING.getCode(),COMPLETED.getCode());

    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}
