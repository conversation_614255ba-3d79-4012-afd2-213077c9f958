package com.weifu.srm.requirement.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/12 11:37
 * @Description
 * @Version 1.0
 */
@Getter
public enum RecommendSupplierTypeEnum {
    NORMAL_RECOMMEND("NORMAL_RECOMMEND", "Normal Recommend", "普通推荐"),
    CUSTOMER_SPECIFIED("CUSTOMER_SPECIFIED", "Customer Specified", "客户指定")
    ;


    private String code;

    private String englishName;

    private String chineseName;


    private RecommendSupplierTypeEnum(String code, String englishName, String chineseName) {
        this.code = code;
        this.englishName = englishName;
        this.chineseName = chineseName;
    }

    private static final Map<String, RecommendSupplierTypeEnum> lookup = new HashMap<>();

    static {
        for (RecommendSupplierTypeEnum mode : values()) {
            lookup.put(mode.getCode(), mode);
        }
    }

    public static RecommendSupplierTypeEnum getByCode(String code) {
        return lookup.get(code);
    }

    public static Set<String> getChineseNameSet(){
       return Arrays.stream(values()).map(RecommendSupplierTypeEnum::getChineseName).collect(Collectors.toSet());
    }
    public static String getSupplierTypeCode(Object chineseName){
        for (RecommendSupplierTypeEnum typeEnum:values()){
            if(typeEnum.chineseName.equals(chineseName)){
                return typeEnum.getCode();
            }
        }
        return null;
    }
}
