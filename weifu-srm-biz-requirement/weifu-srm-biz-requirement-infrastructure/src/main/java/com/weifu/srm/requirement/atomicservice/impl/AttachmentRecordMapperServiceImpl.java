package com.weifu.srm.requirement.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.requirement.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.requirement.enums.AttachmentRecordEnum;
import com.weifu.srm.requirement.mapper.AttachmentRecordMapper;
import com.weifu.srm.requirement.po.AttachmentRecordPO;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AttachmentRecordMapperServiceImpl extends ServiceImpl<AttachmentRecordMapper, AttachmentRecordPO>
                                                implements AttachmentRecordMapperService {
    private final AttachmentRecordMapper attachmentRecordMapper;
    @Override
    public Boolean deleteByBizNo(String bizNo, String bizType) {
        return attachmentRecordMapper.deleteByBizNo(bizNo, bizType);
    }

    @Override
    public Boolean deleteByBizNos(List<String> bizNos, List<String> bizTypes) {
        return attachmentRecordMapper.deleteByBizNos(bizNos, bizTypes);
    }

    @Override
    public List<AttachmentRecordPO> getAttachments(List<String> bizNos) {
        return this.lambdaQuery()
                .in(AttachmentRecordPO::getBusinessNo, bizNos)
                .eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
    }

    /**
     * 清除总需求保存或提交时，零件需求下会存在的附件
     * 1.其他技术附件
     * 2.客户指定供应商说明
     * 3.物流及包装说明
     */
    @Override
    public void clearAttachmentForRequirementPart(List<RequirementPartsPO> oldParts) {
        if (CollectionUtils.isEmpty(oldParts)) {
            return;
        }

        List<String> partNos = oldParts.stream().map(RequirementPartsPO::getRequirementPartsNo).collect(Collectors.toList());
        this.deleteByBizNos(partNos, List.of(
                AttachmentRecordEnum.OTHER_TECH_ATTACHMENT.getKey(),
                AttachmentRecordEnum.CUSTOMER_SPECIFIED_SUPPLIER_DESC.getKey(),
                AttachmentRecordEnum.LOGISTICS_AND_PACKAGING_DESC.getKey()
        ));
    }

}
