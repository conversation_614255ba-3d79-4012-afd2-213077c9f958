package com.weifu.srm.requirement.atomicservice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.requirement.atomicservice.RequirementMapperService;
import com.weifu.srm.requirement.enums.RequirementTypeEnum;
import com.weifu.srm.requirement.enums.project.ProjectSubmitTypeEnum;
import com.weifu.srm.requirement.mapper.RequirementMapper;
import com.weifu.srm.requirement.po.RequirementPO;
import com.weifu.srm.requirement.request.RequirementQueryPageReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:37
 * @Description
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequirementMapperServiceImpl extends ServiceImpl<RequirementMapper, RequirementPO>
        implements RequirementMapperService {
    private static final String MANAGED_CATEGORY_ON_CPE_A_CPE_MASTER = "MANAGED_CATEGORY_ON_CPE_A_CPE_MASTER";
    private static final String CREATOR_A_MANAGED_CATEGORY_ON_CPE_A_CPE_MASTER = "CREATOR_A_MANAGED_CATEGORY_ON_CPE_A_CPE_MASTER";
    private static final String MANAGED_DIVISION = "MANAGED_DIVISION";
    private static final String CREATOR_A_MANAGED_DIVISION = "CREATOR_A_MANAGED_DIVISION";
    private static final String CREATOR = "CREATOR";
    private static final String ALL = "ALL";


    private static final String SOME = "SOME";

    @Override
    public Page<RequirementPO> queryPage(Page<RequirementPO> page, RequirementQueryPageReqDTO reqDTO, DataPermissionRespDTO dataPermission) {
        LambdaQueryWrapper<RequirementPO> queryWrapper = getRequirementPOLambdaQueryWrapper(reqDTO, dataPermission);
        return this.page(page, queryWrapper);
    }

    public static LambdaQueryWrapper<RequirementPO> getRequirementPOLambdaQueryWrapper(RequirementQueryPageReqDTO reqDTO, DataPermissionRespDTO dataPermission) {
        LambdaQueryWrapper<RequirementPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(!StringUtil.isNullOrEmpty(reqDTO.getRequirementType()), RequirementPO::getRequirementType, reqDTO.getRequirementType());
        queryWrapper.like(!StringUtil.isNullOrEmpty(reqDTO.getRequirementApplyName()), RequirementPO::getCreateName, reqDTO.getRequirementApplyName());
        queryWrapper.eq(!StringUtil.isNullOrEmpty(reqDTO.getPurchaseRequirementType()), RequirementPO::getPurchaseRequirementType, reqDTO.getPurchaseRequirementType());
        queryWrapper.in(!CollUtil.isEmpty(reqDTO.getPurchaseOrgCode()), RequirementPO::getPurchaseOrgCode, reqDTO.getPurchaseOrgCode());
        queryWrapper.eq(!ObjectUtil.isNull(reqDTO.getIsRelateProject()), RequirementPO::getIsRelateProject, reqDTO.getIsRelateProject());
        queryWrapper.eq(!ObjectUtil.isNull(reqDTO.getStatus()), RequirementPO::getStatus, reqDTO.getStatus());
        queryWrapper.ge(reqDTO.getCreateTimeStart() != null, RequirementPO::getCreateTime, reqDTO.getCreateTimeStart());
        queryWrapper.le(reqDTO.getCreateTimeEnd() != null, RequirementPO::getCreateTime, reqDTO.getCreateTimeEnd());
        if (!StringUtil.isNullOrEmpty(reqDTO.getKeyWord())) {
            queryWrapper.and(r -> r.or(j -> j.like(RequirementPO::getRequirementNo, reqDTO.getKeyWord()))
                    .or(j -> j.like(RequirementPO::getProjectName, reqDTO.getKeyWord()))
                    .or(j -> j.like(RequirementPO::getRequirementDesc, reqDTO.getKeyWord()))
                    .or(j -> j.like(RequirementPO::getRequirementDesc, reqDTO.getKeyWord()))
                    .or(j -> j.like(RequirementPO::getCreateName, reqDTO.getKeyWord())));
        }
        log.info("dataPermission :"+dataPermission);
        if (SOME.equals(dataPermission.getType())) {
            queryWrapper.and(r -> {
                for (DataPermissionRespDTO.DataPermissionKeyRespDTO key : dataPermission.getKeys()) {

                    master(reqDTO, r, key);
                    managedDivision(r, key);
                    if (CREATOR.equals(key.getKey())) {
                        r.or(j -> j.and(wrapper -> wrapper.in(RequirementPO::getCreateBy, reqDTO.getOperationBy())
                                )
                        );
                    }
                    setDivisionIds(reqDTO, r, key);

                }
            });
        }
        queryWrapper.orderByDesc(RequirementPO::getCreateTime);
        return queryWrapper;
    }

    private static void master(RequirementQueryPageReqDTO reqDTO, LambdaQueryWrapper<RequirementPO> r, DataPermissionRespDTO.DataPermissionKeyRespDTO key) {
        if (MANAGED_CATEGORY_ON_CPE_A_CPE_MASTER.equals(key.getKey())) {
            r.or(wrapper -> wrapper.in(RequirementPO::getOperationByRole,
                            List.of(ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.getKey(),
                                    ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.getKey()))
                    .in(RequirementPO::getOperationByThreeCategoryCode, key.getCategoryCodes()));
        }
        if (CREATOR_A_MANAGED_CATEGORY_ON_CPE_A_CPE_MASTER.equals(key.getKey())) {
            r.or(wrapper -> wrapper.in(RequirementPO::getOperationByRole,
                                    List.of(ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.getKey(),
                                            ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.getKey()))
                            .in(RequirementPO::getOperationByThreeCategoryCode, key.getCategoryCodes()))
                    .or(wrapper -> wrapper.eq(RequirementPO::getCreateBy, reqDTO.getOperationBy()));
        }
    }

    private static void managedDivision(LambdaQueryWrapper<RequirementPO> r, DataPermissionRespDTO.DataPermissionKeyRespDTO key) {
        if (MANAGED_DIVISION.equals(key.getKey()) && !CollUtil.isEmpty(key.getDivisionIds())) {
            r.or(j -> j.and(wrapper -> wrapper
                            .in(RequirementPO::getSubsidiaryCode, key.getDivisionIds())
                            .in(RequirementPO::getOperationByRole,
                                    List.of(ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.getKey(),
                                            ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.getKey(),
                                            ProjectSubmitTypeEnum.PROJECT_SUBMIT_PURCHASE.getKey(),
                                            ProjectSubmitTypeEnum.PROJECT_SUBMIT_PURCHASE_LEADER.getKey(),
                                            ProjectSubmitTypeEnum.REQUIREMENT.getKey()
                                    )
                            )
                    )
            );
        }
    }

    private static void setDivisionIds(RequirementQueryPageReqDTO reqDTO, LambdaQueryWrapper<RequirementPO> r, DataPermissionRespDTO.DataPermissionKeyRespDTO key) {
        if (CREATOR_A_MANAGED_DIVISION.equals(key.getKey())) {
            if (!CollUtil.isEmpty(key.getDivisionIds())) {
                r.or(j -> j.and(wrapper -> wrapper
                                .in(RequirementPO::getSubsidiaryCode, key.getDivisionIds())
                                .in(RequirementPO::getOperationByRole,
                                        List.of(ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.getKey(),
                                                ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.getKey(),
                                                ProjectSubmitTypeEnum.PROJECT_SUBMIT_PURCHASE.getKey(),
                                                ProjectSubmitTypeEnum.PROJECT_SUBMIT_PURCHASE_LEADER.getKey(),
                                                ProjectSubmitTypeEnum.REQUIREMENT.getKey()
                                        )
                                )
                        )
                );
            }
            r.or(wrapper -> wrapper.eq(RequirementPO::getCreateBy, reqDTO.getOperationBy()));
        }
    }

    @Override
    public List<String> getRequirementNosByProjectNo(List<String> projectNo) {
        List<RequirementPO> list = getRequirementPOByProjectNo(projectNo);
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        return list.stream().map(RequirementPO::getRequirementNo).collect(Collectors.toList());
    }

    @Override
    public List<RequirementPO> getRequirementPOByProjectNo(List<String> projectNo) {
        return this.lambdaQuery()
                .in(RequirementPO::getProjectNo, projectNo)
                .eq(RequirementPO::getRequirementType, RequirementTypeEnum.SOURCING.getCode())
                .eq(RequirementPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
    }

    @Override
    public RequirementPO getByNo(String requirementNo) {
        return lambdaQuery().eq(RequirementPO::getRequirementNo, requirementNo)
                .eq(RequirementPO::getIsDelete, YesOrNoEnum.NO.getCode()).one();
    }

    @Override
    public List<RequirementPO> listByNo(List<String> requirementNos) {
        if (CollectionUtils.isEmpty(requirementNos)) {
            return new ArrayList<>();
        }
        return lambdaQuery().in(RequirementPO::getRequirementNo, requirementNos)
                .eq(RequirementPO::getIsDelete, YesOrNoEnum.NO.getCode()).list();
    }

}
