package com.weifu.srm.requirement.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.mapper.RequirementPartsPurchasePlanMapper;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import org.springframework.stereotype.Service;

@Service
public class RequirementPartsPurchasePlanMapperServiceImpl
        extends ServiceImpl<RequirementPartsPurchasePlanMapper, RequirementPartsPurchasePlanPO>
        implements RequirementPartsPurchasePlanMapperService {

    @Override
    public RequirementPartsPurchasePlanPO getByRequirementPartsNo(String requirementPartsNo) {
        return lambdaQuery().eq(RequirementPartsPurchasePlanPO::getRequirementPartsNo, requirementPartsNo)
                .eq(RequirementPartsPurchasePlanPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
    }

}
