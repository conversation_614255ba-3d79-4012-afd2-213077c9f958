package com.weifu.srm.requirement.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "attachment_record")
public class AttachmentRecordPO extends BaseEntity implements Serializable {

    /** 业务编号(统一社会信用代码/准入邀请单号/准入调查表编号) */
    private String businessNo ;
    /** 业务类型(自定义使用) 1.项目附件，2.项目时间变更附件，3.终止附件*/
    private String businessType ;
    /** 文件名称 */
    private String fileName ;
    /** 文件真实名称 */
    private String fileOriginalName ;
    /** 文件URL */
    private String fileUrl ;



}
