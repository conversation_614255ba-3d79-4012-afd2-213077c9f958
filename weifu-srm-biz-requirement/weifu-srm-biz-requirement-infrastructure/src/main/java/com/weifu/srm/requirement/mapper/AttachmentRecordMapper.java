package com.weifu.srm.requirement.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.requirement.po.AttachmentRecordPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AttachmentRecordMapper extends BaseMapper<AttachmentRecordPO> {
    @Delete("delete from attachment_record where business_no = #{bizNo} and business_type = #{bizType}")
    Boolean deleteByBizNo(@Param("bizNo") String bizNo, @Param("bizType") String bizType);

    Boolean deleteByBizNos(@Param("bizNos") List<String> bizNos,@Param("bizTypes") List<String> bizTypes);
}
