package com.weifu.srm.requirement.atomicservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.requirement.po.ProjectPO;
import com.weifu.srm.requirement.request.project.ProjectPageReqDTO;

import java.util.List;

public interface ProjectMapperService extends IService<ProjectPO> {

    Page<ProjectPO> queryList(Page<ProjectPO> page, ProjectPageReqDTO projectPO);

    List<ProjectPO> exportList(ProjectPageReqDTO reqDTO);
}
