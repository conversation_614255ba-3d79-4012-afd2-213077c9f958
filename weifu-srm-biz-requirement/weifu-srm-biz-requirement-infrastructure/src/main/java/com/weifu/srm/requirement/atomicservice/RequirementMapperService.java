package com.weifu.srm.requirement.atomicservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.requirement.po.RequirementPO;
import com.weifu.srm.requirement.request.RequirementQueryPageReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:35
 * @Description
 * @Version 1.0
 */
public interface RequirementMapperService extends IService<RequirementPO> {

    Page<RequirementPO> queryPage(Page<RequirementPO> page,
                                   RequirementQueryPageReqDTO reqDTO,
                                   DataPermissionRespDTO dataPermission);

    List<String> getRequirementNosByProjectNo(List<String> projectNo);

    List<RequirementPO> getRequirementPOByProjectNo(List<String> projectNo);

    RequirementPO getByNo(String requirementNo);

    List<RequirementPO> listByNo(List<String> requirementNos);

}
