package com.weifu.srm.requirement.utils;

import cn.hutool.core.bean.BeanUtil;
import com.weifu.srm.requirement.enums.project.ProjecTimeStageEnum;
import com.weifu.srm.requirement.po.ProjectTimePO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ProjectTimeUtils {
    private ProjectTimeUtils() {
        throw new IllegalStateException("Utility class");
    }


    /**
     * 获取项目计划的最新的计划时间
     * @param timePOList 计划时间list
     * @return 最新的计划时间
     */
    public static ProjectTimePO getLastTime(List<ProjectTimePO> timePOList){
        Map<String, List<ProjectTimePO>> timeMap = timePOList.stream()
                .collect(Collectors.groupingBy(ProjectTimePO::getStageType));
        log.info("queryProjectPlanTime getLastTime, timeMap is 【{}】", timeMap);
        ProjectTimePO lastPlanTime = new ProjectTimePO();
        ProjectTimePO planTime = timeMap.get(ProjecTimeStageEnum.TIME_STAGE_PLAN.getKey()).get(0);
        BeanUtil.copyProperties(planTime, lastPlanTime);
        if(timeMap.containsKey(ProjecTimeStageEnum.TIME_STAGE_CHANGE.getKey())){
            ProjectTimePO changeTime = timeMap.get(ProjecTimeStageEnum.TIME_STAGE_CHANGE.getKey()).get(0);

            BeanUtil.copyProperties(changeTime, lastPlanTime);
        }
        return lastPlanTime;
    }

}
