package com.weifu.srm.requirement.atomicservice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.requirement.atomicservice.ProjectMapperService;
import com.weifu.srm.requirement.enums.project.ProjectStatusEnum;
import com.weifu.srm.requirement.mapper.ProjectMapper;
import com.weifu.srm.requirement.po.ProjectPO;
import com.weifu.srm.requirement.request.project.ProjectPageReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ProjectMapperServiceImpl extends ServiceImpl<ProjectMapper, ProjectPO> implements ProjectMapperService {

    @Override
    public Page<ProjectPO> queryList(Page<ProjectPO> page, ProjectPageReqDTO reqDTO) {
        LambdaQueryWrapper<ProjectPO> queryWrapper = getProjectPOLambdaQueryWrapper(reqDTO);
        return this.page(page,queryWrapper);
    }

    @Override
    public List<ProjectPO> exportList(ProjectPageReqDTO reqDTO) {
        return this.list(getProjectPOLambdaQueryWrapper(reqDTO));
    }

    private static LambdaQueryWrapper<ProjectPO> getProjectPOLambdaQueryWrapper(ProjectPageReqDTO reqDTO) {
        log.info("getProjectPOLambdaQueryWrapper reqDTO is 【{}】", reqDTO);
        LambdaQueryWrapper<ProjectPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(ObjectUtil.isNotEmpty(reqDTO.getStartDate()), ProjectPO::getCreateTime, reqDTO.getStartDate())
                    .le(ObjectUtil.isNotEmpty(reqDTO.getEndDate()), ProjectPO::getCreateTime, reqDTO.getEndDate())
        .like(ObjectUtil.isNotEmpty(reqDTO.getSubsidiaryName()),
                                    ProjectPO::getSubsidiaryName, reqDTO.getSubsidiaryName())
        .like(ObjectUtil.isNotEmpty(reqDTO.getDepartmentName()),
                                    ProjectPO::getDepartmentName, reqDTO.getDepartmentName())

        .like(ObjectUtil.isNotEmpty(reqDTO.getProjectLeaderName()),
                                    ProjectPO::getProjectLeaderName, reqDTO.getProjectLeaderName())
        .like(ObjectUtil.isNotEmpty(reqDTO.getProjectLeaderName()),
                                    ProjectPO::getProjectLeaderName, reqDTO.getProjectLeaderName())
        .eq(ObjectUtil.isNotEmpty(reqDTO.getSubmitByRole()), ProjectPO::getSubmitByRole, reqDTO.getSubmitByRole())
        // 项目状态
        .eq(ObjectUtil.isNotEmpty(reqDTO.getStatus())
            && ObjectUtil.isNotEmpty(ProjectStatusEnum.statOf(reqDTO.getStatus())),
                                    ProjectPO::getStatus, reqDTO.getStatus())
        // 项目类型
        .eq(ObjectUtil.isNotEmpty(reqDTO.getProjectType()), ProjectPO::getProjectType, reqDTO.getProjectType())

        // keyword 项目名称、SRM项目编号、需求项目经理
        .and(ObjectUtil.isNotEmpty(reqDTO.getSearchKey()),
                wrapper -> wrapper.or(wrapper1 -> wrapper1.like(ProjectPO::getProjectName, reqDTO.getSearchKey()))
                        .or(wrapper1 -> wrapper1.like(ProjectPO::getProjectNo, reqDTO.getSearchKey()))
                        .or(wrapper1 -> wrapper1.like(ProjectPO::getProjectManagerName, reqDTO.getSearchKey())));
        // 数据权限
        // 不等于all的就拼接条件
        if (!ObjectUtil.equals(YesOrNoEnum.YES.getCode(), reqDTO.getIsAll())) {
            queryWrapper.and(ObjectUtil.isNotEmpty(reqDTO.getCategorySet())
                            || ObjectUtil.isNotEmpty(reqDTO.getSubsidiaryCodeSet())
                            || ObjectUtil.isNotEmpty(reqDTO.getUserId()),
                    wrapper -> wrapper.or(ObjectUtil.isNotEmpty(reqDTO.getCategorySet()),
                                    wrapper1 -> wrapper1.in(ProjectPO::getSubmitByThreeCategory, reqDTO.getCategorySet()))
                            .or(ObjectUtil.isNotEmpty(reqDTO.getSubsidiaryCodeSet()),
                                    wrapper1 -> wrapper1.in(ProjectPO::getSubsidiaryCode, reqDTO.getSubsidiaryCodeSet()))
                            .or(ObjectUtil.isNotEmpty(reqDTO.getLeaderId()),
                                    wrapper1 -> wrapper1.in(ProjectPO::getProjectLeaderId, reqDTO.getLeaderId()))
            );
        }

        queryWrapper.eq(ProjectPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .orderByDesc(ProjectPO::getCreateTime);
        return queryWrapper;
    }

}
