package com.weifu.srm.requirement.enums.project;

public enum ProjectInitiationPhaseEnum {
    INITIATION_PHASE_CONCEPT("CONCEPT", "概念"),
    INITIATION_PHASE_A("A", "A阶段"),
    INITIATION_PHASE_B("B", "B阶段"),
    INITIATION_PHASE_C("C", "C阶段"),
    INITIATION_PHASE_D("D", "D阶段"),
    INITIATION_PHASE_MASS( "MASS", "批产阶段");


    private final String key;
    private final String value;

    private ProjectInitiationPhaseEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static ProjectInitiationPhaseEnum statOf(String key) {
        for (ProjectInitiationPhaseEnum projectStatusEnum : values()){
            if (projectStatusEnum.getKey().equals(key))
                return projectStatusEnum;
        }
        return null;
    }

    public static String getValueByKey(String key) {
        for (ProjectInitiationPhaseEnum projectStatusEnum : values()){
            if (projectStatusEnum.getKey().equals(key))
                return projectStatusEnum.getValue();
        }
        return null;
    }
}
