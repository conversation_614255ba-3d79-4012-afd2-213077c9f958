package com.weifu.srm.requirement.po;
import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 * @Date 2024/8/12 10:31
 * @Description 零件需求与图纸关联关系表
 * @Version 1.0
 */
@ApiModel(value = "零件需求图纸关联关系表",description = "")
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("requirement_parts_drawing")
public class RequirementPartsDrawingPO extends BaseEntity implements Serializable{
    @ApiModelProperty(value = "零件需求编号",notes = "")
    private String requirementPartsNo ;
    @ApiModelProperty(value = "物料号",notes = "")
    private String materialCode;
    @ApiModelProperty(value = "类型;mannual-人工 ，windchill-windchill",notes = "")
    private String type;
    @ApiModelProperty(value = "windchill推送的基线ID",notes = "")
    private String windchillId;
    @ApiModelProperty(value = "图纸编号",notes = "")
    private String drawingNo ;
    @ApiModelProperty(value = "图纸版本号",notes = "")
    private String drawingVersion ;
    @ApiModelProperty(value = "图纸格式",notes = "")
    private String drawingFormat ;
    @ApiModelProperty(value = "图纸地址",notes = "")
    private String drawingUrl ;
    @ApiModelProperty(value = "图纸名称;人工上传的对应附件表file_ori_name",notes = "")
    private String drawingName ;
    @ApiModelProperty(value = "文件名称（MinIO）",notes = "")
    private String fileName ;
}
