package com.weifu.srm.requirement.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "project")
public class ProjectPO extends BaseEntity implements Serializable {

    /** SRM项目编号 */
    private String projectNo ;
    /** 项目编编码 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectCode ;
    /** 项目状态：正常（灰），完成（绿），延期（红、黄） */
    private String projectStatus;
    /** 项目状态 :编制中，审核中，执行中，终止中，已终止，已完成 */
    private String status;
    /** 事业部/子公司id */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String subsidiaryCode ;
    /** 事业部/子公司名称 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String subsidiaryName ;
    /** 需求部门id */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String departmentCode ;
    /** 需求部门名称 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String departmentName ;
    /** 需求项目经理id */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long projectManagerId ;
    /** 需求项目经理名称 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectManagerName ;
    /** 项目类型： 预研项目、平台开发项目、应用开发项目、已批产项目*/
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectType ;
    /** 项目名称 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectName ;
    /** 客户名称 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customerName ;
    /** 产品名称 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String productName ;
    /** 产品型号 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String productModel ;
    /** 平台名称 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String platformName ;
    /** 发起阶段（概念、A阶段、B阶段、C阶段、D阶段、批产阶段） */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String initiationPhase ;
    /** 项目定级 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectClassification ;
    /** 项目负责人id */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long projectLeaderId ;
    /** 项目负责人名称 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectLeaderName ;
    /** 项目提交人角色 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String submitByRole ;
    /** 项目提交人三级品类 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long submitByThreeCategory ;
    /** 项目提交人三级品类名称 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String submitByThreeCategoryName ;
    /** 备注 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    /** 外购件总目标成本 */
    private BigDecimal outsourceTotalTargetCost;
}
