package com.weifu.srm.requirement.enums.requirement;

import lombok.Getter;

@Getter
public enum RequirementToDoTemplateEnum {

    PART_REQUIREMENT_RETURN_CONTENT("需求退回处理","您分发的${requirement_desc}需求中的${material_code}零件被退回了，退回原因:${return_reason}，快去处理吧~"),
    PART_REQUIREMENT_SYSTEM_DISTRIBUTION("零件需求处理_系统分发", "${applicant}提交的${requirementNo}需求已被审批通过，系统自动分派给您${num}个零件需求，请尽快处理！"),
    PART_REQUIREMENT_BP_TRANSFER("零件需求处理_BP转派", "${requirementPartsNo}零件需求，物料号：${materialCode}已被${operateBy}转派给您，请您尽快处理！"),
    PART_REQUIREMENT_MARK_BORROWED("借用件标记通知", "${operateBy}将${requirementNo}需求中零件需求${requirementPartsNo}，物料号：${materialCode}标记为借用件，请您知晓！"),
    PART_REQUIREMENT_BP_DISTRIBUTION("零件需求处理_BP分发", "${requirementPartsNo}零件需求，物料号：${materialCode}已被${operateBy}分发给您，请您尽快处理！"),
    PART_REQUIREMENT_CPE_MASTER_DISTRIBUTION("零件需求处理_品类组长分发", "${requirementPartsNo}零件需求，物料号：${materialCode}已被${operateBy}分发给您，请您尽快处理！"),
    PART_REQUIREMENT_CPE_TRANSFER("零件需求处理_CPE转派", "${requirementPartsNo}零件需求，物料号：${materialCode}已被${operateBy}转派给您，请您尽快处理！"),
    ;

    private String title;
    private String content;

    private RequirementToDoTemplateEnum(String title, String content) {
        this.title = title;
        this.content = content;
    }

}
