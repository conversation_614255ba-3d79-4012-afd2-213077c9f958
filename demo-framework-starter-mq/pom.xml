<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.demo.framework</groupId>
        <artifactId>demo-framework-starter-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../demo-framework-starter-parent/pom.xml</relativePath>
    </parent>

    <artifactId>demo-framework-starter-mq</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>demo-framework-starter-mq-api</module>
        <module>demo-framework-starter-mq-kafka</module>
        <module>demo-framework-starter-mq-rocket</module>
        <module>demo-framework-starter-mq-rabbit</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>