package com.weifu.srm.supplier.cio.repository.atomicservice.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierAssessmentMapperService;
import com.weifu.srm.supplier.cio.repository.enums.SupplierAssessmentInvoiceStatusEnum;
import com.weifu.srm.supplier.cio.repository.enums.SupplierAssessmentStatusEnum;
import com.weifu.srm.supplier.cio.repository.mapper.SupplierAssessmentMapper;
import com.weifu.srm.supplier.cio.repository.po.SupplierAssessmentPO;
import com.weifu.srm.supplier.cio.request.assessment.SupplierAssessmentPageReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/13 17:57
 * @Description
 * @Version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierAssessmentMapperServiceImpl
        extends ServiceImpl<SupplierAssessmentMapper, SupplierAssessmentPO>
        implements SupplierAssessmentMapperService {
    @Override
    public Page<SupplierAssessmentPO> queryPage(Page<SupplierAssessmentPO> page,
                                                SupplierAssessmentPageReqDTO req,
                                                DataPermissionRespDTO dataPermission) {
        String invoiceStatus = req.getInvoiceStatus();
        if (ObjectUtil.equals(SupplierAssessmentInvoiceStatusEnum.UNVOICED.getCode(), req.getInvoiceStatus())) {
            invoiceStatus = "";
        }
        LambdaQueryWrapper<SupplierAssessmentPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(!StringUtil.isNullOrEmpty(req.getAssessmentNo()), SupplierAssessmentPO::getAssessmentNo, req.getAssessmentNo());
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getSupplierCode()), SupplierAssessmentPO::getSupplierCode, req.getSupplierCode());
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getStatus()), SupplierAssessmentPO::getStatus, req.getStatus());
        // 供应商端检索状态
        // 状态，供应商端检索考核单状态，代供应商反馈；进行中；提前结束，已完成
        queryWrapper.in(!StringUtil.isNullOrEmpty(req.getStatusFromSupplier()), SupplierAssessmentPO::getStatus,
                SupplierAssessmentStatusEnum.getStatusFromSupplier(req.getStatusFromSupplier()));
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getInvoiceStatus()), SupplierAssessmentPO::getInvoiceStatus, invoiceStatus);
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getCategoryCode()), SupplierAssessmentPO::getCategoryCode, req.getCategoryCode());
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getDivision()), SupplierAssessmentPO::getDivision, req.getDivision());
        queryWrapper.ge(!ObjectUtil.isNull(req.getAssessmentTaxAmtFrom()), SupplierAssessmentPO::getAssessmentTaxAmt, req.getAssessmentTaxAmtFrom());
        queryWrapper.le(!ObjectUtil.isNull(req.getAssessmentTaxAmtTo()), SupplierAssessmentPO::getAssessmentTaxAmt, req.getAssessmentTaxAmtTo());
        queryWrapper.eq(!ObjectUtil.isNull(req.getSqeId()), SupplierAssessmentPO::getSqeId, req.getSqeId());
        queryWrapper.eq(!ObjectUtil.isNull(req.getCpeId()), SupplierAssessmentPO::getCpeId, req.getCpeId());
        queryWrapper.ge(!ObjectUtil.isNull(req.getCreateTimeFrom()), SupplierAssessmentPO::getCreateTime, req.getCreateTimeFrom());
        queryWrapper.le(!ObjectUtil.isNull(req.getCreateTimeTo()), SupplierAssessmentPO::getCreateTime, req.getCreateTimeTo());
        queryWrapper.ge(!ObjectUtil.isNull(req.getDeadlineTimeFrom()), SupplierAssessmentPO::getDeadlineTime, req.getDeadlineTimeFrom());
        queryWrapper.le(!ObjectUtil.isNull(req.getDeadlineTimeTo()), SupplierAssessmentPO::getDeadlineTime, req.getDeadlineTimeTo());
        queryWrapper.like(StringUtils.isNotBlank(req.getSqeName()), SupplierAssessmentPO::getSqeName, req.getSqeName());
        queryWrapper.like(StringUtils.isNotBlank(req.getCpeName()), SupplierAssessmentPO::getCpeName, req.getCpeName());
        if (ObjectUtils.isEmpty(dataPermission)){
            // 供应商用户查看当前供应商除 草稿/审批中/审批拒绝 外所有数据
            queryWrapper.eq(SupplierAssessmentPO::getSupplierCode, req.getSupplierCode())
                    .notIn(SupplierAssessmentPO::getStatus,
                            SupplierAssessmentStatusEnum.DRAFT.getCode(),
                            SupplierAssessmentStatusEnum.APPROVING.getCode(),
                            SupplierAssessmentStatusEnum.REJECT.getCode()
                            );
        } else if (DataPermissionRespDTO.PERMISSION_TYPE_ALL.equals(dataPermission.getType())) {
            // 所有ALL权限都不能查看草稿状态数据
            queryWrapper.ne(SupplierAssessmentPO::getStatus, SupplierAssessmentStatusEnum.DRAFT.getCode());
        } else if (CollectionUtils.isNotEmpty(dataPermission.getKeys())
                && DataPermissionRespDTO.PERMISSION_TYPE_SOME.equals(dataPermission.getType())) {
            // SOME权限用户
            queryWrapper.and(w -> addRole(req, dataPermission, w));
        }
        queryWrapper.orderByDesc(SupplierAssessmentPO::getCreateTime);

        return this.page(page, queryWrapper);
    }

    private void addRole(SupplierAssessmentPageReqDTO req, DataPermissionRespDTO dataPermission, LambdaQueryWrapper<SupplierAssessmentPO> w) {
        for (DataPermissionRespDTO.DataPermissionKeyRespDTO key : dataPermission.getKeys()) {
            w.or(r -> {
                if ("MANAGED_CATEGORY".equals(key.getKey())) {
                    r.in(SupplierAssessmentPO::getCategoryCode, key.getCategoryCodes())
                            .ne(SupplierAssessmentPO::getStatus, SupplierAssessmentStatusEnum.DRAFT);
                }
                if ("IS_SQE_DIRECTOR".equals(key.getKey())) {
                    r.or(wrapper -> wrapper
                            .eq(SupplierAssessmentPO::getSqeDirectorId, req.getUserId())
                            .ne(SupplierAssessmentPO::getStatus, SupplierAssessmentStatusEnum.DRAFT));
                }
                if ("CREATOR_A_IS_SQE".equals(key.getKey())) {
                    r.or(wrapper -> wrapper.eq(SupplierAssessmentPO::getSqeId, req.getUserId())
                                    .ne(SupplierAssessmentPO::getStatus, SupplierAssessmentStatusEnum.DRAFT))
                     .or(wrapper -> wrapper.eq(SupplierAssessmentPO::getCreateBy, req.getUserId()));
                }
            });
        }
    }

    @Override
    public SupplierAssessmentPO queryByAssessmentNo(String assessmentNo) {
        return this.getOne(new LambdaQueryWrapper<SupplierAssessmentPO>()
                .eq(SupplierAssessmentPO::getAssessmentNo, assessmentNo)
                .eq(SupplierAssessmentPO::getIsDelete, YesOrNoEnum.NO.getCode()));
    }

    @Override
    public List<SupplierAssessmentPO> queryAssessmentListExport(SupplierAssessmentPageReqDTO req, DataPermissionRespDTO dataPermission) {
        LambdaQueryWrapper<SupplierAssessmentPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getAssessmentNo()), SupplierAssessmentPO::getAssessmentNo, req.getAssessmentNo());
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getSupplierCode()), SupplierAssessmentPO::getSupplierCode, req.getSupplierCode());
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getStatus()), SupplierAssessmentPO::getStatus, req.getStatus());
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getInvoiceStatus()), SupplierAssessmentPO::getInvoiceStatus, req.getInvoiceStatus());
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getCategoryCode()), SupplierAssessmentPO::getCategoryCode, req.getCategoryCode());
        queryWrapper.eq(!StringUtil.isNullOrEmpty(req.getDivision()), SupplierAssessmentPO::getDivision, req.getDivision());
        queryWrapper.ge(!ObjectUtil.isNull(req.getAssessmentTaxAmtFrom()), SupplierAssessmentPO::getAssessmentTaxAmt, req.getAssessmentTaxAmtFrom());
        queryWrapper.le(!ObjectUtil.isNull(req.getAssessmentTaxAmtTo()), SupplierAssessmentPO::getAssessmentTaxAmt, req.getAssessmentTaxAmtTo());
        queryWrapper.eq(!ObjectUtil.isNull(req.getSqeId()), SupplierAssessmentPO::getSqeId, req.getSqeId());
        queryWrapper.eq(!ObjectUtil.isNull(req.getCpeId()), SupplierAssessmentPO::getCpeId, req.getCpeId());
        queryWrapper.ge(!ObjectUtil.isNull(req.getCreateTimeFrom()), SupplierAssessmentPO::getCreateTime, req.getCreateTimeFrom());
        queryWrapper.le(!ObjectUtil.isNull(req.getCreateTimeTo()), SupplierAssessmentPO::getCreateTime, req.getCreateTimeTo());
        queryWrapper.ge(!ObjectUtil.isNull(req.getDeadlineTimeFrom()), SupplierAssessmentPO::getDeadlineTime, req.getDeadlineTimeFrom());
        queryWrapper.le(!ObjectUtil.isNull(req.getDeadlineTimeTo()), SupplierAssessmentPO::getDeadlineTime, req.getDeadlineTimeTo());
        if (CollectionUtils.isNotEmpty(dataPermission.getKeys())
                && "SOME".equals(dataPermission.getType())
                && ObjectUtil.isNull(req.getSupplierCode())) {
            queryWrapper.and(w -> {
                addRoleSQE(req, dataPermission, w);
            });
        }
        return this.list(queryWrapper);
    }

    private void addRoleSQE(SupplierAssessmentPageReqDTO req, DataPermissionRespDTO dataPermission, LambdaQueryWrapper<SupplierAssessmentPO> w) {
        for (DataPermissionRespDTO.DataPermissionKeyRespDTO key : dataPermission.getKeys()) {
            w.or(r -> {
                if ("MANAGED_CATEGORY".equals(key.getKey())) {
                    r.in(SupplierAssessmentPO::getCategoryCode, key.getCategoryCodes())
                            .ne(SupplierAssessmentPO::getStatus, SupplierAssessmentStatusEnum.DRAFT);
                }
                if ("IS_SQE_DIRECTOR".equals(key.getKey())) {
                    r.or(wrapper -> wrapper
                            .eq(SupplierAssessmentPO::getSqeDirectorId, req.getUserId())
                            .ne(SupplierAssessmentPO::getStatus, SupplierAssessmentStatusEnum.DRAFT));
                }
                if ("CREATOR_A_IS_SQE".equals(key.getKey())) {
                    r.or(wrapper -> wrapper.eq(SupplierAssessmentPO::getSqeId, req.getUserId()))
                            .or(wrapper -> wrapper.eq(SupplierAssessmentPO::getCreateBy, req.getUserId()));
                }
            });
        }
    }
}
