package com.weifu.srm.supplier.cio.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.supplier.cio.dto.report.ReportInfoDTO;
import com.weifu.srm.supplier.cio.dto.report.ReportPermissionDTO;
import com.weifu.srm.supplier.cio.dto.report.ReportQueryDTO;
import com.weifu.srm.supplier.cio.entity.ReportInfo;
import com.weifu.srm.supplier.cio.entity.ReportPermission;
import com.weifu.srm.supplier.cio.mapper.ReportApprovalMapper;
import com.weifu.srm.supplier.cio.mapper.ReportInfoMapper;
import com.weifu.srm.supplier.cio.mapper.ReportPermissionMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class ReportInfoServiceImplTest {

    @Mock
    private ReportInfoMapper reportInfoMapper;

    @Mock
    private ReportPermissionMapper reportPermissionMapper;

    @Mock
    private ReportApprovalMapper reportApprovalMapper;

    @InjectMocks
    private ReportInfoServiceImpl reportInfoService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testPageReports() {
        // 准备测试数据
        ReportQueryDTO queryDTO = new ReportQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        queryDTO.setTitle("测试");
        
        Page<ReportInfo> page = new Page<>(1, 10);
        List<ReportInfo> records = new ArrayList<>();
        ReportInfo info = new ReportInfo();
        info.setId(1L);
        info.setReportTitle("测试报表");
        info.setReportCode("TEST001");
        records.add(info);
        page.setRecords(records);
        page.setTotal(1);
        
        // 设置Mock行为
        when(reportInfoMapper.pageReports(any(Page.class), anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(page);
        
        // 执行测试
        IPage<ReportInfoDTO> result = reportInfoService.pageReports(queryDTO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());
        assertEquals("测试报表", result.getRecords().get(0).getReportTitle());
        assertEquals("TEST001", result.getRecords().get(0).getReportCode());
        
        // 验证交互
        verify(reportInfoMapper).pageReports(any(Page.class), eq("测试"), eq(null), eq(null), eq(null), eq(null), eq(null));
    }

    @Test
    public void testGetReportById() {
        // 准备测试数据
        Long reportId = 1L;
        ReportInfo info = new ReportInfo();
        info.setId(reportId);
        info.setReportTitle("测试报表");
        info.setReportCode("TEST001");
        
        List<ReportPermission> permissions = new ArrayList<>();
        ReportPermission permission = new ReportPermission();
        permission.setId(1L);
        permission.setReportId(reportId);
        permission.setPermissionType("ROLE");
        permission.setPermissionValue("ADMIN");
        permissions.add(permission);
        
        // 设置Mock行为
        when(reportInfoMapper.selectById(reportId)).thenReturn(info);
        when(reportPermissionMapper.selectByReportId(reportId)).thenReturn(permissions);
        
        // 执行测试
        ReportInfoDTO result = reportInfoService.getReportById(reportId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("测试报表", result.getReportTitle());
        assertEquals("TEST001", result.getReportCode());
        assertNotNull(result.getPermissions());
        assertEquals(1, result.getPermissions().size());
        assertEquals("ROLE", result.getPermissions().get(0).getPermissionType());
        assertEquals("ADMIN", result.getPermissions().get(0).getPermissionValue());
        
        // 验证交互
        verify(reportInfoMapper).selectById(reportId);
        verify(reportPermissionMapper).selectByReportId(reportId);
    }

    @Test
    public void testCreateReport() {
        // 准备测试数据
        ReportInfoDTO reportDTO = new ReportInfoDTO();
        reportDTO.setReportTitle("新报表");
        reportDTO.setReportCategoryId(1L);
        reportDTO.setBusinessDomainId(1L);
        reportDTO.setReportSource("INTERNAL");
        
        List<ReportPermissionDTO> permissions = new ArrayList<>();
        ReportPermissionDTO permissionDTO = new ReportPermissionDTO();
        permissionDTO.setPermissionType("ROLE");
        permissionDTO.setPermissionValue("ADMIN");
        permissions.add(permissionDTO);
        reportDTO.setPermissions(permissions);
        
        String userId = "testUser";
        
        // 设置Mock行为
        when(reportInfoMapper.insert(any(ReportInfo.class))).thenAnswer(invocation -> {
            ReportInfo info = invocation.getArgument(0);
            info.setId(1L);
            return 1;
        });
        when(reportPermissionMapper.batchInsert(anyList())).thenReturn(1);
        
        // 执行测试
        Long result = reportInfoService.createReport(reportDTO, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result);
        
        // 验证交互
        verify(reportInfoMapper).insert(any(ReportInfo.class));
        verify(reportPermissionMapper).batchInsert(anyList());
    }

    @Test
    public void testUpdateReport() {
        // 准备测试数据
        Long reportId = 1L;
        ReportInfoDTO reportDTO = new ReportInfoDTO();
        reportDTO.setId(reportId);
        reportDTO.setReportTitle("更新报表");
        reportDTO.setReportCategoryId(1L);
        reportDTO.setBusinessDomainId(1L);
        
        List<ReportPermissionDTO> permissions = new ArrayList<>();
        ReportPermissionDTO permissionDTO = new ReportPermissionDTO();
        permissionDTO.setPermissionType("ROLE");
        permissionDTO.setPermissionValue("ADMIN");
        permissions.add(permissionDTO);
        reportDTO.setPermissions(permissions);
        
        String userId = "testUser";
        
        ReportInfo existingReport = new ReportInfo();
        existingReport.setId(reportId);
        existingReport.setReportTitle("原报表");
        
        // 设置Mock行为
        when(reportInfoMapper.selectById(reportId)).thenReturn(existingReport);
        when(reportInfoMapper.updateById(any(ReportInfo.class))).thenReturn(1);
        when(reportPermissionMapper.deleteByReportId(reportId)).thenReturn(1);
        when(reportPermissionMapper.batchInsert(anyList())).thenReturn(1);
        
        // 执行测试
        boolean result = reportInfoService.updateReport(reportId, reportDTO, userId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证交互
        verify(reportInfoMapper).selectById(reportId);
        verify(reportInfoMapper).updateById(any(ReportInfo.class));
        verify(reportPermissionMapper).deleteByReportId(reportId);
        verify(reportPermissionMapper).batchInsert(anyList());
    }

    @Test
    public void testDeleteReport() {
        // 准备测试数据
        Long reportId = 1L;
        String userId = "testUser";
        
        // 设置Mock行为
        when(reportInfoMapper.deleteById(reportId)).thenReturn(1);
        
        // 执行测试
        boolean result = reportInfoService.deleteReport(reportId, userId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证交互
        verify(reportInfoMapper).deleteById(reportId);
    }

    @Test
    public void testUpdateReportStatus() {
        // 准备测试数据
        Long reportId = 1L;
        String status = "PUBLISHED";
        String userId = "testUser";
        
        ReportInfo existingReport = new ReportInfo();
        existingReport.setId(reportId);
        existingReport.setReportStatus("PENDING");
        
        // 设置Mock行为
        when(reportInfoMapper.selectById(reportId)).thenReturn(existingReport);
        when(reportInfoMapper.updateById(any(ReportInfo.class))).thenReturn(1);
        
        // 执行测试
        boolean result = reportInfoService.updateReportStatus(reportId, status, userId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证交互
        verify(reportInfoMapper).selectById(reportId);
        verify(reportInfoMapper).updateById(any(ReportInfo.class));
    }

    @Test
    public void testSubmitReportApproval() {
        // 准备测试数据
        Long reportId = 1L;
        String userId = "testUser";
        
        ReportInfo existingReport = new ReportInfo();
        existingReport.setId(reportId);
        existingReport.setReportStatus("DRAFT");
        
        // 设置Mock行为
        when(reportInfoMapper.selectById(reportId)).thenReturn(existingReport);
        when(reportInfoMapper.updateById(any(ReportInfo.class))).thenReturn(1);
        when(reportApprovalMapper.insert(any())).thenAnswer(invocation -> {
            Object arg = invocation.getArgument(0);
            ((ReportApproval) arg).setId(1L);
            return 1;
        });
        
        // 执行测试
        Long result = reportInfoService.submitReportApproval(reportId, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result);
        
        // 验证交互
        verify(reportInfoMapper).selectById(reportId);
        verify(reportInfoMapper).updateById(any(ReportInfo.class));
        verify(reportApprovalMapper).insert(any());
    }
}