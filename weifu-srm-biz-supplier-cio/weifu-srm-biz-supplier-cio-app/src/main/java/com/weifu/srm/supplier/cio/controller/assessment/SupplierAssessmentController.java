package com.weifu.srm.supplier.cio.controller.assessment;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.supplier.cio.api.SupplierAssessmentApi;
import com.weifu.srm.supplier.cio.request.assessment.*;
import com.weifu.srm.supplier.cio.response.assessment.*;
import com.weifu.srm.supplier.cio.service.SupplierAssessmentService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/13 18:34
 * @Description
 * @Version 1.0
 */
@Api(tags = "供应商索赔接口")
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
public class SupplierAssessmentController implements SupplierAssessmentApi {
    public static final String COMMON_QUERY_RESULT_SUCCESS = "common.query.result.success";
    public static final String COMMON_SUBMIT_RESULT_SUCCESS = "common.submit.result.success";
    private final SupplierAssessmentService supplierAssessmentService;
    private final LocaleMessage localeMessage;
    @Override
    public ApiResponse<PageResponse<SupplierAssessmentPageRespDTO>> queryAssessmentPage(SupplierAssessmentPageReqDTO req) {
        PageResponse<SupplierAssessmentPageRespDTO> pageResult = supplierAssessmentService.queryPage(req);
        return ApiResponse.success(localeMessage.getMessage(COMMON_QUERY_RESULT_SUCCESS),pageResult);
    }

    @Override
    public ApiResponse<String> save(SupplierAssessmentSaveReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage("common.save.result.success"),supplierAssessmentService.saveAssessment(reqDTO));
    }

    @Override
    public ApiResponse<SupplierAssessmentDetailRespDTO> queryByAssessmentNo(String assessmentNo) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_QUERY_RESULT_SUCCESS),supplierAssessmentService.queryByAssessmentNo(assessmentNo));
    }

    @Override
    public ApiResponse<String> submit(SupplierAssessmentSaveReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_QUERY_RESULT_SUCCESS),supplierAssessmentService.submitAssessment(reqDTO));
    }

    @Override
    public ApiResponse<Void> delete(String assessmentNo) {
        return ApiResponse.success(localeMessage.getMessage("common.delete.result.success"),supplierAssessmentService.deleteAssessment(assessmentNo));
    }

    @Override
    public ApiResponse<String> acceptOrNot(SupplierAssessmentAcceptOrNotReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage("common.save.result.success"),supplierAssessmentService.acceptOrNot(reqDTO));

    }

    @Override
    public ApiResponse<String> submitAgain(SupplierAssessmentSaveReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_SUBMIT_RESULT_SUCCESS),supplierAssessmentService.submitAgainAssessment(reqDTO));
    }

    @Override
    public ApiResponse<String> forceAssessment(SupplierAssessmentForceReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_SUBMIT_RESULT_SUCCESS),supplierAssessmentService.forceAssessment(reqDTO));
    }

    @Override
    public ApiResponse<String> earlyEndAssessment(SupplierAssessmentEarlyEndReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_SUBMIT_RESULT_SUCCESS),supplierAssessmentService.earlyEndAssessment(reqDTO));
    }

    @Override
    public ApiResponse<String> turnToCpe(SupplierAssessmentTurnToCPEReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_SUBMIT_RESULT_SUCCESS),supplierAssessmentService.turnToCpe(reqDTO));
    }

    @Override
    public ApiResponse<String> closeAssessment(SupplierAssessmentCloseReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_SUBMIT_RESULT_SUCCESS),supplierAssessmentService.closeAssessment(reqDTO));
    }

    @Override
    public ApiResponse<List<SupplierAssessmentPageRespDTO>> queryAssessmentListExport(SupplierAssessmentPageReqDTO req) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_SUBMIT_RESULT_SUCCESS),supplierAssessmentService.queryAssessmentListExport(req));
    }

    @Override
    public ApiResponse<List<SupplierAssessmentLogRespDTO>> queryAssessmentLog(String assessmentNo) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_QUERY_RESULT_SUCCESS),supplierAssessmentService.queryAssessmentLog(assessmentNo));
    }

    @Override
    public ApiResponse<SupplierAssessmentLogDetailRespDTO> queryAssessmentLogDetail(String assessmentLogId) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_QUERY_RESULT_SUCCESS),supplierAssessmentService.queryAssessmentLogDetail(assessmentLogId));
    }

    @Override
    public ApiResponse<String> uploadClaimDoc(SupplierAssessmentUploadClaimDocReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_SUBMIT_RESULT_SUCCESS),supplierAssessmentService.uploadClaimDoc(reqDTO));
    }

    @Override
    public ApiResponse<String> returnToSqe(SupplierAssessmentReturnToSQEReqDTO reqDTO) {
        return ApiResponse.success(localeMessage.getMessage(COMMON_SUBMIT_RESULT_SUCCESS),supplierAssessmentService.returnToSqe(reqDTO));
    }

    @Override
    public ApiResponse<Void> assessmentBlueInvoiceOpen(SupplierAssessmentBlueInvoiceOpenReqDTO req) {
        supplierAssessmentService.assessmentBlueInvoiceOpen(req);
        return ApiResponse.success();
    }



    @Override
    public ApiResponse<Void> assessmentBlueInvoiceOpenResult(SupplierAssessmentBlueInvoiceOpenResultReqDTO req) {
        supplierAssessmentService.assessmentBlueInvoiceOpenResult(req);
        return ApiResponse.success();
    }


    @Override
    public ApiResponse<List<SupplierAssessmentInvoiceListRespDTO>> assessmentInvoiceHistory(String assessmentNo) {
        List<SupplierAssessmentInvoiceListRespDTO> list = supplierAssessmentService.assessmentInvoiceHistory(assessmentNo);
        return ApiResponse.success(list);
    }

    @Override
    public ApiResponse<SupplierAssessmentInvoiceFullDetailRespDTO> invoiceDetail(Long invoiceId) {
        SupplierAssessmentInvoiceFullDetailRespDTO fullDetail = supplierAssessmentService.invoiceDetail(invoiceId);
        return ApiResponse.success(fullDetail);
    }

    @Override
    public ApiResponse<SupplierAssessmentStatisticsRespDTO> queryAssessmentStatistics(Long userId, String supplierCode) {
        // 暂时仅统计供应商端的数量
        SupplierAssessmentStatisticsRespDTO resp = supplierAssessmentService.queryAssessmentStatistics(userId, supplierCode);
        return ApiResponse.success(resp);
    }
}
