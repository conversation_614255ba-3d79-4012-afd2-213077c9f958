package com.weifu.srm.supplier.cio.service;

import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.cio.request.audit.*;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityOplAdjustmentLogRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityOplTaskDetailRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityOplTaskReportRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityOplTaskRespDTO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商质量审核OPL任务-服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualityOplTaskService {

    /**
     * 分页查询
     */
    PageResponse<SupplierQualityOplTaskRespDTO> page(SupplierQualityOplTaskPageReqDTO reqDTO);
    PageResponse<SupplierQualityOplTaskRespDTO> pageRelation(SupplierQualityOplTaskRelationPageReqDTO reqDTO);

    /**
     * 详情
     */
    SupplierQualityOplTaskDetailRespDTO detail(Long id);

    /**
     * 创建
     */
    Long create(SupplierQualityOplTaskCreateReqDTO reqDTO);

    /**
     * 反馈
     */
    void feedback(SupplierQualityOplTaskFeedbackReqDTO reqDTO);

    /**
     * 审核
     */
    void approve(SupplierQualityOplTaskApproveReqDTO reqDTO);

    /**
     * 撤回
     */
    void withdraw(SupplierQualityOplTaskWithdrawReqDTO reqDTO);

    /**
     * 调整计划时间
     */
    void adjustment(SupplierQualityOplTaskAdjustmentReqDTO reqDTO);

    /**
     * 调整记录分页查询
     */
    List<SupplierQualityOplAdjustmentLogRespDTO> adjustmentLog(String oplNo);

    /**
     * 提醒反馈
     */
    void supplierOplReminderFeedbackJob(Date date);

    /**
     * 创建者需要审核反馈
     */
    void supplierOplReminderReviewFeedbackJob(Date date);

    /**
     * 报表分页查询
     */
    PageResponse<SupplierQualityOplTaskReportRespDTO> pageReport(SupplierQualityOplTaskReportPageReqDTO reqDTO);
}
