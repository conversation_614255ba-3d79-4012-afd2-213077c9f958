package com.weifu.srm.supplier.cio.manager;

import com.weifu.srm.supplier.cio.convert.AttachmentRecordConvert;
import com.weifu.srm.supplier.cio.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.cio.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AttachmentManager {

    private final AttachmentRecordConvert attachmentMessageConvert;
    private final AttachmentRecordMapperService attachmentRecordMapperService;

    public List<AttachmentMessageRespDTO> getAttachmentsByBusinessNoAndBusType(String businessNo, String businessType){
        List<AttachmentRecordPO> list = attachmentRecordMapperService.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessNo, businessNo)
                .eq(AttachmentRecordPO::getBusinessType, businessType)
                .list();
        return attachmentMessageConvert.toAttachmentMessageRespDTO(list);
    }

    public List<AttachmentMessageRespDTO> getAttachmentsByBusinessNoAndBusType(String businessNo, List<String> businessTypes) {
        List<AttachmentRecordPO> list = attachmentRecordMapperService.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessNo, businessNo)
                .in(AttachmentRecordPO::getBusinessType, businessTypes)
                .list();
        return attachmentMessageConvert.toAttachmentMessageRespDTO(list);
    }

    public List<AttachmentRecordPO> getAttachments(String businessNo, List<String> businessTypes) {
        return attachmentRecordMapperService.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessNo, businessNo)
                .in(AttachmentRecordPO::getBusinessType, businessTypes)
                .list();
    }

}
