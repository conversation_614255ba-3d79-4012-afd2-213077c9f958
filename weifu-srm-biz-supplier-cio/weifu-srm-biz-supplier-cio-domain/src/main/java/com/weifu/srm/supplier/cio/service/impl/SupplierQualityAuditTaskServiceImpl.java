package com.weifu.srm.supplier.cio.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.response.CategoryWithAllLevelDTO;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.cio.constants.ServiceConstants;
import com.weifu.srm.supplier.cio.convert.SupplierQualityAuditTaskAdjustConvert;
import com.weifu.srm.supplier.cio.convert.SupplierQualityAuditTaskConvert;
import com.weifu.srm.supplier.cio.enums.*;
import com.weifu.srm.supplier.cio.manager.DataPermissionManager;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierQualityAuditPlanMapperService;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierQualityAuditTaskAdjustMapperService;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierQualityAuditTaskMapperService;
import com.weifu.srm.supplier.cio.repository.constants.SupplierCioCommonConstants;
import com.weifu.srm.supplier.cio.repository.enums.BizNoRuleEnum;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditPlanPO;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditTaskAdjustPO;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditTaskPO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import com.weifu.srm.supplier.cio.request.audit.*;
import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskExcelRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskReportRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskRespDTO;
import com.weifu.srm.supplier.cio.service.SupplierQualityAuditTaskService;
import com.weifu.srm.supplier.cio.service.biz.AttachmentRecordBiz;
import com.weifu.srm.supplier.cio.service.biz.SupplierQualityAuditMessageBiz;
import com.weifu.srm.supplier.cio.utils.BizNoUtil;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商质量审核任务-服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierQualityAuditTaskServiceImpl implements SupplierQualityAuditTaskService {
    public static final String CANCEL = "CANCEL";
    public static final String CONCAT_STR = ":";
    public static final String OPL = "OPL";
    public static final String SUPPLIER = "SUPPLIER";
    private final SupplierQualityAuditTaskMapperService supplierQualityAuditTaskMapperService;
    private final SupplierQualityAuditPlanMapperService supplierQualityAuditPlanMapperService;
    private final SupplierQualityAuditTaskAdjustMapperService supplierQualityAuditTaskAdjustMapperService;
    private final SupplierQualityAuditTaskConvert supplierQualityAuditTaskConvert;
    private final SupplierQualityAuditTaskAdjustConvert supplierQualityAuditTaskAdjustConvert;
    private final AttachmentRecordBiz attachmentRecordBiz;
    private final SupplierQualityAuditMessageBiz supplierQualityAuditMessageBiz;
    private final CategoryApi categoryApi;
    private final DataPermissionManager dataPermissionManager;
    private final LocaleMessage localeMessage;

    @Override
    public PageResponse<SupplierQualityAuditTaskRespDTO> page(SupplierQualityAuditTaskPageReqDTO reqDTO) {
        setPermission(reqDTO);
        Page<SupplierQualityAuditTaskPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        IPage<SupplierQualityAuditTaskRespDTO> pageResult = supplierQualityAuditTaskMapperService.listPageByQuery(page, reqDTO);
        List<SupplierQualityAuditTaskRespDTO> dtoList = pageResult.getRecords();
        for (SupplierQualityAuditTaskRespDTO dto : dtoList) {
            dto.setStatusName(SupplierQualityAuditTaskStatusEnum.getNameByCode(dto.getStatus()));
            dto.setAuditConclusionName(SupplierQualityAuditTaskConclusionEnum.getNameByCode(dto.getAuditConclusion()));
            dto.setIsOverdue(getIsInProgress(dto.getStatus(), dto.getPracticalCompleteDate(), dto.getPredictCompleteDate()));
            fillFieldName(dto);
        }
        fillAnnex(dtoList);
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), dtoList);
    }

    private void fillFieldName(SupplierQualityAuditTaskRespDTO dto) {
        boolean isOther = SupplierQualityAuditPlanSubclassTypeEnum.OTHER.getCode().equals(dto.getAuditSubclassType()) || SupplierQualityAuditPlanSubclassTypeEnum.SPECIAL_REVIEW.getCode().equals(dto.getAuditSubclassType());
        String auditSubclassTypeName = SupplierQualityAuditPlanSubclassTypeEnum.getNameByCode(dto.getAuditSubclassType());
        auditSubclassTypeName = isOther ? auditSubclassTypeName + "(" + dto.getAuditSubclassOther() + ")" : auditSubclassTypeName;
        dto.setAuditSubclassTypeName(auditSubclassTypeName);
        dto.setAuditCategoryTypeName(SupplierQualityAuditPlanCategoryTypeEnum.getNameByCode(dto.getAuditCategoryType()));
    }

    @Override
    public List<SupplierQualityAuditTaskExcelRespDTO> listByQuery(SupplierQualityAuditTaskPageReqDTO reqDTO) {
        setPermission(reqDTO);
        List<SupplierQualityAuditTaskRespDTO> poList = supplierQualityAuditTaskMapperService.listByQuery(reqDTO);
        List<SupplierQualityAuditTaskExcelRespDTO> dtoList = Lists.newArrayList();
        for (SupplierQualityAuditTaskRespDTO dto : poList) {
            dto.setStatusName(SupplierQualityAuditTaskStatusEnum.getNameByCode(dto.getStatus()));
            dto.setAuditConclusionName(SupplierQualityAuditTaskConclusionEnum.getNameByCode(dto.getAuditConclusion()));
            dto.setIsOverdue(getIsInProgress(dto.getStatus(), dto.getPracticalCompleteDate(), dto.getPredictCompleteDate()));
            dto.setCreateTaskStr(createTaskFormat(dto.getCreateTask()));
            fillFieldName(dto);
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdate(String auditPlanNo, List<SupplierQualityAuditTaskReqDTO> taskList, OperatorBaseReqDTO reqDTO, boolean isSubmit) {
        removeTask(auditPlanNo, taskList);
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        Date now = DateUtil.date();
        List<SupplierQualityAuditTaskPO> modifyList = CollectionUtils.isEmpty(taskList) ? Lists.newArrayList() : supplierQualityAuditTaskConvert.toSupplierQualityAuditTaskList(taskList);
        Set<String> repeatSet = Sets.newHashSet();
        for (SupplierQualityAuditTaskPO task : modifyList) {
            task.setAuditPlanNo(auditPlanNo);
            task.setAuditTaskNo(StringUtils.isBlank(task.getAuditTaskNo()) ? BizNoUtil.generateNo(BizNoRuleEnum.SUPPLIER_QUALITY_TASK) : task.getAuditTaskNo());
            task.setStatus(SupplierQualityAuditTaskStatusEnum.NOT_STARTED.getCode());
            if (task.getId() == null) {
                BaseEntityUtil.setCommon(task, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
            } else {
                BaseEntityUtil.setCommonForU(task, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
            }
            if (isSubmit) {
                String taskKey = taskKeyData(task);
                if (repeatSet.contains(taskKey)) {
                    throw new BizFailException(localeMessage.getMessage("supplier.cio.audit.plan.repeat.valid.error", new String[]{task.getSupplierCode()}));

                }
                repeatSet.add(taskKey);
            }
        }
        supplierQualityAuditTaskMapperService.saveOrUpdateBatch(modifyList);
    }

    private String taskKeyData(SupplierQualityAuditTaskPO task) {
        List<String> valueList = Lists.newArrayList();
        valueList.add(task.getSupplierCode());
        valueList.add(task.getAuditProductSeries());
        valueList.add(task.getAuditProductRange());
        valueList.add(task.getThreeCategoryCode());
        valueList.add(String.valueOf(task.getSqeId()));
        valueList.add(task.getDivisionCode());
        valueList.add(DateUtil.format(task.getPredictCompleteDate(), DatePattern.NORM_DATE_PATTERN));
        return StringUtils.join(valueList, CONCAT_STR);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approveResult(SupplierQualityAuditTaskApproveReqDTO reqDTO) {
        SupplierQualityAuditTaskPO po = supplierQualityAuditTaskMapperService.getById(reqDTO.getId());
        po.setAuditOpinion(reqDTO.getAuditOpinion() == null ? "" : reqDTO.getAuditOpinion());
        po.setAuditResult(reqDTO.getAuditResult());
        BaseEntityUtil.setCommonForU(po, reqDTO.getOperationBy(), reqDTO.getOperationByName(), DateUtil.date());
        po.setStatus(SupplierCioCommonConstants.AUDIT_PASS.equals(reqDTO.getAuditResult()) ? SupplierQualityAuditTaskStatusEnum.ENDED.getCode() : SupplierQualityAuditTaskStatusEnum.APPROVE_FAIL.getCode());
        supplierQualityAuditMessageBiz.sendMessageAfterApproveResult(po);
        supplierQualityAuditTaskMapperService.updateById(po);
        if (SupplierCioCommonConstants.AUDIT_PASS.equals(reqDTO.getAuditResult())) {
            //检查所有子任务是否完成，如果已完成则更新计划状态为完成
            po = supplierQualityAuditTaskMapperService.getById(reqDTO.getId());
            completedTask(po, reqDTO);
        }
    }

    private void completedTask(SupplierQualityAuditTaskPO po, OperatorBaseReqDTO reqDTO) {
        boolean isCompleted = supplierQualityAuditTaskMapperService.isCompleted(po.getAuditPlanNo());
        if (isCompleted) {
            supplierQualityAuditPlanMapperService.complete(po.getAuditPlanNo(), reqDTO.getOperationBy(), reqDTO.getOperationByName());
            supplierQualityAuditMessageBiz.sendMessageByAllTaskComplete(po.getAuditPlanNo());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitResult(SupplierQualityAuditTaskResultReqDTO reqDTO) {
        SupplierQualityAuditTaskPO po = supplierQualityAuditTaskMapperService.getById(reqDTO.getId());
        po.setPracticalCompleteDate(reqDTO.getPracticalCompleteDate());
        po.setAuditConclusion(reqDTO.getAuditConclusion());
        po.setAuditScore(reqDTO.getAuditScore());
        po.setAuditRemark(reqDTO.getAuditRemark());
        po.setCreateTask(reqDTO.getCreateTask());
        BaseEntityUtil.setCommonForU(po, reqDTO.getOperationBy(), reqDTO.getOperationByName(), DateUtil.date());
        po.setStatus(SupplierQualityAuditTaskStatusEnum.SUBMITTED_RESULT.getCode());
        supplierQualityAuditTaskMapperService.updateById(po);
        //保存附件
        attachmentRecordBiz.saveBatchAttachmentRecord(reqDTO.getAnnexList(), SupplierQualityAuditTaskAnnexEnum.SUPPLIER_AUDIT_TASK_SUBMIT_ANNEX.getCode(), po.getAuditTaskNo(), reqDTO);
        supplierQualityAuditMessageBiz.sendMessageAfterSubmitResult(po);
    }

    /**
     * 复制任务
     *
     * @param oldAuditPlanNo 旧任务编号
     * @param newAuditPlanNo 新任务编号
     * @param reqDTO         操作人参数
     * @param now            现在时间
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copyTask(String oldAuditPlanNo, String newAuditPlanNo, OperatorBaseReqDTO reqDTO, Date now) {
        List<SupplierQualityAuditTaskPO> taskList = supplierQualityAuditTaskMapperService.listByAuditPlanNo(oldAuditPlanNo);
        for (SupplierQualityAuditTaskPO task : taskList) {
            task.setId(null);
            task.setAuditScore(null);
            task.setAuditResult(null);
            task.setAuditConclusion(null);
            task.setPracticalCompleteDate(null);
            task.setCreateTask(null);
            task.setAuditPlanNo(newAuditPlanNo);
            task.setAuditTaskNo(BizNoUtil.generateNo(BizNoRuleEnum.SUPPLIER_QUALITY_TASK));
            task.setStatus(SupplierQualityAuditTaskStatusEnum.NOT_STARTED.getCode());
            BaseEntityUtil.setCommon(task, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
        }
        supplierQualityAuditTaskMapperService.saveBatch(taskList);
    }

    @Override
    public List<SupplierQualityAuditTaskRespDTO> listByAuditPlanNo(String auditPlanNo) {
        List<SupplierQualityAuditTaskPO> taskList = supplierQualityAuditTaskMapperService.listByAuditPlanNo(auditPlanNo);
        List<SupplierQualityAuditTaskRespDTO> respList = supplierQualityAuditTaskConvert.toSupplierQualityAuditTaskRespList(taskList);
        if (CollectionUtils.isNotEmpty(respList)) {
            fillAnnex(respList);
        }
        return respList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByAuditPlanNo(String auditPlanNo) {
        List<SupplierQualityAuditTaskPO> taskList = supplierQualityAuditTaskMapperService.listByAuditPlanNo(auditPlanNo);
        if (CollectionUtils.isNotEmpty(taskList)) {
            supplierQualityAuditTaskMapperService.removeByIds(taskList.stream().map(SupplierQualityAuditTaskPO::getId).collect(Collectors.toList()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String adjustPetition(SupplierQualityAuditPlanAdjustReqDTO reqDTO, String adjustType) {
        List<SupplierQualityAuditTaskAdjustReqDTO> taskRespList = reqDTO.getTaskList();
        List<Long> idList = taskRespList.stream().map(SupplierQualityAuditTaskAdjustReqDTO::getId).collect(Collectors.toList());
        updateStatus(idList, reqDTO, SupplierQualityAuditTaskStatusEnum.ADJUSTING);
        List<SupplierQualityAuditTaskAdjustPO> adjustList = supplierQualityAuditTaskAdjustConvert.toSupplierQualityAuditTaskAdjustList(taskRespList);
        Date now = DateUtil.date();
        String adjustNo = BizNoUtil.generateNo(BizNoRuleEnum.SUPPLIER_QUALITY_TASK_ADJUST);
        for (SupplierQualityAuditTaskAdjustPO adjust : adjustList) {
            adjust.setId(null);
            adjust.setAdjustNo(adjustNo);
            adjust.setAdjustType(adjustType);
            BaseEntityUtil.setCommon(adjust, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
        }
        supplierQualityAuditTaskAdjustMapperService.saveBatch(adjustList);
        return adjustNo;
    }

    /**
     * 是否跨年判断
     */
    @Override
    public boolean isStrideYear(List<SupplierQualityAuditTaskAdjustReqDTO> taskRespList) {
        List<Long> taskIdList = taskRespList.stream().map(SupplierQualityAuditTaskAdjustReqDTO::getId).collect(Collectors.toList());
        List<SupplierQualityAuditTaskPO> poList = supplierQualityAuditTaskMapperService.listByIds(taskIdList);
        validStatus(poList);
        Map<Long, Date> dateMap = poList.stream().collect(Collectors.toMap(SupplierQualityAuditTaskPO::getId, SupplierQualityAuditTaskPO::getPredictCompleteDate, (k1, k2) -> k2));
        boolean isStrideYear = false;
        for (SupplierQualityAuditTaskAdjustReqDTO req : taskRespList) {
            Date oldDate = dateMap.get(req.getId());
            Date newDate = req.getPredictCompleteDate();
            int oldYear = DateUtil.year(oldDate == null ? newDate : oldDate);
            int adjustYear = DateUtil.year(newDate);
            isStrideYear = oldYear != adjustYear;
            if (isStrideYear) {
                break;
            }
        }
        return isStrideYear;
    }

    @Override
    public void checkStatus(List<Long> taskIdList) {
        List<SupplierQualityAuditTaskPO> poList = supplierQualityAuditTaskMapperService.listByIds(taskIdList);
        validStatus(poList);
    }

    private void validStatus(List<SupplierQualityAuditTaskPO> poList) {
        for (SupplierQualityAuditTaskPO auditTask : poList) {
            if (!SupplierQualityAuditTaskStatusEnum.IN_PROGRESS.getCode().equals(auditTask.getStatus())) {
                throw new BizFailException(localeMessage.getMessage("supplier.cio.audit.plan.adjust.status.error"));
            }
        }
    }

    @Override
    public List<SupplierQualityAuditTaskRespDTO> listByAdjustNo(String adjustNo) {
        List<SupplierQualityAuditTaskAdjustPO> poList = supplierQualityAuditTaskAdjustMapperService.listByAdjustNo(adjustNo);
        return supplierQualityAuditTaskAdjustConvert.toSupplierQualityAuditTaskRespList(poList);
    }

    @Override
    public void inProgress(String auditPlanNo, OperatorBaseReqDTO reqDTO) {
        List<SupplierQualityAuditTaskPO> poList = supplierQualityAuditTaskMapperService.listByAuditPlanNo(auditPlanNo);
        List<Long> idList = poList.stream().map(SupplierQualityAuditTaskPO::getId).collect(Collectors.toList());
        updateStatus(idList, reqDTO, SupplierQualityAuditTaskStatusEnum.IN_PROGRESS);
        supplierQualityAuditMessageBiz.sendMessageByPlanApproved(auditPlanNo, poList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAdjustProgress(TicketStatusChangedMQ mq) {
        if (TicketStatusEnum.APPROVING.equalsCode(mq.getStatus())) {
            return;
        }
        List<SupplierQualityAuditTaskAdjustPO> adjustList = supplierQualityAuditTaskAdjustMapperService.listByAdjustNo(mq.getBusinessNo());
        List<Long> idList = Lists.newArrayList();
        List<String> taskNoList = Lists.newArrayList();
        Map<String, SupplierQualityAuditTaskAdjustPO> adjustMap = Maps.newHashMap();
        for (SupplierQualityAuditTaskAdjustPO adjust : adjustList) {
            idList.add(adjust.getId());
            taskNoList.add(adjust.getAuditTaskNo());
            adjustMap.put(adjust.getAuditTaskNo(), adjust);
        }
        List<SupplierQualityAuditTaskPO> taskList = supplierQualityAuditTaskMapperService.listByAuditTaskNo(taskNoList);
        Date now = DateUtil.date();
        //将审批通过的数据覆盖原来的数据
        for (SupplierQualityAuditTaskPO po : taskList) {
            SupplierQualityAuditTaskAdjustPO adjust = adjustMap.get(po.getAuditTaskNo());
            if (adjust == null) {
                continue;
            }
            processData(adjust, po, mq);
            BaseEntityUtil.setCommonForU(po, adjust.getUpdateBy(), adjust.getUpdateName(), now);
        }
        if (CollectionUtils.isNotEmpty(taskList)) {
            supplierQualityAuditTaskMapperService.updateBatchById(taskList);
            supplierQualityAuditTaskAdjustMapperService.updateAdjustStatus(idList);
            SupplierQualityAuditTaskPO task = supplierQualityAuditTaskMapperService.getByAuditTaskNo(taskList.get(0).getAuditTaskNo());
            OperatorBaseReqDTO reqDTO = new OperatorBaseReqDTO();
            reqDTO.setOperationBy(mq.getOperateBy());
            reqDTO.setOperationByName(mq.getOperateName());
            completedTask(task, reqDTO);
        }

    }

    private void processData(SupplierQualityAuditTaskAdjustPO adjust, SupplierQualityAuditTaskPO po, TicketStatusChangedMQ mq) {
        if (CANCEL.equals(adjust.getAdjustType())) {
            if (TicketStatusEnum.APPROVED.equalsCode(mq.getStatus())) {
                po.setStatus(SupplierQualityAuditTaskStatusEnum.CANCELLED.getCode());
            } else {
                po.setStatus(SupplierQualityAuditTaskStatusEnum.IN_PROGRESS.getCode());
            }
        } else {
            if (TicketStatusEnum.APPROVED.equalsCode(mq.getStatus())) {
                po.setAuditProductSeries(adjust.getAuditProductSeries());
                po.setAuditProductRange(adjust.getAuditProductRange());
                po.setThreeCategoryCode(adjust.getThreeCategoryCode());
                po.setThreeCategoryName(adjust.getThreeCategoryName());
                po.setSqeId(adjust.getSqeId());
                po.setSqeName(adjust.getSqeName());
                po.setDivisionCode(adjust.getDivisionCode());
                po.setDivisionName(adjust.getDivisionName());
                po.setPredictCompleteDate(adjust.getPredictCompleteDate());
            }
            po.setStatus(SupplierQualityAuditTaskStatusEnum.IN_PROGRESS.getCode());
        }
    }

    @Override
    public void supplierAuditPlanRemindJob(Date date) {
        List<String> statusList = Lists.newArrayList();
        statusList.add(SupplierQualityAuditTaskStatusEnum.IN_PROGRESS.getCode());
        statusList.add(SupplierQualityAuditTaskStatusEnum.SUBMITTED_RESULT.getCode());
        statusList.add(SupplierQualityAuditTaskStatusEnum.APPROVE_FAIL.getCode());
        List<SupplierQualityAuditTaskPO> poList = supplierQualityAuditTaskMapperService.listByStatusAndPredictCompleteDate(statusList, date);
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        Map<String, List<SupplierQualityAuditTaskPO>> groupByMap = poList.stream().collect(Collectors.groupingBy(SupplierQualityAuditTaskPO::getAuditPlanNo));
        List<SupplierQualityAuditPlanPO> plans = supplierQualityAuditPlanMapperService.listByAuditPlanNos(new ArrayList<>(groupByMap.keySet()));
        //发送通知
        for (SupplierQualityAuditPlanPO plan : plans) {
            supplierQualityAuditMessageBiz.sendMessageAfterSupplierAuditPlanRemindJob(plan, groupByMap.get(plan.getAuditPlanNo()));
        }

    }

    @Override
    public PageResponse<SupplierQualityAuditTaskReportRespDTO> pageReport(SupplierQualityAuditTaskReportPageReqDTO reqDTO) {
        Page<SupplierQualityAuditTaskReportRespDTO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        IPage<SupplierQualityAuditTaskReportRespDTO> pageResult = supplierQualityAuditTaskMapperService.listReportPageByQuery(page, reqDTO);
        for (SupplierQualityAuditTaskReportRespDTO dto : pageResult.getRecords()) {
            dto.setStatusName(SupplierQualityAuditTaskStatusEnum.getNameByCode(dto.getStatus()));
            dto.setAuditConclusionName(SupplierQualityAuditTaskConclusionEnum.getNameByCode(dto.getAuditConclusion()));
            dto.setAuditCategoryTypeName(SupplierQualityAuditPlanCategoryTypeEnum.getNameByCode(dto.getAuditCategoryType()));
            dto.setAuditSubclassTypeName(getAuditSubclassTypeName(dto));
            dto.setIsOverdue(getIsInProgress(dto.getStatus(), dto.getPracticalCompleteDate(), dto.getPredictCompleteDate()));
        }
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public List<String> listSupplierCodeByAuditPlanNo(String auditPlanNo) {
        List<SupplierQualityAuditTaskPO> poList = supplierQualityAuditTaskMapperService.listByAuditPlanNo(auditPlanNo);
        if (CollectionUtils.isEmpty(poList)) {
            return Lists.newArrayList();
        }
        return poList.stream().map(SupplierQualityAuditTaskPO::getSupplierCode).collect(Collectors.toList());
    }

    private String getAuditSubclassTypeName(SupplierQualityAuditTaskReportRespDTO dto) {
        String auditSubclassTypeName = SupplierQualityAuditPlanSubclassTypeEnum.getNameByCode(dto.getAuditSubclassType());
        boolean isOther = SupplierQualityAuditPlanSubclassTypeEnum.OTHER.getCode().equals(dto.getAuditSubclassType()) || SupplierQualityAuditPlanSubclassTypeEnum.SPECIAL_REVIEW.getCode().equals(dto.getAuditSubclassType());
        return isOther ? auditSubclassTypeName + "(" + dto.getAuditSubclassOther() + ")" : auditSubclassTypeName;
    }


    private void updateStatus(List<Long> idList, OperatorBaseReqDTO reqDTO, SupplierQualityAuditTaskStatusEnum statusEnum) {
        supplierQualityAuditTaskMapperService.lambdaUpdate().in(SupplierQualityAuditTaskPO::getId, idList)
                .set(SupplierQualityAuditTaskPO::getStatus, statusEnum.getCode())
                .set(SupplierQualityAuditTaskPO::getUpdateBy, reqDTO.getOperationBy())
                .set(SupplierQualityAuditTaskPO::getUpdateName, reqDTO.getOperationByName())
                .set(SupplierQualityAuditTaskPO::getUpdateTime, DateUtil.date())
                .update();
    }


    private String getIsInProgress(String status, Date practicalCompleteDate, Date predictCompleteDate) {
        boolean isInProgress = !SupplierQualityAuditTaskStatusEnum.ENDED.getCode().equals(status);
        String isOverdue = DateUtil.compare(practicalCompleteDate, predictCompleteDate) > 0 ? "逾期提交" : "按时提交";
        return isInProgress ? "进行中" : isOverdue;
    }

    private void removeTask(String auditPlanNo, List<SupplierQualityAuditTaskReqDTO> taskRespList) {
        List<SupplierQualityAuditTaskPO> allOldTaskList = supplierQualityAuditTaskMapperService.listByAuditPlanNo(auditPlanNo);
        List<Long> deleteTaskIdList;
        if (CollectionUtils.isNotEmpty(allOldTaskList)) {
            taskRespList = CollectionUtils.isEmpty(taskRespList) ? Lists.newArrayList() : taskRespList;
            List<Long> finalModifyIdList = taskRespList.stream()
                    .map(SupplierQualityAuditTaskReqDTO::getId).filter(Objects::nonNull).collect(Collectors.toList());
            deleteTaskIdList = allOldTaskList.stream().map(SupplierQualityAuditTaskPO::getId)
                    .filter(id -> !finalModifyIdList.contains(id)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteTaskIdList)) {
                supplierQualityAuditTaskMapperService.removeByIds(deleteTaskIdList);
            }
        }
    }

    private void fillCategoryCode(SupplierQualityAuditTaskPageReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getCategoryCode())) {
            return;
        }
        ApiResponse<List<CategoryWithAllLevelDTO>> apiResponse = categoryApi.listCategory(Lists.newArrayList(reqDTO.getCategoryCode()));
        if (apiResponse == null || CollectionUtils.isEmpty(apiResponse.getData())) {
            return;
        }
        List<CategoryWithAllLevelDTO> categoryList = apiResponse.getData();
        List<String> threeCategoryCodes = categoryList.stream().map(CategoryWithAllLevelDTO::getThreeLevelCategoryCode).collect(Collectors.toList());
        reqDTO.setThreeCategoryCodes(threeCategoryCodes);
    }

    /**
     * 补充附件信息
     */
    private void fillAnnex(List<SupplierQualityAuditTaskRespDTO> dtoList) {
        List<String> taskNoList = dtoList.stream().map(SupplierQualityAuditTaskRespDTO::getAuditTaskNo).collect(Collectors.toList());
        Map<String, List<AttachmentMessageRespDTO>> annexMap = attachmentRecordBiz.listByBusinessNoAndBusinessType(taskNoList, SupplierQualityAuditTaskAnnexEnum.SUPPLIER_AUDIT_TASK_SUBMIT_ANNEX.getCode());
        for (SupplierQualityAuditTaskRespDTO dto : dtoList) {
            List<AttachmentMessageRespDTO> annexList = annexMap.get(dto.getAuditTaskNo());
            dto.setAnnexList(annexList);
        }
    }

    private void fillPlanInfo(Set<String> auditPlanNoSet, List<SupplierQualityAuditTaskRespDTO> dtoList) {
        if (CollectionUtils.isEmpty(auditPlanNoSet)) {
            return;
        }
        Map<String, SupplierQualityAuditPlanPO> poMap = getPlanMap(auditPlanNoSet);
        for (SupplierQualityAuditTaskRespDTO dto : dtoList) {
            SupplierQualityAuditPlanPO po = poMap.get(dto.getAuditPlanNo());
            if (po == null) {
                continue;
            }
            dto.setAuditCategoryType(po.getAuditCategoryType());
            dto.setAuditSubclassType(po.getAuditSubclassType());
            dto.setSqeMasterId(po.getSqeMasterId());
            dto.setSqeMasterName(po.getSqeMasterName());
        }
    }

    private Map<String, SupplierQualityAuditPlanPO> getPlanMap(Set<String> auditPlanNoSet) {
        List<String> auditPlanNos = CollectionUtils.isEmpty(auditPlanNoSet) ? Collections.emptyList() : new ArrayList<>(auditPlanNoSet);
        List<List<String>> auditPlanNoPartition = Lists.partition(auditPlanNos, 500);
        Map<String, SupplierQualityAuditPlanPO> poMap = Maps.newHashMap();
        for (List<String> auditPlanNoList : auditPlanNoPartition) {
            List<SupplierQualityAuditPlanPO> planList = CollectionUtils.isEmpty(auditPlanNoSet) ? Collections.emptyList() : supplierQualityAuditPlanMapperService.listByAuditPlanNos(auditPlanNoList);
            for (SupplierQualityAuditPlanPO po : planList) {
                poMap.put(po.getAuditPlanNo(), po);
            }
        }
        return poMap;
    }

    private void setPermission(SupplierQualityAuditTaskPageReqDTO reqDTO) {
        DataPermissionRespDTO dataPermissionRespDTO = dataPermissionManager.queryUserDataPermission(reqDTO, "PC_INTERNAL_PAGE_SUPPLIER_QUALITY_REVIEW_TASK");
        log.info("supplier quality audit task permission:{}", JSONUtil.toJsonStr(dataPermissionRespDTO));
        fillCategoryCode(reqDTO);
    }

    private String createTaskFormat(String createTask) {
        if (OPL.equals(createTask)) {
            return createTask;
        } else if (SUPPLIER.equals(createTask)) {
            return ServiceConstants.ZH_CN.equals(LocaleContextHolder.getLocale().toString()) ? "供应商任务" : "Supplier tasks";
        }
        return null;
    }
}
