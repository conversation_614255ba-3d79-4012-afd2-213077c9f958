package com.weifu.srm.supplier.cio.service.biz;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.weifu.srm.communication.request.sitemessage.SendSiteMessageReqDTO;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.supplier.cio.enums.SupplierQualityEmailTemplateEnum;
import com.weifu.srm.supplier.cio.enums.SupplierCioSiteMessageTemplateEnum;
import com.weifu.srm.supplier.cio.manager.MQServiceManager;
import com.weifu.srm.supplier.cio.repository.constants.SupplierCioCommonConstants;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssuePO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决表-发送通知和邮件
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierQualityIssueSendMessageBiz {
    public static final String COMPANY_NAME = "companyName";
    @Value("${weifu.srm.cio.quality.url:SRM_URL}")
    String srmUrl;
    @Value("${weifu.srm.cio.quality.companyName:companyName}")
    String companyName;
    public static final String SRM_URL = "SRM_URL";
    public static final String SUBMIT_TIME = "submitTime";
    public static final String CLOSE_ISSUE_OPINION = "closeIssueOpinion";
    public static final String RECALL_ISSUE_REASON_DESCRIPTION = "recallIssueReasonDescription";
    public static final String MEASURE_AUDIT_OPINION = "measureAuditOpinion";
    public static final String ROOT_CAUSE_AUDIT_OPINION = "rootCauseAuditOpinion";
    public static final String REASON_ANALYSIS_FEEDBACK_DATE = "reasonAnalysisFeedbackDate";
    public static final String PREVENT_RECUR_FEEDBACK_DATE = "preventRecurFeedbackDate";
    public static final String ISSUE_NO = "issueNo";
    public static final String PART_CATEGORY = "partCategory";
    public static final String COMPLAINT_TITLE = "complaintTitle";
    public static final String SQE_NAME = "sqeName";
    public static final String RECEIVE_OPINION = "receiveOpinion";
    public static final String SUPPLIER_NAME = "supplierName";
    public static final String CREATE_NAME = "createName";

    public static final String COMPLAINT_DATE = "complaintDate";
    public static final String TEMPORARY_MEASURES = "temporaryMeasures";
    public static final String PART_SAP_NO = "partSapNo";
    public static final String PART_NAME = "partName";

    private final MQServiceManager mqService;

    public void sendEmail(SupplierQualityIssuePO supplierQualityIssue,
                          SupplierQualityEmailTemplateEnum emailTemplateEnum,
                          String email) {
        if (StringUtils.isBlank(email)) {
            return;
        }
        String title = mqService.formatTemplateStr(getEmailTitleVariableMap(supplierQualityIssue), emailTemplateEnum.getTitle());
        String content = mqService.formatTemplateStr(getEmailVariableMap(supplierQualityIssue), emailTemplateEnum.getContent());
        CreateSendEmailTaskMQ mq = mqService.buildCreateSendEmailTask(title, content, email,
                supplierQualityIssue.getUpdateBy(), supplierQualityIssue.getUpdateName());
        mq.setBusinessNo(supplierQualityIssue.getIssueNo());
        mq.setBusinessType(emailTemplateEnum.getCode());
        mqService.sendEmailTask(mq);
    }


    /**
     * 发送通知
     */
    public void sendMessage(SupplierQualityIssuePO supplierQualityIssue, SupplierCioSiteMessageTemplateEnum templateEnum, Long userId, String username) {
        String template = formatTodoTemplate(supplierQualityIssue, templateEnum);
        SendSiteMessageReqDTO dto = mqService.buildSendSiteMessageReqDTO(template, templateEnum, supplierQualityIssue.getIssueNo(), userId, username);
        mqService.sendSiteMessage(dto);
    }

    /**
     * 发送通知-批量
     */
    public void sendMessageBatch(SupplierQualityIssuePO supplierQualityIssue, SupplierCioSiteMessageTemplateEnum templateEnum, Map<Long, String> messageUserMap) {
        if (messageUserMap == null || messageUserMap.isEmpty()) {
            return;
        }
        String template = formatTodoTemplate(supplierQualityIssue, templateEnum);
        messageUserMap.forEach((k, v) ->
                mqService.sendSiteMessage(mqService.buildSendSiteMessageReqDTO(template, templateEnum, supplierQualityIssue.getIssueNo(), k, v)));

    }

    public String formatTodoTemplate(SupplierQualityIssuePO supplierQualityIssue, SupplierCioSiteMessageTemplateEnum supplierCioSiteMessageTemplateEnum) {
        Map<String, String> variableMap = getVariableMap(supplierQualityIssue);
        return mqService.formatTemplateStr(variableMap, supplierCioSiteMessageTemplateEnum.getContent());
    }

    /**
     * 获取占位符的参数
     *
     * @param supplierQualityIssue 供应商质量问题参数
     * @return 参数集合
     */
    private Map<String, String> getVariableMap(SupplierQualityIssuePO supplierQualityIssue) {
        Map<String, String> variableMap = Maps.newHashMap();
        variableMap.put(ISSUE_NO, fillDefaultStr(supplierQualityIssue.getIssueNo()));
        variableMap.put(PART_CATEGORY, fillDefaultStr(supplierQualityIssue.getPartCategoryName()));
        variableMap.put(COMPLAINT_TITLE, fillDefaultStr(supplierQualityIssue.getComplaintTitle()));
        variableMap.put(SQE_NAME, fillDefaultStr(supplierQualityIssue.getSqeName()));
        variableMap.put(RECEIVE_OPINION, fillDefaultStr(supplierQualityIssue.getReceiveOpinion()));
        variableMap.put(SUPPLIER_NAME, fillDefaultStr(supplierQualityIssue.getSupplierName()));
        variableMap.put(CREATE_NAME, fillDefaultStr(supplierQualityIssue.getCreateName()));
        variableMap.put(REASON_ANALYSIS_FEEDBACK_DATE, fillDefaultStr(DateUtil.format(supplierQualityIssue.getReasonAnalysisFeedbackDate(), DatePattern.NORM_DATE_PATTERN)));
        variableMap.put(PREVENT_RECUR_FEEDBACK_DATE, fillDefaultStr(DateUtil.format(supplierQualityIssue.getPreventRecurFeedbackDate(), DatePattern.NORM_DATE_PATTERN)));
        variableMap.put(COMPLAINT_DATE, fillDefaultStr(DateUtil.format(supplierQualityIssue.getComplaintDate(), DatePattern.NORM_DATE_PATTERN)));
        variableMap.put(TEMPORARY_MEASURES, fillDefaultStr(supplierQualityIssue.getTemporaryMeasures()));
        return variableMap;
    }

    /**
     * 获取占位符的参数
     *
     * @param supplierQualityIssue 供应商质量问题参数
     * @return 参数集合
     */
    private Map<String, String> getEmailTitleVariableMap(SupplierQualityIssuePO supplierQualityIssue) {
        Map<String, String> variableMap = Maps.newHashMap();
        variableMap.put(ISSUE_NO, fillDefaultStr(supplierQualityIssue.getIssueNo()));
        variableMap.put(SUPPLIER_NAME, fillDefaultStr(supplierQualityIssue.getSupplierName()));
        variableMap.put(REASON_ANALYSIS_FEEDBACK_DATE, fillDefaultStr(DateUtil.format(supplierQualityIssue.getReasonAnalysisFeedbackDate(), SupplierCioCommonConstants.YYYY_MM_DD)));
        variableMap.put(PREVENT_RECUR_FEEDBACK_DATE, fillDefaultStr(DateUtil.format(supplierQualityIssue.getPreventRecurFeedbackDate(), SupplierCioCommonConstants.YYYY_MM_DD)));
        variableMap.put(COMPANY_NAME, companyName);
        return variableMap;
    }

    /**
     * 获取占位符的参数
     *
     * @param supplierQualityIssue 供应商质量问题参数
     * @return 参数集合
     */
    private Map<String, String> getEmailVariableMap(SupplierQualityIssuePO supplierQualityIssue) {
        Map<String, String> variableMap = Maps.newHashMap();
        variableMap.put(ISSUE_NO, fillDefaultStr(supplierQualityIssue.getIssueNo()));
        variableMap.put(PART_SAP_NO, fillDefaultStr(supplierQualityIssue.getPartSapNo()));
        variableMap.put(PART_NAME, fillDefaultStr(supplierQualityIssue.getPartName()));
        variableMap.put(COMPLAINT_TITLE, fillDefaultStr(supplierQualityIssue.getComplaintTitle()));
        variableMap.put(SUPPLIER_NAME, fillDefaultStr(supplierQualityIssue.getSupplierName()));
        variableMap.put(COMPLAINT_DATE, fillDefaultStr(DateUtil.format(supplierQualityIssue.getComplaintDate(), SupplierCioCommonConstants.YYYY_MM_DD)));
        variableMap.put(SUBMIT_TIME, fillDefaultStr(DateUtil.format(supplierQualityIssue.getSubmitTime(), SupplierCioCommonConstants.YYYY_MM_DD)));
        variableMap.put(TEMPORARY_MEASURES, fillDefaultStr(supplierQualityIssue.getTemporaryMeasures()));
        variableMap.put(CLOSE_ISSUE_OPINION, fillDefaultStr(supplierQualityIssue.getCloseIssueOpinion()));
        variableMap.put(RECALL_ISSUE_REASON_DESCRIPTION, fillDefaultStr(supplierQualityIssue.getRecallIssueReasonDescription()));
        variableMap.put(MEASURE_AUDIT_OPINION, fillDefaultStr(supplierQualityIssue.getMeasureAuditOpinion()));
        variableMap.put(ROOT_CAUSE_AUDIT_OPINION, fillDefaultStr(supplierQualityIssue.getRootCauseAuditOpinion()));
        variableMap.put(REASON_ANALYSIS_FEEDBACK_DATE, fillDefaultStr(DateUtil.format(supplierQualityIssue.getReasonAnalysisFeedbackDate(), SupplierCioCommonConstants.YYYY_MM_DD)));
        variableMap.put(PREVENT_RECUR_FEEDBACK_DATE, fillDefaultStr(DateUtil.format(supplierQualityIssue.getPreventRecurFeedbackDate(), SupplierCioCommonConstants.YYYY_MM_DD)));
        variableMap.put(CREATE_NAME, fillDefaultStr(supplierQualityIssue.getCreateName()));
        variableMap.put(SRM_URL, srmUrl);
        variableMap.put(COMPANY_NAME, companyName);
        return variableMap;
    }

    private String fillDefaultStr(String str) {
        return StringUtils.isBlank(str) ? SupplierCioCommonConstants.DEFAULT_EMPTY_STR : str;
    }


}
