package com.weifu.srm.supplier.cio.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditTaskPO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import com.weifu.srm.supplier.cio.request.audit.*;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskExcelRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskReportRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskRespDTO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商质量审核任务-服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualityAuditTaskService {
    /**
     * 分页查询
     */
    PageResponse<SupplierQualityAuditTaskRespDTO> page(SupplierQualityAuditTaskPageReqDTO reqDTO);

    /**
     * 条件查询
     */
    List<SupplierQualityAuditTaskExcelRespDTO> listByQuery(SupplierQualityAuditTaskPageReqDTO reqDTO);

    /**
     * 保存或修改
     */
    void saveOrUpdate(String auditPlanNo, List<SupplierQualityAuditTaskReqDTO> taskList, OperatorBaseReqDTO reqDTO,boolean isSubmit);

    /**
     * 提交结果
     */
    void submitResult(SupplierQualityAuditTaskResultReqDTO reqDTO);

    /**
     * 审核结果
     */
    void approveResult(SupplierQualityAuditTaskApproveReqDTO reqDTO);

    /**
     * 复制任务
     */
    void copyTask(String oldAuditPlanNo, String newAuditPlanNo, OperatorBaseReqDTO reqDTO, Date now);

    /**
     * 根据计划编号查询
     */
    List<SupplierQualityAuditTaskRespDTO> listByAuditPlanNo(String auditPlanNo);

    /**
     * 根据计划编号删除
     */
    void deleteByAuditPlanNo(String auditPlanNo);

    /**
     * 是否跨年
     */
    boolean isStrideYear(List<SupplierQualityAuditTaskAdjustReqDTO> taskRespList);

    void checkStatus(List<Long> taskIdList);
    /**
     * 调整保存
     */
    String adjustPetition(SupplierQualityAuditPlanAdjustReqDTO reqDTO, String adjustType);

    /**
     * 根据调整编号查询
     */
    List<SupplierQualityAuditTaskRespDTO> listByAdjustNo(String adjustNo);

    /**
     * 审核结束
     */
    void inProgress(String auditPlanNo, OperatorBaseReqDTO reqDTO);

    /**
     * 调整审批流变更
     *
     * @param mq 审批流状态
     */
    void updateAdjustProgress(TicketStatusChangedMQ mq);

    /**
     * 定时任务
     */
    void supplierAuditPlanRemindJob(Date date);

    /**
     * 分页查询
     */
    PageResponse<SupplierQualityAuditTaskReportRespDTO> pageReport(SupplierQualityAuditTaskReportPageReqDTO reqDTO);

    List<String> listSupplierCodeByAuditPlanNo(String auditPlanNo);
}
