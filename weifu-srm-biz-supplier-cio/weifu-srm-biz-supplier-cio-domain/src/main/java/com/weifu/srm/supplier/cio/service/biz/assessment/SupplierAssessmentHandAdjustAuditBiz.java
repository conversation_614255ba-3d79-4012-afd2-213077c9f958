package com.weifu.srm.supplier.cio.service.biz.assessment;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.util.DateUtil;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.cio.manager.MQServiceManager;
import com.weifu.srm.supplier.cio.manager.SupplierManager;
import com.weifu.srm.supplier.cio.manager.remote.SysUserManager;
import com.weifu.srm.supplier.cio.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierAssessmentLogMapperService;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierAssessmentMapperService;
import com.weifu.srm.supplier.cio.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.cio.repository.enums.EmailTemplateEnum;
import com.weifu.srm.supplier.cio.repository.enums.SupplierAssessmentStatusEnum;
import com.weifu.srm.supplier.cio.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.cio.repository.po.SupplierAssessmentLogPO;
import com.weifu.srm.supplier.cio.repository.po.SupplierAssessmentPO;
import com.weifu.srm.supplier.enums.SupplierContactTypeEnum;
import com.weifu.srm.supplier.response.SupplierContactSupplierCodeRespDTO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/9/19 18:12
 * @Description
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierAssessmentHandAdjustAuditBiz {
    private final SupplierAssessmentMapperService assessmentMapperService;
    private final TransactionTemplate transactionTemplate;
    private final MQServiceManager mqService;
    private final SupplierManager supplierManager;
    private final SupplierAssessmentLogMapperService logMapperService;
    private final SysUserManager sysUserManager;
    private final AttachmentRecordMapperService attachmentRecordMapperService;

    public void handleAdjustAudit(TicketStatusChangedMQ ticketStatusChangedMQ) {
        if (TicketStatusEnum.APPROVING.equalsCode(ticketStatusChangedMQ.getStatus())){
            return;
        }
        log.info("SupplierAssessmentHandAdjustAuditBiz :{}", ticketStatusChangedMQ);
        SupplierAssessmentPO assessmentPO = assessmentMapperService.lambdaQuery()
                .eq(SupplierAssessmentPO::getAssessmentNo, ticketStatusChangedMQ.getBusinessNo())
                .eq(SupplierAssessmentPO::getStatus, SupplierAssessmentStatusEnum.ADJUST_ASSESSMENT_APPROVING.getCode())
                .eq(SupplierAssessmentPO::getIsDelete, YesOrNoEnum.NO.getCode()).one();
        if (ObjectUtil.isNull(assessmentPO)) {
            log.error("this assessment :{} doses not exists or status not support", ticketStatusChangedMQ.getBusinessNo());
            return;
        }
        String assessmentStatus = getAssessmentAuditStatus(ticketStatusChangedMQ);
        if (StringUtils.isBlank(assessmentStatus)){
            return;
        }
        LambdaUpdateWrapper<SupplierAssessmentPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SupplierAssessmentPO::getStatus, assessmentStatus);
        updateWrapper.set(SupplierAssessmentPO::getIsAdjusted, YesOrNoEnum.YES.getCode());
        updateWrapper.set(SupplierAssessmentPO::getUpdateTime, new Date());
        updateWrapper.set(SupplierAssessmentPO::getUpdateBy, ticketStatusChangedMQ.getOperateBy());
        updateWrapper.set(SupplierAssessmentPO::getUpdateName, ticketStatusChangedMQ.getOperateName());
        updateWrapper.eq(SupplierAssessmentPO::getAssessmentNo, ticketStatusChangedMQ.getBusinessNo());
        //所有审批层级都通过后，以审批通过的次日+“确认时限（天）”作为供应商的反馈时间期限
        if (!ObjectUtil.isNull(assessmentPO.getLimitDays())
                && TicketStatusEnum.APPROVED.equalsCode(ticketStatusChangedMQ.getStatus())) {
            updateWrapper.set(SupplierAssessmentPO::getDeadlineTime, addDaysToCurrentTime(assessmentPO.getLimitDays() + 1));
        }
        CreateSendEmailTaskMQ emailReq = boxSupplierAssessmentEmail(assessmentPO, ticketStatusChangedMQ);
        //再次调整审核通过后，将此前的考核单存入记录表中
        SupplierAssessmentLogPO supplierAssessmentLogPO = logMapperService.lambdaQuery()
                .eq(SupplierAssessmentLogPO::getAssessmentNo, assessmentPO.getAssessmentNo())
                .eq(SupplierAssessmentLogPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .eq(SupplierAssessmentLogPO::getStatus, SupplierAssessmentStatusEnum.ADJUST_ASSESSMENT_APPROVING.getCode())
                .orderByDesc(SupplierAssessmentLogPO::getId)
                .list()
                .get(0);
        transactionTemplate.executeWithoutResult(r -> {
            //审批通过后给给供应商注册联系人与供应商质量联系人发邮件
            if (TicketStatusEnum.APPROVED.equalsCode(ticketStatusChangedMQ.getStatus())) {
                assessmentMapperService.update(updateWrapper);
                // 发送邮件通知
                mqService.sendMessage(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(emailReq));
                supplierAssessmentLogPO.setDeadlineTime(new Date());
                supplierAssessmentLogPO.setStatus(SupplierAssessmentStatusEnum.WAITING_SUPPLIER_FEEDBACK.getCode());
                // 更新记录状态
                logMapperService.updateById(supplierAssessmentLogPO);
                return;
            }
            // 考核拒绝/关闭 回滚考核数据为提前再次考核审批前
            rollbackDataToBeforeAdjust(assessmentPO, supplierAssessmentLogPO, ticketStatusChangedMQ, assessmentStatus);
        });


    }

    public static LocalDateTime addDaysToCurrentTime(int daysToAdd) {
        LocalDate futureDate = LocalDate.now().plusDays(daysToAdd);
        LocalTime endTimeOfDay = LocalTime.of(23, 59, 59); // 使用LocalTime.MAX表示一天中的最后一秒
        return LocalDateTime.of(futureDate, endTimeOfDay);
    }

    private String getAssessmentAuditStatus(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        switch (statusEnum) {
            case CANCELED:
                return SupplierAssessmentStatusEnum.SUPPLIER_REJECT_ASSESSMENT.getCode();
            case REJECTED:
                return SupplierAssessmentStatusEnum.ADJUST_ASSESSMENT_REJECT.getCode();
            case APPROVED:
                return SupplierAssessmentStatusEnum.WAITING_SUPPLIER_FEEDBACK.getCode();
            default:
                log.debug("***** audit message do not need  process={}", ticketInfo);
                return null;
        }
    }

    private CreateSendEmailTaskMQ boxSupplierAssessmentEmail(SupplierAssessmentPO assessmentPO,
                                                             TicketStatusChangedMQ ticketStatusChangedMQ) {
        if (!TicketStatusEnum.APPROVED.equalsCode(ticketStatusChangedMQ.getStatus())) {
            return null;
        }
        List<SupplierContactSupplierCodeRespDTO> contactQualityInfos = supplierManager.getContactInfo(List.of(assessmentPO.getSupplierCode()), SupplierContactTypeEnum.QUALITY.getCode());
        SupplierContactSupplierCodeRespDTO contactQualityInfoPO = Optional.ofNullable(contactQualityInfos).flatMap(r -> r.stream().findFirst()).orElse(null);
        List<SupplierContactSupplierCodeRespDTO> contactRegisterInfos = supplierManager.getContactInfo(List.of(assessmentPO.getSupplierCode()), SupplierContactTypeEnum.REGISTER.getCode());
        SupplierContactSupplierCodeRespDTO contactInfoPO = Optional.ofNullable(contactRegisterInfos).flatMap(r -> r.stream().findFirst()).orElse(null);
        String recipients = Optional.ofNullable(contactQualityInfoPO).map(SupplierContactSupplierCodeRespDTO::getEmail).orElse("")
                .concat(";")
                .concat(Optional.ofNullable(contactInfoPO).map(SupplierContactSupplierCodeRespDTO::getEmail).orElse(""));
        //查询质量联系人邮箱
        BaseSysUserRespDTO userRespDTO = sysUserManager.getUserDetailById(assessmentPO.getSqeId());
        CreateSendEmailTaskMQ emailReq = new CreateSendEmailTaskMQ();
        Integer limitDays = assessmentPO.getLimitDays();
        long days = 1L + (ObjectUtil.isNotEmpty(limitDays) ? Long.parseLong(limitDays.toString()) : 0L);
        String time = LocalDate.now().plusDays(days).format(DateTimeFormatter.ISO_LOCAL_DATE);
        emailReq.setTitle(EmailTemplateEnum.ADJUST_QUALITY_ASSESSMENT.getTitle()
                .replace("${assessment_amt}", String.valueOf(assessmentPO.getAssessmentTaxAmt()))
                .replace("${limit_days}", time));
        emailReq.setContent(EmailTemplateEnum.ADJUST_QUALITY_ASSESSMENT.getContent()
                .replace("${supplier_name}", assessmentPO.getSupplierName())
                .replace("${create_time}", DateUtil.dateFormat(assessmentPO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                .replace("${assessment_amt}", String.valueOf(assessmentPO.getAssessmentTaxAmt()))
                .replace("${assessment_no}", assessmentPO.getAssessmentNo())
                .replace("${limit_days}", time));
        emailReq.setRecipients(recipients);
        emailReq.setCcRecipients(Optional.ofNullable(userRespDTO).map(BaseSysUserRespDTO::getEmail).orElse(null));
        emailReq.setBusinessNo(assessmentPO.getAssessmentNo());
        emailReq.setBusinessType("再次调整质量考核单");
        emailReq.setCreateBy(ticketStatusChangedMQ.getOperateBy());
        emailReq.setCreateName(ticketStatusChangedMQ.getOperateName());
        return emailReq;
    }

    private void rollbackDataToBeforeAdjust(SupplierAssessmentPO assessmentPO,
                                            SupplierAssessmentLogPO supplierAssessmentLogPO,
                                            TicketStatusChangedMQ ticketStatusChangedMQ,
                                            String assessmentStatus) {
        // 还原附件
        List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessNo, supplierAssessmentLogPO.getId().toString())
                .in(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.ASSESSMENT_DESC_FILE, AttachmentBizTypeConstants.ASSESSMENT_INNER_DESC_FILE)
                .list();
        if (CollectionUtils.isNotEmpty(attachmentRecordPOList)) {
            for (AttachmentRecordPO attachmentRecordPO : attachmentRecordPOList) {
                attachmentRecordPO.setBusinessNo(assessmentPO.getAssessmentNo());
            }
        }
        attachmentRecordMapperService.removeByBusinessTypeAndBizNo(assessmentPO.getAssessmentNo(), List.of(AttachmentBizTypeConstants.ASSESSMENT_DESC_FILE, AttachmentBizTypeConstants.ASSESSMENT_INNER_DESC_FILE));
        attachmentRecordMapperService.saveBatch(attachmentRecordPOList);
        // 还原主数据
        supplierAssessmentLogPO.setId(assessmentPO.getId());
        BeanUtils.copyProperties(supplierAssessmentLogPO, assessmentPO);
        assessmentPO.setStatus(assessmentStatus);
        BaseEntityUtil.setCommonForU(assessmentPO, ticketStatusChangedMQ.getOperateBy(), ticketStatusChangedMQ.getOperateName(), ticketStatusChangedMQ.getUpdateTime());
        assessmentMapperService.updateById(assessmentPO);
        // 删除记录数据
        logMapperService.removeById(supplierAssessmentLogPO.getId());
    }
}
