package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 质量审核计划-状态枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityAuditPlanStatusEnum {
    DRAFT("DRAFT", "草稿"),
    IN_APPROVAL("IN_APPROVAL", "审批中"),
    APPROVAL_REJECTED("APPROVAL_REJECTED", "审批拒绝"),
    IN_PROGRESS("IN_PROGRESS", "进行中"),
    COMPLETED("COMPLETED", "已完成");

    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualityAuditPlanStatusEnum statusEnum : SupplierQualityAuditPlanStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
