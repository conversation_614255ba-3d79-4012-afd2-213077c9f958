package com.weifu.srm.price.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OriginalMaterialPriceHistoryRespDTO extends BaseRespDTO{
    /**
     * 原材料价格主表ID
     */
    @ApiModelProperty(value = "原材料价格主表ID")
    private Long originalMaterialPriceId;
    /**
     * 原材料牌号(新)
     */
    @ApiModelProperty(value = "原材料牌号(新)")
    private String originalMaterialNo;
    /**
     * 原材料名称(新)
     */
    @ApiModelProperty(value = "原材料名称(新)")
    private String originalMaterialName;
    /**
     * 基本单位(新)
     */
    @ApiModelProperty(value = "基本单位(新)")
    private String basicUnit;
    /**
     * 价格单位(新)
     */
    @ApiModelProperty(value = "价格单位(新)")
    private Long priceUnit;
    /**
     * 价格（元）(新)
     */
    @ApiModelProperty(value = "价格（元）(新)")
    private BigDecimal price;

    @ApiModelProperty(value = "供应商编码(新)")
    private String sapSupplierCode;

    @ApiModelProperty(value = "供应商名称(新)")
    private String supplierName;

    @ApiModelProperty("备注(新)")
    private String remark;
    /**
     * 原材料牌号（旧）
     */
    @ApiModelProperty(value = "原材料牌号（旧）")
    private String oldOriginalMaterialNo;
    /**
     * 原材料名称（旧）
     */
    @ApiModelProperty(value = "原材料名称（旧）")
    private String oldOriginalMaterialName;
    /**
     * 基本单位（旧）
     */
    @ApiModelProperty(value = "基本单位（旧）")
    private String oldBasicUnit;
    /**
     * 价格单位（旧）
     */
    @ApiModelProperty(value = "价格单位（旧）")
    private Long oldPriceUnit;
    /**
     * 价格（元）（旧）
     */
    @ApiModelProperty(value = "价格（元）（旧）")
    private BigDecimal oldPrice;

    @ApiModelProperty(value = "供应商编码(旧)")
    private String oldSapSupplierCode;

    @ApiModelProperty(value = "供应商名称(旧)")
    private String oldSupplierName;

    @ApiModelProperty("备注(旧)")
    private String oldRemark;
}
