package com.weifu.srm.price.atomicservice.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.price.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.price.convert.PriceLedgerConvert;
import com.weifu.srm.price.mapper.AttachmentRecordMapper;
import com.weifu.srm.price.po.AttachmentRecordPO;
import com.weifu.srm.price.request.AttachmentRecordReqDTO;
import com.weifu.srm.price.request.BaseUserReqDTO;
import com.weifu.srm.price.response.AttachmentRecordRespDTO;
import com.weifu.srm.price.utils.BaseUserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 针对表【attachment_record(附件信息记录表)】的数据库操作Service实现
 * @createDate 2024-08-07 15:28:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AttachmentRecordMapperServiceImpl extends ServiceImpl<AttachmentRecordMapper, AttachmentRecordPO>
        implements AttachmentRecordMapperService {

    private final PriceLedgerConvert priceLedgerConvert;

    @Override
    public List<AttachmentRecordRespDTO> listAttachments(String businessNo, String businessType) {
        if(CharSequenceUtil.isBlank(businessNo) || CharSequenceUtil.isBlank(businessType)) return Collections.emptyList();

        LambdaQueryWrapper<AttachmentRecordPO> query = new LambdaQueryWrapper<>();
        query.eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
        query.eq(AttachmentRecordPO::getBusinessNo,businessNo);
        query.eq(AttachmentRecordPO::getBusinessType,businessType);

        return priceLedgerConvert.convertAttachmentDTOList(this.list(query));
    }

    @Override
    public void saveAttachments(List<AttachmentRecordReqDTO> attachments,String businessNo,String businessType, BaseUserReqDTO user) {
        LambdaQueryWrapper<AttachmentRecordPO> query = Wrappers.lambdaQuery(AttachmentRecordPO.class)
                .eq(AttachmentRecordPO::getBusinessNo,businessNo).eq(AttachmentRecordPO::getBusinessType,businessType);
        if(CollUtil.isEmpty(attachments)){
            this.remove(query);
            return;
        }

        //先将现有数据查询出来
        List<AttachmentRecordPO> list = this.list(query);
        //需要新增的数据
        List<AttachmentRecordReqDTO> newAttachments = attachments.stream()
                .filter(v->Objects.isNull(v.getId())).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(newAttachments)){
            List<AttachmentRecordPO> saveList = BeanUtil.copyToList(newAttachments,AttachmentRecordPO.class);
            BaseUserUtils.setCreateUserInfo(saveList,user);
            saveList.forEach(v->{
                v.setBusinessNo(businessNo);
                v.setBusinessType(businessType);
            });
            this.saveBatch(saveList);
            //如果数据全部新增 直接结束 保存附件逻辑容错处理
            if(CollUtil.isEmpty(list) && Objects.equals(CollUtil.size(attachments),CollUtil.size(saveList))) return;
        }


        if(CollUtil.isNotEmpty(list)) {
            //需要删除的数据
            List<Long> removeIds = list.stream().filter(v->
                    attachments.stream().noneMatch(v1->Objects.nonNull(v1.getId()) && Objects.equals(v.getId(),v1.getId()))
            ).map(AttachmentRecordPO::getId).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(removeIds)) this.removeByIds(removeIds);
        }
    }
}




