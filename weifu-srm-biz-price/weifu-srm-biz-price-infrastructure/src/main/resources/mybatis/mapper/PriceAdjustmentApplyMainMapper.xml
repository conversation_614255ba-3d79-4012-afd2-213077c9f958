<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.price.mapper.PriceAdjustmentApplyMainMapper">
    <select id="listByPriceLedgerNos" resultType="com.weifu.srm.price.po.PriceAdjustmentApplyMainPO">
        select DISTINCT tb1.* from price_adjustment_apply_main tb1
        left join price_adjustment_apply_detail tb2 on tb1.apply_no = tb2.apply_no
        where tb1.is_delete=0 and tb2.is_delete=0
        AND tb2.old_price_ledger_no in
        <foreach collection="priceLedgerNos" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
        AND tb1.status in
        <foreach collection="status" item="item2" open="(" separator="," close=")">
            #{item2}
        </foreach>
    </select>

    <select id="getByApplyNo" resultType="com.weifu.srm.price.po.PriceAdjustmentApplyMainPO">
        select * from price_adjustment_apply_main where apply_no=#{applyNo}
    </select>
</mapper>