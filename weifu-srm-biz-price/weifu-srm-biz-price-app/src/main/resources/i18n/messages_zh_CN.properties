## 公共
common.option.result.failure=操作失败
common.option.result.success=操作成功
common.option.result.version.error=数据已被更新，请刷新重试
common.query.result.failure=查询失败
common.query.result.success=查询成功
common.query.result.not.find=查询的数据不存在
common.query.not.value=暂无数据
common.data.already.exists=记录已存在
common.params.cannot.be.empty={0}不能为空
common.template.is.inconformity=模板不匹配
common.data.is.empty=数据为空
common.data.is.duplicate=数据重复
common.params.failure=参数错误

## 信用额度
credit.limit.params.error=信用额度申请单参数错误
credit.limit.not.found=该信用额度申请单不存在

## 合作伙伴
new.code.not.exist=新编码不存在，请核实后重试
old.code.not.exist=原编码不存在，请核实后重试
new.code.status.not.effective=新编码的状态不是已生效，不能合并
this.partner.not.exist=当前合作伙伴不存在
partner.cannot.be.disabled=请检查合作伙伴{0}在合同、销售提单、供应商发货指令中的使用情况

## 商品主数据
product.data.null=商品主数据不存在，请确认
product.option.result.part.failure=部分数据操作失败，请刷新重试！
product.chinese.name.exists=中文名称已存在，请重新输入！
product.chinese.name.duplicate=中文名称有重复，请重新输入！
product.display.name.exists=显示名称已存在，请重新输入！
product.display.name.duplicate=显示名称有重复，请检查！
product.import.title=商品主数据
product.import.sheet=商品信息
product.excel.empty=excel内容为空，请填写后再导入!
product.history.not.find=未找到操作历史！
product.customs.multiple=海关编码存在多个，请检查！
product.customs.not.find=海关编码不存在，请检查！
product.group.category.not.find=集团产品分类不存在，请检查！
product.basic.unit.not.find=基本单位不存在，请检查！
product.value.added.taxRate.not.find=进项税不存在，请检查！
product.value.added.salesTaxRate.not.find=销项税不存在，请检查！
merge.query.result.not.find=要合并的商品明细编码不存在或状态不是已生效
product.cannot.be.disabled=请检查商品{0}子类状态及在合同、销售提单、业务库存中的使用情况
product.company.or.department.not.find=数据权限公司或部门不存在，请检查！

## 币种
currency.list.empty=币种列表为空
currency.code.cannot.be.empty=币种代码不能为空
currency.name.cannot.be.empty=币种名称不能为空
currency.code.cannot.be.duplicate=币种代码不能重复
currency.code.already.exists=币种代码{0}已经存在
currency.name.already.exists=币种名称{0}已经存在
currency.not.exist=币种不存在
currency.cannot.be.disabled=币种{0}是某个账套的本位币，不允许禁用

## 会计日历
calendar.list.empty=会计日历列表为空
calendar.code.cannot.be.empty=日历代码不能为空
calendar.type.cannot.be.empty=日历类型不能为空
calendar.desc.cannot.be.empty=日历说明不能为空
calendar.init.open.year=初始打开年度不能为空
calendar.code.cannot.be.duplicate=日历代码不能重复
calendar.code.already.exists=日历代码{0}已经存在
calendar.not.exist=日历不存在

## 账套
ledger.code.cannot.be.empty=分类账代码不能为空
ledger.name.cannot.be.empty=分类账名称不能为空
ledger.chart.cannot.be.empty=会计科目表不能为空
ledger.calendar.cannot.be.empty=会计日历不能为空
ledger.currency.code.cannot.be.empty=本位币不能为空
ledger.must.contain.legal=账套必须包含法人
ledger.code.already.exists=分类账代码{0}已经存在
ledger.not.exist=账套不存在
ledger.default.exchange.type.cannot.be.empty=默认汇率类型不能为空
ledger.init.open.period.cannot.be.empty=初始打开期间不能为空
ledger.legal.code.cannot.be.empty=法人主体代码不能为空
ledger.legal.name.cannot.be.empty=法人主体不能为空
ledger.legal.country.cannot.be.empty=国家(地区)不能为空
ledger.legal.credit.code.cannot.be.empty=统一社会信用代码不能为空
ledger.legal.address.cannot.be.empty=注册地址不能为空
ledger.legal.code.cannot.be.duplicate=法人主体不能重复
ledger.legal.code.already.exists=法人主体{0}已经存在

## 人员主数据
person.sequence.too.long=序列优先级不能大于999
person.id.exists=身份证号码重复，请查看对应人员数据

## 导入
excel.import.data.not.found=内容为空，请填写内容后再导入
excel.import.result.success=导入成功
excel.import.template.incorrect=导入的模板不正确
excel.import.analytic.anomaly=解析异常,请使用Excel标准模板
excel.import.result.failure=导入失败

## 权限
account.code.result.save=该员工账号已存在,请重新选择
application.code.result.delete=应用下面含有菜单不能删除
application.code.result.save=应用名称已存在

## 组织主数据
company.code.result.save=当前数据已被其他人修改，请刷新后重试
company.code.result.import.null=请填写数据
company.code.result.order=序列优先级已到最大值，请重新输入
company.code.result.check=公司名称已存在
nationality.not.find=国籍不存在
department.name.repeat=当前公司下部门名称重复
company.prohibit=当前公司下存在人员，不能禁用
storage.code.result.check=仓库名称已存在
department.code.result.check=部门名称已存在
department.code.result.enable=请先启用父级部门
department.prohibit=当前部门或子部门下存在人员，不能禁用
storage.person.result=该账号绑定的人员异常
control.area.existence.department=所选部门已存在其他控制范围，请重新选择
storage.cannot.be.disabled=请检查仓库{0}在供应商发货指令、物流入库、业务入库、物流出库、采购退换货、销售退换货中的使用情况

## 合作伙伴
partner.bank.not.match=银行信息不匹配
partner.person.not.match=人员信息不匹配

## 汇率
currency.exchange.date.repeat = 日期重复
currency.exchange.database.exists = 数据库已存在相同数据，请修改后重试
currency.exchange.rate.query.not.value=请维护当日汇率
currency.exchange.to.cannot.be.empty=币种至不能为空

## 外部价格
external.price.data.repeat = 相同价格日期，相同产品，相同价格类型，只能存在一条数据

## 供应链主体
supplier.status.exception=草稿、已驳回、已生效状态下才能修改
partner.name.exist=合作伙伴名称已存在
approval.error=审批失败，错误码
supplier.cannot.be.disabled=请检查供应链主体{0}在合同中的使用情况

## MDG
data.send.mdg.error=同步MDG失败:{0}
data.send.mdg.return.error=MDG返回报文异常
data.send.mdg.response.error=MDG响应异常
data.send.mdg.response.success=推送MDG成功

## project
termination.reason.not.empty=终止原因不能为空
termination.other.reason.not.empty=终止其他原因不能为空
project.name.is.exist=项目名称已存在

## price
price.data.not.exist=数据不存在！
price.file.export.fail=文件导出失败！
price.file.is.excel=文件需为Excel文件！
price.order.unit=填写了订单单位也需填写分子、分母数据！
price.file.template.equals=导入文件与模板格式不一致！
price.original.material.repeat=原材料名称或原材料牌号重复！
price.original.material.create.fail=创建原材料价格失败！
price.original.material.not.exist=原材料价格不存在！
price.original.material.update.fail=更新原材料价格失败！
price.data.status.verify.fail=数据不是待导入或导入失败状态！
price.status.not.import.fail=数据不是导入失败状态！
price.supplier.not.exist=供应商数据不存在！
price.supplier.leave.price.invalid=‘{0}’供应商已退出，对应价格已失效！
price.ledger.id.not.null=价格台账ID不能为空！
price.status.is.not.invalid=价格状态不为已失效！
price.supplier.code.empty=供应商编码为空！
price.contract.verify.fail=价格有效期的范围超过框架协议有效期范围，供应商{0}需重新签署框架协议！
price.query.cms.contract.error=CMS合同信息查询异常！
price.data.permission.fail=查询用户数据权限失败！
price.requirement.no.expansion.fail=需求号{0}不在可扩充价格的需求号范围！
price.effective.data.verify.fail=价格有效期（止）日期不能超过价格有效期（起）+90天（自然日）！
price.purchase.org.repeat=申请单中相同的采购组织只能存在一条价格扩充记录！
price.requirement.different.purchase.org=不同的采购组织的需求号不能相同！
price.no.have.save.data=不存在需要保存的数据！
price.user.no.data.permission=当前用户无数据权限！
price.data.not.exist.line=第{0}行数据不存在！
price.status.verify.fail.line=第{0}行数据状态不合法！
price.supplier.code.read.only.line=第{0}行数据供应商编码不能修改！
price.supplier.name.read.only.line=第{0}行数据供应商名称不能修改！
price.material.code.read.only.line=第{0}行数据物料号不能修改！
price.material.desc.read.only.line=第{0}行数据物料描述不能修改！
price.factory.read.only.line=第{0}行数据工厂数据不能修改！
price.tax.code.invalid.line=第{0}行数据税码不是有效的数据！
price.order.unit.invalid.line=第{0}行数据订单单位不是有效的数据！
price.order.unit.molecule.denominator.line=第{0}行数据填写了订单单位也需填写分子、分母数据！
price.no.draft.status=数据不是草稿状态！
price.effect.data.supplier.effect.data.verify=第{0}行价格有效期（止）不能大于供应商有效期结束时间{1}！
price.division.identical=相同事业部或子公司的价格才能一起发起新建申请！
price.material.not.exist=序号{0}中的物料号在SRM中不存在，请维护了正确的物料号后再提交！
price.material.category.no.relationship=序号{0}中的物料无归属的品类，请维护了物料与品类关系后再提交！
price.material.not.in.factory=第{0}行物料在工厂{0}中未被维护！
price.supplier.material.factory.exist=供应商{0} 物料{1} 工厂{2} 价格在价格台账中已经存在！
price.supplier.material.factory.exist.approving.apply=供应商{0} 物料{1} 工厂{2} 价格存在审核中的新建价格申请！
price.min.order.qty.compare.min.package.qty=第{0}行最小起订量需小于等于最小包装量！
price.sourcing.cancel=第{0}行价格商务定点或PPAP已作废！
price.detail.not.empty=价格明细数据不能为空！
price.create.apply.fail=创建申请单失败！
price.apply.not.exist=申请单不存在！
price.division.not.exist=事业部信息不存在！
price.material.purchase.org.factory.repeat=供应商：{0} 物料：{1} 采购组织：{2} 工厂：{3} 不能重复！
price.temporary.price.not.adjust=临时生效的价格不能进行价格调整！
price.exist.approving.adjust.apply=被调整的价格数据存在审批中的价格及价格有效期调整申请单！
price.adjust.status.verify=仅支持未导入或已生效状态进行价格调整！
price.material.not.exist.line=第{0}行物料不存在！
price.supplier.not.exist.line=第{0}行供应商不存在！
price.purchase.group.not.in.dict=第{0}行采购组不是采购组字典中的有效数据！
price.purchase.org.not.in.dict=第{0}行采购组织不是采购组织字典中的有效数据！
price.factory.code.verify=第{0}行工厂不存在或不是有效的工厂编码！
price.tax.code.verify=第{0}行税码不是有效的数据！
price.order.unit.verify=第{0}行订单单位不是有效的数据！
price.currency.verify=第{0}行币种不是有效的数据！
price.user.data.permission.adjust=当前用户无权限操作ID为{0}的价格数据！
price.verify.confirm.control=第{0}行确认控制不是有效的数据！
price.verify.date.format=第{0}行’{1}‘不是有效的日期格式！
price.verify.date.format1=第{0}行数据’{1}‘不是有效的日期格式！
price.verify.is.number=第{0}行‘{1}’需为数字！
price.verify.is.number1=第{0}行数据‘{1}’需为数字！
price.verify.info.type=第{0}行信息类别不是有效的数据！
price.verify.is.sign.contract=第{0}行是否能签署框架合同不是有效的数据！
price.verify.data.is.long=第{0}行’{1}‘需为整数！
price.verify.data.is.long1=第{0}行数据’{1}‘需为整数！
price.verify.price.date.control=第{0}行定价日期控制不是有效的数据！
price.verify.is.positive.long=第{0}行’{1}‘需为正整数！
price.verify.is.positive.long1=第{0}行数据’{1}‘需为正整数！
price.temporary.price.not.expansion=临时生效的价格不能进行价格扩充！
price.verify.is.sign.price.contract=第{0}行是否签署价格协议不是有效的数据！
price.maintenance.import.verify.serial.no.not.null=第{0}行序号不能为空！
price.maintenance.import.verify.material.code.not.bank=第{0}行物料号不能为空！
price.maintenance.import.verify.supplier.code.not.bank=第{0}行供应商编码不能为空！
price.maintenance.import.verify.price.unit.not.null=第{0}行价格单位不能为空！
price.maintenance.import.verify.price.unit=第{0}行价格单位需为正整数！
price.maintenance.import.verify.currency.not.bank=第{0}行币种不能为空！
price.maintenance.import.verify.current.unit.price.not.null=第{0}行单价（不含税）不能为空！
price.maintenance.import.verify.plan.delivery.day.not.null=第{0}行计划交货时间（天）不能为空！
price.maintenance.import.verify.pricing.date.control.not.bank=第{0}行定价日期控制不能为空！
price.maintenance.import.verify.effective.start.date.not.null=第{0}行价格有效期（起）不能为空！
price.maintenance.import.verify.effective.end.date.not.null=第{0}行价格有效期（止）不能为空！
price.maintenance.import.verify.tax.code.not.bank=第{0}行税码不能为空！
price.maintenance.import.verify.sign.contract.not.null=第{0}行是否能签署框架合同不能为空！
price.maintenance.import.verify.sign.price.agreement.not.null=第{0}行是否签署价格协议不能为空！
price.maintenance.import.verify.info.type=第{0}行信息类别不能为空！
price.maintenance.import.verify.purchase.group.code.not.bank=第{0}行采购组不能为空！
price.maintenance.import.verify.purchase.org.code.not.bank=第{0}行采购组织编码不能为空！
price.maintenance.import.verify.factory.not.bank=第{0}行工厂不能为空！
price.maintenance.import.verify.extreme.overcharge.min=第{0}行极限超收最小为0！
price.maintenance.import.verify.extreme.overcharge.scale=第{0}行极限超收为最多一位小数的数字！
price.maintenance.import.verify.molecule=第{0}行分子为大于0的正整数！
price.maintenance.import.verify.denominator=第{0}行分母为大于0的正整数！
price.adjust.import.verify.id.not.null=第{0}行ID不能为空！
price.adjust.import.verify.material.code.not.bank=第{0}行物料号不能为空！
price.adjust.import.verify.material.desc.not.bank=第{0}行物料描述不能为空！
price.adjust.import.verify.supplier.code.not.bank=第{0}行供应商编码不能为空！
price.adjust.import.verify.supplier.name.not.bank=第{0}行供应商名称不能为空！
price.adjust.import.verify.sign.contract.not.null=第{0}行是否能签署框架合同不能为空！
price.adjust.import.verify.sign.price.contract.not.null=第{0}行是否签署价格协议不能为空！
price.adjust.import.verify.factory.not.bank=第{0}行工厂不能为空！
price.adjust.import.verify.min.order.qty=第{0}行最小起订量为大于0的正整数！
price.adjust.import.verify.min.package.qty=第{0}行最小包装量为大于0的正整数！
price.adjust.import.verify.molecule=第{0}行分子为大于0的正整数！
price.adjust.import.verify.denominator=第{0}行分母为大于0的正整数！
price.adjust.import.verify.extreme.overcharge=第{0}行极限超收最小为0！
price.adjust.import.verify.extreme.overcharge.scale=第{0}行极限超收为最多一位小数的数字！
price.original.material.no.not.empty=原材料牌号不能为空！
price.original.material.no.max.character=原材料牌号限制输入20个字符！
price.original.material.name.not.empty=原材料名称不能为空！
price.original.material.name.max.character=原材料名称限制输入20个字符！
price.original.material.basic.unit.not.empty=基本单位不能为空！
price.original.material.verify.price.unit=价格单位是大于等于1的整数！
price.original.material.price.unit.not.null=价格单位不能为空！
price.original.material.price.not.null=价格不能为空！
price.original.material.price.positive=价格为正数！
price.original.material.price.scale=价格为最多两位小数的正数！
price.original.material.remark.max.character=备注限制输入200个字符！
price.ledger.re.create.goods.source=价格单号{0}重新创建货源失败！
price.supplier.not.exist.by.code=供应商{0}不存在！
price.import.sap.verify.price.contract=价格{0}存在未完成的价格合同签署待办！