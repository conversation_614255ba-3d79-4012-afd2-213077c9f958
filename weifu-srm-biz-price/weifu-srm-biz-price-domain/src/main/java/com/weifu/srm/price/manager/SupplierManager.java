package com.weifu.srm.price.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.price.request.SupplierCategoryReqDTO;
import com.weifu.srm.supplier.api.SupplierBasicApi;
import com.weifu.srm.supplier.api.SupplierCategoryApi;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.request.QuerySupplierCategoryListReqDTO;
import com.weifu.srm.supplier.response.SupplierBasicInfoRespDTO;
import com.weifu.srm.supplier.response.SupplierBasicSimpleInfoRespDTO;
import com.weifu.srm.supplier.response.SupplierCategoryRelationshipRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class SupplierManager {
    private final SupplierBasicApi supplierBasicApi;
    private final SupplierCategoryApi supplierCategoryApi;

    public List<SupplierBasicSimpleInfoRespDTO> listBySupplierCodes(List<String> supplierCodes){
        if(CollUtil.isEmpty(supplierCodes)) return null;
        log.info("查询供应商数据 param:{}",supplierCodes);
        ApiResponse<List<SupplierBasicSimpleInfoRespDTO>> resp =  supplierBasicApi.listBySupplierCodes(supplierCodes);
        log.info("查询供应商数据 resp:{}", JSONUtil.toJsonStr(resp));
        if(null != resp && BooleanUtil.isTrue(resp.getSucc()) && CollUtil.isNotEmpty(resp.getData())){
            return resp.getData();
        }
        return null;
    }


    /**
     * 根据供应商编码获取供应商基础信息 联系人信息 银行信息
     * @param supplierCodes 供应商编码
     * @return K:供应商编码 V:供应商信息 {@link SupplierBasicInfoRespDTO}
     */
    public Map<String,SupplierBasicInfoRespDTO> getSupplierInfoByCodes(List<String> supplierCodes){
        if(CollUtil.isEmpty(supplierCodes)) return null;

        ApiResponse<List<SupplierBasicInfoRespDTO>> resp = supplierBasicApi.getSupplierInfoByCodes(supplierCodes);
        if(null != resp && CollUtil.isNotEmpty(resp.getData())){
            return resp.getData().stream().collect(Collectors.toMap(v->v.getSapSupplierCode(),v->v,(v1,v2)->v1));
        }
        return null;
    }

    public Map<String,SupplierBasicSimpleInfoRespDTO> getSupplierSimpleMap(List<String> supplierCodes){
        List<SupplierBasicSimpleInfoRespDTO> list = this.listBySupplierCodes(supplierCodes);
        if(CollUtil.isEmpty(list)) return null;

        return list.stream().collect(Collectors.toMap(v->v.getSapSupplierCode(),v->v,(v1,v2)->v1));
    }


    /**
     * 根据供应商编码和品类编码获取临时供应商有效期限
     * @return K:supplierCode-categoryCode V:SupplierCategoryRelationshipRespDTO
     */
    public Map<String, SupplierCategoryRelationshipRespDTO> getSupplierCategory(List<SupplierCategoryReqDTO> list){
        if(CollUtil.isEmpty(list)) return null;

        List<String> supplierCodes = list.stream()
                .filter(v-> CharSequenceUtil.isNotBlank(v.getSapSupplierCode()))
                .map(SupplierCategoryReqDTO::getSapSupplierCode)
                .distinct().collect(Collectors.toList());
        List<String> categoryCodes = list.stream()
                .filter(v-> CharSequenceUtil.isNotBlank(v.getCategoryCode()))
                .map(SupplierCategoryReqDTO::getCategoryCode)
                .distinct().collect(Collectors.toList());

        if(CollUtil.isEmpty(supplierCodes) || CollUtil.isEmpty(categoryCodes)) return null;
        QuerySupplierCategoryListReqDTO param = new QuerySupplierCategoryListReqDTO();
        param.setSupplierCodes(supplierCodes);
        param.setCategoryCodes(categoryCodes);

        ApiResponse<List<SupplierCategoryRelationshipRespDTO>> resp =  supplierCategoryApi.querySupplierCategoryRelationship(param);
        if(Objects.isNull(resp) || CollUtil.isEmpty(resp.getData())) return null;

        //临时供应商
        List<SupplierCategoryRelationshipRespDTO> result = resp.getData().stream()
                .filter(v-> SupplierCategoryStatusEnum.TMP_STATUS.getCode().equals(v.getSupplierCategoryStatus()))
                .collect(Collectors.toList());
        if(CollUtil.isEmpty(result)) return null;

        return result.stream()
                .collect(Collectors.toMap(v->v.getSapSupplierCode()+"-"+v.getCategoryCode(), Function.identity(),(v1,v2)->v1));
    }


    /**
     * K:id
     */
    public Map<Long,SupplierBasicSimpleInfoRespDTO> getSupplierMapByIds(List<Long> ids){
        if(CollUtil.isEmpty(ids)) return null;

        ApiResponse<List<SupplierBasicSimpleInfoRespDTO>> resp = supplierBasicApi.listByIds(ids);
        if(CollUtil.isEmpty(resp.getData())) return null;

        return resp.getData().stream()
                .collect(Collectors.toMap(v->v.getId(),Function.identity(),(v1,v2)->v1));
    }

}
