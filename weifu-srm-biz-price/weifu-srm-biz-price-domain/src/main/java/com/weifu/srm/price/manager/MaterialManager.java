package com.weifu.srm.price.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.masterdata.api.MaterialApi;
import com.weifu.srm.masterdata.response.MaterialResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class MaterialManager {
    private final MaterialApi materialApi;


    /**
     * 获取物料数据
     * @param materialCodes 物料编码
     * @return K:物料编码 V:{@link MaterialResultDTO}
     */
    public Map<String, MaterialResultDTO> getMaterialMap(List<String> materialCodes){
        log.info("查询物料数据 param:{}", JSONUtil.toJsonStr(materialCodes));
        if (CollUtil.isEmpty(materialCodes)) return null;
        ApiResponse<List<MaterialResultDTO>> resp = materialApi.listByMaterialCodes(materialCodes);
        log.info("查询物料数据 resp:{}", JSONUtil.toJsonStr(resp));
        if(null != resp && CollUtil.isNotEmpty(resp.getData())){
            return resp.getData().stream().collect(Collectors.toMap(v->v.getMaterialCode(), Function.identity(),(v1,v2)->v1));
        }

        return null;
    }
}
