package com.weifu.srm.price.manager;

import cn.hutool.core.collection.CollUtil;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.user.api.SysFactoryApi;
import com.weifu.srm.user.request.FactoryQueryReqDTO;
import com.weifu.srm.user.response.FactoryDivisionRespDTO;
import com.weifu.srm.user.response.SysFactoryRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class SysFactoryManager {
    private final SysFactoryApi sysFactoryApi;

    public List<SysFactoryRespDTO> listByFactory(List<String> factory){
        if(CollUtil.isEmpty(factory)) return Collections.emptyList();

        ApiResponse<List<SysFactoryRespDTO>> resp = sysFactoryApi.listByFactory(factory);

        return resp.getData();
    }


    /**
     * 校验工厂事业部是否一致
     */
    public Boolean verifyFactoryDivisionEquals(List<String> factory){
        List<SysFactoryRespDTO> list = this.listByFactory(factory);
        if(CollUtil.isEmpty(list)) return true;

        List<String> divisionCodes = list.stream().map(SysFactoryRespDTO::getDivisionCode).distinct().collect(Collectors.toList());
        return CollUtil.size(divisionCodes) <= 1;
    }


    /**
     * 获取工厂 事业部编码
     * @return K:工厂 V:事业部编码
     */
    public Map<String,String> getFactoryDivisionMap(List<String> factory){
        List<SysFactoryRespDTO> list = this.listByFactory(factory);
        if(CollUtil.isNotEmpty(list)){
            return list.stream().collect(Collectors.toMap(v->v.getFactoryCode(),v->v.getDivisionCode()));
        }
        return null;
    }

    public Map<String, FactoryDivisionRespDTO> getFactoryMap(List<String> factory){
        if(CollUtil.isEmpty(factory)) return null;
        ApiResponse<List<FactoryDivisionRespDTO>> resp = sysFactoryApi.listByFactoryCodes(factory);
        if(Objects.nonNull(resp) && CollUtil.isNotEmpty(resp.getData())){
            return resp.getData().stream()
                    .collect(Collectors.toMap(v->v.getFactoryCode(), Function.identity(),(v1,v2)->v1));
        }
        return null;
    }

    public List<String> listFactoryByKeyword(String keyword){
        FactoryQueryReqDTO param = new FactoryQueryReqDTO();
        param.setKeyword(keyword);
        ApiResponse<List<SysFactoryRespDTO>> resp = sysFactoryApi.queryFactory(param);
        if(CollUtil.isNotEmpty(resp.getData())){
            return resp.getData().stream().map(SysFactoryRespDTO::getFactoryCode).collect(Collectors.toList());
        }
        return null;
    }

    public Map<String,SysFactoryRespDTO> listSimpleMapByFactory(List<String> factory){
        if(CollUtil.isEmpty(factory)) return null;

       List<SysFactoryRespDTO> list = this.listByFactory(factory);
       if (CollUtil.isEmpty(list)) return null;
        return list.stream().collect(Collectors.toMap(v->v.getFactoryCode(), Function.identity(),(v1,v2)->v1));
    }
}
