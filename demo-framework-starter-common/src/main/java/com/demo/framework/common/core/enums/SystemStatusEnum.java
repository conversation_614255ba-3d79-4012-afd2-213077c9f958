

package com.demo.framework.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @ProjectName 
 * @Description
 * @Date 2020/7/23 11:28
 */
@Getter
@AllArgsConstructor
public enum SystemStatusEnum {
    SUCCESS(200, "操作成功"),
    FAIL(100, "FAIL"),
    BUSY(101, "系统繁忙"),
    FLOW_ERROR(301, "流量限流异常"),
    FLOW_LIMIT(311, "限流(系统繁忙)"),
    FUSING(321, "熔断"),
    UNKNOWN_ERROR(4000, "未知错误"),
    ILLEGAL_HTTP(4001, "非法请求"),
    ILLEGAL_CHARACTERS(4002, "非法字符"),
    PARAMETER_ERROR(4003, "参数错误"),
    STATE_ERROR(4004, "状态错误"),
    DATA_EMPTY(4005, "没有数据"),
    DATA_UNIFORMITY(4006, "数据一致性错误"),
    ILLEGAL_URL(4007, "不合法的url"),
    ILLEGAL_USER(5000, "用户不存在"),
    NOT_LOGGED_IN(5001, "未登录"),
    TOKEN_INVALID(5002, "登录失效，请重新登录"),
    TOKEN_ERROR(5003, "token异常"),
    INSUFFICIENT_AUTHORITY(5004, "权限不足"),
    DUPLICATE_NAME(5005, "名称重复");


    private final Integer code;
    private final String message;


}
