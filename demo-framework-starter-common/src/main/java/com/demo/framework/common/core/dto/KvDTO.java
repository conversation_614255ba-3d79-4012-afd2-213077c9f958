
package com.demo.framework.common.core.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 公共返回对象dto,tree树形结构对象
 *
 */
@Data
public class KvDTO<K, V>  {

    private K key;
    private V value;

    private List<V> values;

    private List<KvDTO<K,V>> children;

    private String user;
    private Date date;

    public KvDTO() {
        super();
    }

    public KvDTO(K key, V value) {
        this.key = key;
        this.value = value;
    }

    public KvDTO(K key, List<V> values) {
        this.key = key;
        this.values = values;
    }

    public KvDTO(K key) {
        this.key = key;
    }

    public void addChildren(KvDTO<K, V> dto) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(dto);
    }

    public void addChildren(List<KvDTO<K,V>> kvList) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.addAll(kvList);
    }
}

