
package com.demo.framework.common.core.dto;

import cn.hutool.core.lang.Assert;
import com.demo.framework.common.core.enums.SystemStatusEnum;
import com.demo.framework.common.core.exception.ErrorCode;
import lombok.Data;


/**
 * 公共返回对象dto
 *
 * <AUTHOR>
 */
@Data
public class ResponseDTO<T> {

    /**
     * 数值结果,如200,404
     */
    private Integer code;

    /**
     * 文本信息
     */
    private String message;

    /**
     * 总条数
     */
    private Long count;

    /**
     * 数据对象
     */
    private T data;

    public static ResponseDTO<Object> success() {
        return new ResponseDTO<>();
    }

    public static <T> ResponseDTO<T> success(T data) {
        return new ResponseDTO<>(data);
    }

    public static <T> ResponseDTO<T> success(T data, Long count) {
        return new ResponseDTO<>(data, count);
    }

    public static <T> ResponseDTO<T> success(T data, String message) {
        return new ResponseDTO<>(SystemStatusEnum.SUCCESS.getCode(), data, message);
    }

    public static ResponseDTO<Object> fail(int status, String message) {
        return new ResponseDTO<>(status, null, message);
    }

    public static ResponseDTO<Object> fail(String message) {
        return new ResponseDTO<>(SystemStatusEnum.FAIL.getCode(), null, message);
    }

    public static ResponseDTO<Object> fail() {
        return new ResponseDTO<>(SystemStatusEnum.FAIL.getCode(), null, SystemStatusEnum.FAIL.getMessage());
    }

    public static <T> ResponseDTO<T> error(Integer code, String message) {
        Assert.notEquals(SystemStatusEnum.SUCCESS.getCode(), code, "code 必须是错误的！");
        return new ResponseDTO<>(code, null, message);
    }
    public static <T> ResponseDTO<T> error(ErrorCode errorCode) {
        return error(errorCode.getCode(), errorCode.getMsg());
    }

    public ResponseDTO() {
        this.code = SystemStatusEnum.SUCCESS.getCode();
        this.message = SystemStatusEnum.SUCCESS.getMessage();
    }

    public ResponseDTO(T data) {
        this.code = SystemStatusEnum.SUCCESS.getCode();
        this.message = SystemStatusEnum.SUCCESS.getMessage();
        this.data = data;
    }

    public ResponseDTO(T data, Long count) {
        this.code = SystemStatusEnum.SUCCESS.getCode();
        this.data = data;
        this.count = count;
    }


    public ResponseDTO(int status, T data, String message) {
        this.code = status;
        this.data = data;
        this.message = message;
    }


    /**
     * @return 如果成功返回true，否则返回 false。
     */
    public boolean isSuccess() {
        return SystemStatusEnum.SUCCESS.getCode().equals(this.code);
    }


}
