
package com.demo.framework.common.core.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公共返回对象dto，键值对形式
 *
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode
public class VtDTO {

    private Integer value;
    private String label;
    private Boolean status;

    public VtDTO(Integer value, String label) {
        this.value = value;
        this.label = label;
        this.status = true;
    }

}
