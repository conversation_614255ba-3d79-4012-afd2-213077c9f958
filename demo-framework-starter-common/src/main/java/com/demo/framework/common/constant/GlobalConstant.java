package com.demo.framework.common.constant;

/**
 * 全局常量
 *
 * <AUTHOR>
 */
public class GlobalConstant {
    private GlobalConstant(){

    }
    /**
     * 通用状态 默认：0，是：1，否：2
     */
    public static final int NONE = 0;
    public static final Long LONG_NONE = 0L;
    public static final int YES = 1;
    public static final int NO = 2;

    /**
     * 数字1-9
     */
    public static final int ONE = 1;
    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int FIVE = 5;
    public static final int SIX = 6;
    public static final int SEVEN = 7;
    public static final int EIGHT = 8;
    public static final int NINE = 9;

    /**
     * 状态-启用
     */
    public static final Integer STATUS_ENABLE = YES;

    /**
     * 状态-禁用
     */
    public static final Integer STATUS_DISABLE = NO;

    /**
     * 横线
     */
    public static final String SYMBOL_HORIZONTAL = "-";

    /**
     * 逗号
     */
    public static final String SYMBOL_COMMA = ",";

    public static final String SYMBOL_ORG_DIVISION = "::";
    /**
     * 冒号
     */
    public static final String SYMBOL_COLON = ":";
    /**
     * 空字符
     */
    public static final String SYMBOL_EMPTY_STR = "";
    /**
     * 百分号
     */
    public static final String SYMBOL_PERCENT = "%";
}
