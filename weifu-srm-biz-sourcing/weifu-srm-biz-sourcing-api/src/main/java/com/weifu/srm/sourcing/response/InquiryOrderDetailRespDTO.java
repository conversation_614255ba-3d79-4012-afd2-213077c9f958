package com.weifu.srm.sourcing.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.sourcing.response.bidding.BiddingListRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class InquiryOrderDetailRespDTO {

    @ApiModelProperty(value = "询价单号")
    private String inquiryNo;
    @ApiModelProperty(value = "询价名称")
    private String inquiryName;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "询价轮次")
    private Integer inquiryRound;
    @ApiModelProperty(value = "首次询价发出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstInquiryStartTime;
    @ApiModelProperty(value = "本轮询价发出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inquiryStartTime;
    @ApiModelProperty(value = "品类编码")
    private String categoryCode;
    @ApiModelProperty(value = "品类名称")
    private String categoryName;
    @ApiModelProperty(value = "品类英文名称")
    private String categoryNameEn;
    @ApiModelProperty(value = "询价工程师用户ID")
    private Long inquiryEngineerUserId;
    @ApiModelProperty(value = "询价工程师用户RealName")
    private String inquiryEngineerUserName;
    @ApiModelProperty(value = "询价工程师威孚邮箱")
    private String inquiryEngineerEmail;
    @ApiModelProperty(value = "询价方式")
    private String inquiryType;
    @ApiModelProperty(value = "样件询价意愿")
    private Integer sampleInquiryIntention;
    @ApiModelProperty(value = "报价意愿反馈期限（天）")
    private Integer quotationFeedbackDays;
    @ApiModelProperty(value = "报价意愿反馈期限")
    private Date quotationFeedbackDeadline;
    @ApiModelProperty(value = "报价截止期限（天）")
    private Integer quotationCutoffDays;
    @ApiModelProperty(value = "报价截止期限")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date quotationCutoffDeadline;
    @ApiModelProperty(value = "批产报价模板")
    private String massProductionQuotationTemplate;
    @ApiModelProperty(value = "样件报价模板")
    private String sampleQuotationTemplate;
    @ApiModelProperty(value = "寻源方式")
    private String sourceType;
    @ApiModelProperty(value = "单一来源说明")
    private String singleSourceDesc;
    @ApiModelProperty(value = "历史询价单号")
    private String historyInquiryNo;
    @ApiModelProperty(value = "历史竞价单号")
    private String historyBidNo;
    @ApiModelProperty(value = "历史竞价单")
    private BiddingListRespDTO historyBiddingOrder;
    @ApiModelProperty(value = "是否待录入报价")
    private Integer isProxyQuotation;
    @ApiModelProperty(value = "失败原因")
    private String failReason;
    @ApiModelProperty(value = "关闭原因")
    private String closeReason;
    @ApiModelProperty(value = "是否是LastCall 1-是|0-否")
    private Integer isLastCall;
    @ApiModelProperty(value = "供应商选择评审说明")
    private String supplierReviewBasisDesc;
    @ApiModelProperty(value = "备注")
    private String inquiryDesc;
    @ApiModelProperty(value = "是否已定点 1-是|0-否")
    private Integer isDesignated;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @ApiModelProperty(value = "是否可以创建商务定点单 1-是|0-否")
    private Integer isCanCreateBusinessDesignationOrder = 0;
    @ApiModelProperty(value = "询价单附件")
    private List<AttachmentRecordRespDTO> inquiryAttachments;
    @ApiModelProperty(value = "代录入说明附件")
    private List<AttachmentRecordRespDTO> inquiryProxyQuotationAttachments;
    @ApiModelProperty(value = "供应商选择评审依据附件")
    private List<AttachmentRecordRespDTO> inquirySupplierReviewBasisAttachments;
    @ApiModelProperty(value = "供应商信息")
    private List<InquiryOrderSupplierRespDTO> inquirySuppliers;
    @ApiModelProperty(value = "零件信息")
    private List<InquiryOrderMaterialDetailRespDTO> inquiryMaterials;
}
