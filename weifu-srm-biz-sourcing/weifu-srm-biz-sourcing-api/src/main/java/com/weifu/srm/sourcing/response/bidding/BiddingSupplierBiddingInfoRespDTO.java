package com.weifu.srm.sourcing.response.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BiddingSupplierBiddingInfoRespDTO {
    @ApiModelProperty(value = "供应商ID")
    private Integer supplierId;
    @ApiModelProperty(value = "供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(value = "供应商名称-EN")
    private String supplierNameEn;
    @ApiModelProperty(value = "供应商参与情况 1-已确认|0-已拒绝|null-未回复")
    private Integer biddingIntention;
    @ApiModelProperty(value = "是否提交过报价 1-是|null-否")
    private Integer isPriced;
}
