package com.weifu.srm.sourcing.response;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class QuotationOrderRespDTO {

    @ApiModelProperty(value = "询价单号")
    private String inquiryNo;
    @ApiModelProperty(value = "询价单名称")
    private String inquiryName;
    @ApiModelProperty(value = "供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(value = "供应商名称-EN")
    private String supplierNameEn;
    @ApiModelProperty(value = "询价方式")
    private String inquiryType;
    @ApiModelProperty(value = "询价轮次")
    private Integer inquiryRound;
    @ApiModelProperty(value = "是否是Last Call轮次 1-是|0-否")
    private Integer isLastCall;
    @ApiModelProperty(value = "邀请报价时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date inviteQuotationTime;
    @ApiModelProperty(value = "报价意愿反馈截止日期")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private Date quotationFeedbackDeadline;
    @ApiModelProperty(value = "报价截止日期")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private Date quotationCutoffDeadline;
    @ApiModelProperty(value = "询价状态 INQUIRING-询价中|INQUIRY_END-询价截止")
    private String inquiryStatus;
    @ApiModelProperty(value = "意愿反馈情况 不为空-已响应|为空-未响应 1-参与|0-拒绝")
    private String quotationIntention;
    @ApiModelProperty(value = "我的报价状态 ABANDON-放弃报价|REJECTED-拒绝报价|PENDING-待处理|QUOTING-报价中|QUOTED-报价完成")
    private String quotationStatus;
    @ApiModelProperty(value = "是否展示过供应商报价须知弹窗 1-已弹窗|0-未弹窗")
    private Integer isOpenedWindows;
    @ApiModelProperty(value = "供应商报价数据状态 INITIALIZE-初始化|DRAFT-草稿|SUBMITTED-已提交")
    private String dataStatus;
}
