package com.weifu.srm.sourcing.request;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class BusinessDesignationOrderSupplierReqDTO extends OperateUserReqDTO {
    @ApiModelProperty(value = "定价日期控制", required = true)
    private String pricingDateControl;
    @ApiModelProperty(value = "确认控制", required = true)
    private String confirmControl;
    @ApiModelProperty(value = "信息类别", required = true)
    private String infoType;
    @ApiModelProperty(value = "极限超收（%）", required = true)
    private BigDecimal extremeOvercharge;
    @ApiModelProperty(value = "供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(value = "税码")
    private String taxCode;
    @ApiModelProperty(value = "订单单位")
    private String orderUnit;
    @ApiModelProperty(value = "分母")
    private Integer denominator;
    @ApiModelProperty(value = "分子")
    private Integer molecule;
    @ApiModelProperty(value = "价格有效期（起）- yyyy-MM-dd")
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private Date effectiveStartDate;
    @ApiModelProperty(value = "价格有效期（止）- yyyy-MM-dd")
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private Date effectiveEndDate;
    @ApiModelProperty(value = "是否存在有效的框架协议 1-是|0-否")
    private Integer isFrameworkAgreement;
    @ApiModelProperty(value = "是否签署合同 1-是|0-否")
    private Integer isSignContract;
    @ApiModelProperty(value = "是否签署价格协议 1-是|0-否")
    private Integer isSignPriceAgreement;
    @ApiModelProperty(value = "定点物料条件项")
    private List<BusinessDesignationOrderConditionalReqDTO> conditions;
    @ApiModelProperty(value = "商务定点单供应商合同约束项佐证材料附件")
    private List<AttachmentRecordReqDTO> attachments;
}
