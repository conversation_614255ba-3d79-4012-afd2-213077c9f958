package com.weifu.srm.sourcing.response.ppap;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description PPAP批产放行-基础信息response
 * @Version 1.0
 */
@ApiModel(description = "PPAP批产放行-基础信息response")
@Data
public class PpapReleaseInfoRespDTO {
    @ApiModelProperty(value = "放行单号")
    private String releaseNo;

    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    @ApiModelProperty(value = "需求号")
    private String requirementNo;

    @ApiModelProperty(value = "商务定点单号")
    private String designationOrderNo;

    @ApiModelProperty(value = "计划编号（最新的一条）")
    private String planNo;

    @ApiModelProperty(value = "品类编码")
    private String categoryCode;

    @ApiModelProperty(value = "品类名称")
    private String categoryName;

    @ApiModelProperty(value = "品类名称英文")
    private String categoryNameEn;

    @ApiModelProperty(value = "物料号")
    private String materialNo;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    @ApiModelProperty(value = "需求方")
    private String requirement;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商名称英文")
    private String supplierNameEn;

    @ApiModelProperty(value = "品类工程师id")
    private Long categoryEngineerId;

    @ApiModelProperty(value = "品类工程师名称")
    private String categoryEngineerName;

    @ApiModelProperty(value = "质量工程师id")
    private Long qualityEngineerId;

    @ApiModelProperty(value = "质量工程师名称")
    private String qualityEngineerName;

    @ApiModelProperty(value = "推荐质量工程师id")
    private Long recommendEngineerId;

    @ApiModelProperty(value = "推荐质量工程师名称")
    private String recommendEngineerName;

    @ApiModelProperty(value = "放行状态")
    private String releaseStatus;

    @ApiModelProperty(value = "放行状态名称")
    private String releaseStatusName;

    @ApiModelProperty(value = "阶段")
    private String stage;

    @ApiModelProperty(value = "阶段名称")
    private String stageName;

    @ApiModelProperty(value = "PPAP执行状态")
    private String executeStatus;

    @ApiModelProperty(value = "PPAP执行状态名称")
    private String executeStatusName;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "零件采购计划PPAP放行时间")
    private String partPlanReleaseDate;

    @ApiModelProperty(value = "关闭前放行状态")
    private String closeStatus;

    @ApiModelProperty(value = "cpe组长确认状态")
    private String cpeConfirmStatus;

    @ApiModelProperty(value = "cpe组长确认状态名称")
    private String cpeConfirmStatusName;
}
