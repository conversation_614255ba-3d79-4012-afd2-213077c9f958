package com.weifu.srm.sourcing.request.ppap;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description PPAP计划等级-日志request
 * @Version 1.0
 */
@ApiModel(description = "PPAP计划等级-日志request")
@Data
public class PpapPlanGradeLogReqDTO {
    @ApiModelProperty("等级id")
    private Long gradeId;

    @NotBlank(message = "logType is required.")
    @ApiModelProperty("日志类型：SETTING设置,TEMPLATE模板")
    private String logType;
}
