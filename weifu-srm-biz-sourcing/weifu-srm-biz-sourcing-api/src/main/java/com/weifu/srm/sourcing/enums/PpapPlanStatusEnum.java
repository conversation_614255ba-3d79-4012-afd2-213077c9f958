package com.weifu.srm.sourcing.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description PPAP计划-状态枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum PpapPlanStatusEnum {
    DRAFT("DRAFT", "草稿"),
    PUBLISH_EXECUTE("PUBLISH_EXECUTE", "发布执行"),
    PLAN_WITHDRAW("PLAN_WITHDRAW", "计划撤回"),
    EXECUTION_COMPLETED("EXECUTION_COMPLETED", "执行完成"),
    PLAN_CLOSE("PLAN_CLOSE", "计划关闭");
    private final String code;

    private final String name;

    public static String getNameByCode(String code) {
        for (PpapPlanStatusEnum statusEnum : PpapPlanStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }

}