package com.weifu.srm.sourcing.response.bidding;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BiddingSupplierItemRespDTO {
    @ApiModelProperty(value = "竞价单号")
    private String biddingNo;
    @ApiModelProperty(value = "供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(value = "供应商名称-EN")
    private String supplierNameEn;
    @ApiModelProperty(value = "轮次1,2,3...")
    private Integer round;
    @ApiModelProperty(value = "报价金额")
    private BigDecimal price;
    @ApiModelProperty(value = "是否沿用上轮报价 1-是|0-否")
    private Integer isUsePreviousPrice;
    @ApiModelProperty(value = "沿用上轮报价方式 1-页面选择不出价延用上路报价|2-竞价结束没有出价自动沿用上轮报价")
    private Integer usePreviousPriceType;
    @ApiModelProperty(value = "该轮竞价开始时间 yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_MINUTE_PATTERN, timezone = "GMT+8")
    private Date biddingStartTime;
    @ApiModelProperty(value = "该轮竞价结束时间 yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_MINUTE_PATTERN, timezone = "GMT+8")
    private Date biddingEndTime;
    @ApiModelProperty(value = "排名")
    private Integer ranking;
    @ApiModelProperty(value = "当前轮次是否竞价中 -1是|0-否")
    private Integer isActive = 0;
}
