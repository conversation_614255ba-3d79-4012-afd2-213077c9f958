package com.weifu.srm.sourcing.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class InquiryOrderMaterialRespDTO {

    @ApiModelProperty(value = "询价单号")
    private String inquiryNo;
    @ApiModelProperty(value = "需求号")
    private String requirementPartsNo;
    @ApiModelProperty(value = "物料号")
    private String materialCode;
    @ApiModelProperty(value = "物料描述")
    private String materialDesc;
    @ApiModelProperty(value = "相似物料号")
    private String similarMaterialCode;
    @ApiModelProperty(value = "需求类型 寻源/采购")
    private String requirementType;
    @ApiModelProperty(value = "采购组织编码")
    private String purchaseGroupCode;
    @ApiModelProperty(value = "工厂")
    private String factory;
    @ApiModelProperty(value = "物流目的地")
    private String logisticsDestination;
    @ApiModelProperty(value = "预计年采购量")
    private Integer budgetAnnualPurchaseQuantity;
    @ApiModelProperty(value = "样件需求数量")
    private Integer sampleRequirementQuantity;
    @ApiModelProperty(value = "基本单位")
    private String unit;
    @ApiModelProperty(value = "目标价格（元）未税")
    private BigDecimal targetPrice;
    @ApiModelProperty(value = "图纸编号")
    private String drawingNo;
    @ApiModelProperty(value = "图纸版本")
    private String drawingVersion;
    @ApiModelProperty(value = "首批样品交付周期说明（天）")
    private Integer firstSampleDeliveryDays;
    @ApiModelProperty(value = "批产交付周期说明（天）")
    private Integer massProductionDeliveryDays;
    @ApiModelProperty(value = "推荐供应商名称")
    private String recommendedSupplierName;
    @ApiModelProperty(value = "推荐供应商属性")
    private String recommendedSupplierType;
}
