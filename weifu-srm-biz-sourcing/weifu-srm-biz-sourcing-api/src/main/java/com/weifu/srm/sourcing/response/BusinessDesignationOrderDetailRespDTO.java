package com.weifu.srm.sourcing.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class BusinessDesignationOrderDetailRespDTO {

    @ApiModelProperty(value = "定点单号")
    private String designationOrderNo;
    @ApiModelProperty(value = "询价单号")
    private String inquiryNo;
    @ApiModelProperty(value = "询价轮次")
    private Integer inquiryRond;
    @ApiModelProperty(value = "是否是LastCall 1-是|0-否")
    private Integer isLastCall;
    @ApiModelProperty(value = "品类编码")
    private String categoryCode;
    @ApiModelProperty(value = "品类名称")
    private String categoryName;
    @ApiModelProperty(value = "品类名称-EN")
    private String categoryNameEn;
    @ApiModelProperty(value = "寻源名称")
    private String sourceType;
    @ApiModelProperty(value = "品类工程师ID")
    private Long inquiryEngineerUserId;
    @ApiModelProperty(value = "品类工程师名称")
    private String inquiryEngineerUserName;
    @ApiModelProperty(value = "物料")
    private List<BusinessDesignationOrderMaterialDetailRespDTO> materials;
    @ApiModelProperty(value = "附件")
    private List<AttachmentRecordRespDTO> attachments;
}
