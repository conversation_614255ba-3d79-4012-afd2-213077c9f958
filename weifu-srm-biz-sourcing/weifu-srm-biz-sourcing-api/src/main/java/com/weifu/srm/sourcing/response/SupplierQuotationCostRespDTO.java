package com.weifu.srm.sourcing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("L2层级报价分析")
public class SupplierQuotationCostRespDTO{
    @ApiModelProperty("供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("轮次")
    private Integer quotationRound;

    @ApiModelProperty("是否发起 LastCall 1:发起")
    private Integer isLastCall;

    @ApiModelProperty("总成本")
    private BigDecimal totalCost;

    @ApiModelProperty("成本明细")
    private List<SupplierQuotationCostDetailRespDTO> costDetails;
}
