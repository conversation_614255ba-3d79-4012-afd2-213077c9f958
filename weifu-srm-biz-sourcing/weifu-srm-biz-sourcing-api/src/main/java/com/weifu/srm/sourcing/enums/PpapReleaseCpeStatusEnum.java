package com.weifu.srm.sourcing.enums;

import com.weifu.srm.sourcing.constants.ServiceConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;


/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 批产放行-状态枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum PpapReleaseCpeStatusEnum {

    UNCONFIRMED("UNCONFIRMED", "未确认", "Unconfirmed"),
    CONFIRMING_IN_PROGRESS("CONFIRMING_IN_PROGRESS", "确认中", "Confirming in progress"),
    PASSED("PASSED", "已通过", "Passed"),
    FAIL("FAIL", "未通过", "Fail");

    private final String code;

    private final String name;
    private final String englishName;

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        boolean isEnglish = isEnglish();
        for (PpapReleaseCpeStatusEnum statusEnum : PpapReleaseCpeStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return isEnglish ? statusEnum.getEnglishName() : statusEnum.getName();
            }
        }
        return null;
    }

    private static boolean isEnglish() {
        return ServiceConstants.EN_US.equals(LocaleContextHolder.getLocale().toString());
    }
}
