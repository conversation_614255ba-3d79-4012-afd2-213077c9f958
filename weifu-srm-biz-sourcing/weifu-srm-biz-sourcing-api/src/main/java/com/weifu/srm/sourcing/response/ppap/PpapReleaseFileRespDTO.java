package com.weifu.srm.sourcing.response.ppap;

import com.weifu.srm.sourcing.response.AttachmentRecordRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(description = "PPAP批产放行-相关文件response")
@Data
public class PpapReleaseFileRespDTO {

    @ApiModelProperty(value = "过程审核及生产验证报告")
    private List<AttachmentRecordRespDTO> processAuditAndProductReportList;

    @ApiModelProperty(value = "PSW签署文件")
    private List<AttachmentRecordRespDTO> pswSignatureFileList;

    @ApiModelProperty(value = "图纸文件")
    private List<AttachmentRecordRespDTO> drawingFileList;

    @ApiModelProperty(value = "ots报告")
    private List<AttachmentRecordRespDTO> otsAnnexList;
}
