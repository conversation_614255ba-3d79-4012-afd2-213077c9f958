package com.weifu.srm.sourcing.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class QuotationAnalysisReqDTO {
    @ApiModelProperty("询价单号")
    @NotBlank(message = "询价单号不能为空！")
    private String inquiryNo;

    @ApiModelProperty("供应商商编码")
    private List<String> sapSupplierCodes;

    @ApiModelProperty("物料编码")
    private List<String> materialCodes;

    @ApiModelProperty("L2层级数据筛选 RAW_MATERIAL_COST_FEE(\"原材料成本（元）\"),\n" +
            "    OUTSOURCING_COST_FEE(\"外购/外协件陈本（元）\"),\n" +
            "    MANUFACTURING_COST_FEE(\"制造成本（元）\"),\n" +
            "    MOULD_AMORTIZATION_FEE(\"模具摊销费用（元）\"),\n" +
            "    PACKAGE_FEE(\"包装费用（元）\"),\n" +
            "    MANAGEMENT_FEE(\"管理费用（元）\"),\n" +
            "    FINANCIAL_FEE(\"财务加成（元）\"),\n" +
            "    PROFIT_MARKUP(\"利润加成（元）\"),\n" +
            "    LOGISTICS_FEE(\"物流费用（元）\");")
    private List<String> twoLevelConditions;

    @ApiModelProperty("轮次 LastCall传LastCall 其余传1,2,3数字")
    private String round;
}
