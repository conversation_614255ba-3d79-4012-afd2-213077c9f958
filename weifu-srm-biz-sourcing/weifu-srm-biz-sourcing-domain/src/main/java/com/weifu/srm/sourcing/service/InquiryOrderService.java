package com.weifu.srm.sourcing.service;

import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.sourcing.repository.po.InquiryOrderPO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderCloseReqDTO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderCloseReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderDelayQuotationDeadlineReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderEndReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderLastCallReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderReleaseRequirementReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderQueryReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderReInquiryReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderSampleOrderReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderSubmitReqDTO;
import com.weifu.srm.sourcing.request.SourcingApprovalReqDTO;
import com.weifu.srm.sourcing.request.SupplierLastCallQueryReqDTO;
import com.weifu.srm.sourcing.response.InquiryOrderDetailRespDTO;
import com.weifu.srm.sourcing.response.InquiryOrderRespDTO;
import com.weifu.srm.sourcing.response.InquiryOrderSupplierQuotationInfoRespDTO;
import com.weifu.srm.sourcing.response.SupplierLastCallRespDTO;

import java.util.List;

public interface InquiryOrderService {

    PageResponse<InquiryOrderRespDTO> queryPage(InquiryOrderQueryReqDTO paramDTO);

    List<InquiryOrderSupplierQuotationInfoRespDTO> queryCurrentRoundQuotationGeneral(String inquiryNo);

    PageResponse<InquiryOrderRespDTO> queryByInquiryNoLike(InquiryOrderQueryReqDTO paramDTO);

    InquiryOrderPO save(InquiryOrderReqDTO paramDTO);

    InquiryOrderPO copySave(InquiryOrderReqDTO paramDTO);

    InquiryOrderPO submit(InquiryOrderReqDTO paramDTO);

    InquiryOrderPO submitNew(InquiryOrderSubmitReqDTO paramDTO);

    InquiryOrderPO copySubmit(InquiryOrderReqDTO paramDTO);

    /**
     * @param inquiryNo 询价单号
     * @param useTo     用途 1-询价单详情 | 0-报价单详情
     */
    InquiryOrderDetailRespDTO queryDetail(String inquiryNo, Integer useTo);

    InquiryOrderDetailRespDTO queryAllDetail(String inquiryNo, Integer useTo);

    void delayQuotationCutoffDeadline(InquiryOrderDelayQuotationDeadlineReqDTO paramDTO);

    void close(InquiryOrderCloseReqDTO paramDTO);

    void approval(SourcingApprovalReqDTO paramDTO);

    void reInquiry(InquiryOrderReInquiryReqDTO paramDTO);

    void end(InquiryOrderEndReqDTO paramDTO);

    void commitInquiryResultAnalysis(InquiryOrderPO inquiryOrder, List<SupplierQuotationOrderPO> sqos);

    void lastCall(InquiryOrderLastCallReqDTO paramDTO);

    List<SupplierLastCallRespDTO> queryLastCallSupplier(SupplierLastCallQueryReqDTO paramDTO);

    void feedbackDeadlineDataRectify(InquiryOrderPO inquiryOrder);

    void designation(BusinessDesignationOrderReqDTO paramDTO);

    void revokeDesignation(BusinessDesignationOrderCloseReqDTO paramDTO);

    int statisticsPendingBy(Long userId);

    void createSampleOrder(List<InquiryOrderSampleOrderReqDTO> paramDTOs);

    void deleteSampleOrder(List<InquiryOrderSampleOrderReqDTO> paramDTOs);

    void releaseNoDesignatedRequirements(InquiryOrderPO inquiryOrder);

    void ppapClose(InquiryOrderReleaseRequirementReqDTO paramDTO);
}
