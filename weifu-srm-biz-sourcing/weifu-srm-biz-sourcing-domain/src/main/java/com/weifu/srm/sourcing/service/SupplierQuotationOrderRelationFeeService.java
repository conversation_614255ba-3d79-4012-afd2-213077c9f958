package com.weifu.srm.sourcing.service;

import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderRelationFeePO;
import com.weifu.srm.sourcing.request.SupplierQuotationCopyReqDTO;
import com.weifu.srm.sourcing.request.SupplierQuotationOrderSaveReqDTO;

import java.util.List;

public interface SupplierQuotationOrderRelationFeeService {

    void save(SupplierQuotationOrderPO sqo, SupplierQuotationOrderSaveReqDTO paramDTO);

    SupplierQuotationOrderRelationFeePO queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    List<SupplierQuotationOrderRelationFeePO> queryBy(List<Long> quotationOrderMaterialIds);

    void copy(SupplierQuotationCopyReqDTO paramDTO);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);
}
