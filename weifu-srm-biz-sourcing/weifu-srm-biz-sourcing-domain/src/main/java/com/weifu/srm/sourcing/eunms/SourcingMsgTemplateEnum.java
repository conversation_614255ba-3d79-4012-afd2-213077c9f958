package com.weifu.srm.sourcing.eunms;

import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.TodoClsEnum;
import lombok.Getter;

@Getter
public enum SourcingMsgTemplateEnum {

    SIT_MSG_INQUIRY_MATERIAL_SOURCING("物料寻源", MessageClsEnum.MATERIAL_SOURCING, null, IconTypeEnum.GENERAL,
            "您收到了一个${0}${1}的物料子需求－${2}，快去查看吧～"),

    SIT_MSG_INQUIRY_VIEW_QUOTATION("查看报价", MessageClsEnum.QUOTE_VIEW, null, IconTypeEnum.GENERAL,
            "您发送的询价单${0}已经可以查看报价了，快去查看吧～"),

    SIT_MSG_INQUIRY_FAILURE("询价失败", MessageClsEnum.INQUIRY_FAILURE, null, IconTypeEnum.GENERAL,
            "您发送的询价单${0}询价失败，失败原因为${1}，快去查看吧～"),

    SIT_MSG_INQUIRY_MATERIAL_QUOTE_INVITATION("物料报价邀请", MessageClsEnum.MATERIAL_QUOTATION_INVITATION, null, IconTypeEnum.GENERAL,
            "您收到了一个询价单${0}（${1}轮）的报价邀请，快去查看吧～"),

    SIT_MSG_DESIGNATION_NOTIFICATION("定点通知", MessageClsEnum.DESIGNATION, null, IconTypeEnum.GENERAL,
            "您收到了单号为${0}的定点通知书（询价单${1}），快去查看吧～"),

    SIT_MSG_BIDDING_COMPLETE("竞价完成通知", MessageClsEnum.BIDDING_COMPLETE_NOTICE, null, IconTypeEnum.GENERAL,
            "您发布的竞价活动：{${0}}，已经竞价完成，可在“竞价单管理”页面查看竞价结果~"),

    SIT_MSG_BIDDING_FAILURE("竞价失败通知", MessageClsEnum.BIDDING_FAILURE_NOTICE, null, IconTypeEnum.GENERAL,
            "您发布的竞价活动：{${0}}，不满足竞价参与供应商最低为2家的要求，竞价失败！失败原因为：资料更新~"),

    SIT_MSG_BIDDING_INVITATION("竞价邀请通知", MessageClsEnum.BIDDING_INVITATION_NOTICE, null, IconTypeEnum.GENERAL,
            "威孚高科集团邀请您参与竞价，竞价活动名称：{${0}}，竞价活动参与意愿反馈截止时间：{${1}}，竞价活动开始时间：{${2}}，期待您的参与~"),

    SIT_MSG_BIDDING_TERMINATE("竞价终止通知", MessageClsEnum.BIDDING_TERMINATION_NOTICE, null, IconTypeEnum.GENERAL,
            "尊敬的供应商用户，很抱歉的通知您：因{${0}}，{${1}}终止了~"),

    SIT_MSG_DRAWING_CHANGE_NOTICE("图纸变更通知", MessageClsEnum.DRAWING_CHANGE_NOTICE, null, IconTypeEnum.GENERAL,
            "尊敬的${supplierName}用户：\n您好！现询价单${inquiryNo}中的${materialNo}${materialDescription}的图纸发生了变更，详见您的图纸变更清单，如果任何疑问请联系${categoryEngineerName}，邮箱：${engineerEmail}，谢谢！"),

    OTS_INSPECTION_REPORT_RETURNED_BY_LEADER("OTS检验报告已被组长退回", MessageClsEnum.OTS_INSPECTION_REPORT_RETURNED_BY_LEADER, null, IconTypeEnum.GENERAL,
            "放行单：${releaseNo} 已被CPE组长（${cpeMasterName}）退回至CPE（${categoryEngineerName}），请及时处理，谢谢！"),

    BATCH_PRODUCTION_RELEASE_SHEET_APPROVAL_REJECTED("批产放行单审批拒绝", MessageClsEnum.BATCH_PRODUCTION_RELEASE_SHEET_APPROVAL_REJECTED, null, IconTypeEnum.GENERAL,
            "放行单：${releaseNo} 的PPAP任务已被SQE组长/授权质量负责人${rejectName}审批拒绝，拒绝原因为：${approvalComment}。批产放行单已回到“待提交OTS检验报告”状态。请CPE工程师${categoryEngineerName}和CPE组长${cpeMasterName}及时和所有相关方沟通和进一步处理！"),

    BATCH_PRODUCTION_RELEASE_SHEET_CLOSED("批产放行单已关闭", MessageClsEnum.BATCH_PRODUCTION_RELEASE_SHEET_CLOSED, null, IconTypeEnum.GENERAL,
            "放行单：${releaseNo} 已被${closeName}关闭，关闭说明：${closeComment}。如您有任何问题，请及时和CPE工程师${categoryEngineerName}和CPE组长${cpeMasterName}进行沟通和处理！"),


    BATCH_PRODUCTION_RELEASE_DOCUMENT_WAITING_FOR_CPE_LEADER_CONFIRMATION("批产放行文件待CPE组长确认", MessageClsEnum.BATCH_PRODUCTION_RELEASE_DOCUMENT_WAITING_FOR_CPE_LEADER_CONFIRMATION, null, IconTypeEnum.GENERAL,
            "放行单：${releaseNo} CPE（${categoryEngineerName}）已完成所有任务，并且上传完成所有文件，待CPE工程师组长（${cpeMasterName}）验证处理，请持续关注，谢谢！"),

    BATCH_PRODUCTION_RELEASE_DOCUMENT_CONFIRMED_BY_CPE_LEADER("CPE组长已确认批产放行文件", MessageClsEnum.BATCH_PRODUCTION_RELEASE_DOCUMENT_CONFIRMED_BY_CPE_LEADER, null, IconTypeEnum.GENERAL,
            "放行单：${releaseNo} CPE组长（${cpeMasterName}）已确认通过，感谢您的持续关注，谢谢！"),

    BATCH_PRODUCTION_RELEASE_DOCUMENT_VERIFICATION_FAILED_BY_CPE_LEADER("批产放行文件CPE组长验证未通过", MessageClsEnum.BATCH_PRODUCTION_RELEASE_DOCUMENT_VERIFICATION_FAILED_BY_CPE_LEADER, null, IconTypeEnum.GENERAL,
            "放行单：${releaseNo} CPE组长（${cpeMasterName}）未验证通过，请CPE（${categoryEngineerName}）及时处理，谢谢！"),

    CPE_MODIFIED_BATCH_PRODUCTION_RELEASE_TASK("CPE对批产放行任务进行了修改", MessageClsEnum.CPE_MODIFIED_BATCH_PRODUCTION_RELEASE_TASK, null, IconTypeEnum.GENERAL,
            "放行单：${releaseNo} CPE（${categoryEngineerName}）进行了任务修改操作，后续需要再次由CPE组长（${cpeMasterName}）进行验证处理，请您持续关注，谢谢！"),

    TODO_INQUIRY_MATERIAL_QUOTE_INVITATION("物料报价", null, TodoClsEnum.MATERIAL_QUOTATION, IconTypeEnum.GENERAL,
            "无锡威孚高科技集团股份有限公司邀请您参与询价单：${0}的轮次（${1}轮）的报价，请尽快处理！"),

    TODO_DESIGNATION_CONTRACT_SIGN("合同签署", null, TodoClsEnum.CONTRACT_SIGN, IconTypeEnum.GENERAL,
            "供应商：${0}，供应商编码：${1}需要签署${2}，请尽快去CMS系统处理！"),

    TODO_BIDDING_INVITATION("竞价", null, TodoClsEnum.BIDDING, IconTypeEnum.GENERAL,
            "无锡威孚高科集团邀请您参与{${0}}的竞价(竞价单号：{${1}})，竞价意愿反馈截止日期为：{${2}}，请尽快处理，谢谢！"),

    MAIL_INQUIRY_MATERIAL_QUOTE_INVITATION("【报价邀请】无锡威孚高科技集团股份有限公司物料报价邀请", null, null, null,
            "尊敬的${0}用户：<br/>您好！您收到了无锡威孚高科技集团股份有限公司的询价单${1}（${2}轮）的报价邀请，为避免您错过最佳报价时间，请尽快登录威孚SRM系统后在${3}前进行报价意愿反馈，并完成报价，感谢您的支持！<br/>"),

    MAIL_INQUIRY_VIEW_QUOTATION("【查看报价】询价完成通知", null, null, null,
            "尊敬的用户：<br/>您发送的询价单${0}已经可以查看报价了，快去询价单管理页面，点击对应询价单后面的“查看报价”去查看吧～<br/>"),

    MAIL_DESIGNATION_NOTIFICATION("【定点通知】无锡威孚高科技集团股份有限公司物料定点通知", null, null, null,
            "尊敬的${0}用户：<br/>您好！恭喜您收到了无锡威孚高科技集团股份有限公司的单号为${1}的定点通知书（询价单${2}），您可以登录威孚SRM系统进行定点通知书的查看，感谢您的支持！<br/>"),

    MAIL_BIDDING_COMPLETE("【重要通知】竞价单号${0}${1}竞价完成通知", null, null, null,
            "尊敬的用户：<br/>您发布的竞价活动：{${0}}，已经竞价完成，可在“竞价单管理”页面查看竞价结果！<br/>"),

    MAIL_BIDDING_FAILURE("【重要通知】竞价单号${0}${1}竞价失败通知", null, null, null,
            "尊敬的用户：<br/>您发布的竞价活动：${0}，不满足竞价参与供应商最低为2家的要求，竞价失败！失败原因为：资料更新<br/>"),

    MAIL_BIDDING_INVITATION("【竞价邀请】无锡威孚高科技集团股份有限公司的竞价单号${0}${1}竞价邀请通知", null, null, null,
            "尊敬的${0}用户：<br/>您好！无锡威孚高科集团邀请您参与竞价，竞价活动名称：${1}，竞价活动参与意愿反馈截止时间：${2}，竞价活动开始时间：${3}，期待您的参与！<br/>");


    private final String title;
    private final MessageClsEnum messageClsEnum;
    private final TodoClsEnum todoClsEnum;
    private final IconTypeEnum iconTypeEnum;
    private final String content;

    SourcingMsgTemplateEnum(String title, MessageClsEnum messageClsEnum, TodoClsEnum todoClsEnum, IconTypeEnum iconTypeEnum, String content) {
        this.messageClsEnum = messageClsEnum;
        this.todoClsEnum = todoClsEnum;
        this.iconTypeEnum = iconTypeEnum;
        this.title = title;
        this.content = content;
    }
}
