package com.weifu.srm.sourcing.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.sourcing.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.sourcing.repository.po.AttachmentRecordPO;
import com.weifu.srm.sourcing.request.AttachmentRecordReqDTO;
import com.weifu.srm.sourcing.request.OperateUserReqDTO;
import com.weifu.srm.sourcing.request.SupplierQuotationCopyReqDTO;
import com.weifu.srm.sourcing.response.AttachmentRecordRespDTO;
import com.weifu.srm.sourcing.response.AttachmentRecordSimpleRespDTO;
import com.weifu.srm.sourcing.service.AttachmentRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AttachmentRecordServiceImpl implements AttachmentRecordService {

    private final AttachmentRecordMapperService attachmentRecordMapperService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(String businessNo, String businessSubNo, String businessType, List<AttachmentRecordReqDTO> paramDTOs) {
        this.deleteBy(businessNo, businessSubNo, businessType);
        if (CollectionUtils.isNotEmpty(paramDTOs)) {
            for (AttachmentRecordReqDTO paramDTO : paramDTOs) {
                save(businessNo, businessSubNo, businessType, paramDTO);
            }
        }
    }

    private void save(String businessNo, String businessSubNo, String businessType, AttachmentRecordReqDTO paramDTO) {
        if (paramDTO != null && StringUtils.isNotBlank(paramDTO.getFileUrl())) {
            AttachmentRecordPO attachmentRecord = new AttachmentRecordPO();
            attachmentRecord.setBusinessNo(businessNo);
            attachmentRecord.setBusinessSubNo(businessSubNo);
            attachmentRecord.setBusinessType(businessType);
            attachmentRecord.setFileName(paramDTO.getFileName());
            attachmentRecord.setFileOriginalName(paramDTO.getFileOriginalName());
            attachmentRecord.setFileUrl(paramDTO.getFileUrl());
            BaseEntityUtil.setCommon(attachmentRecord, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
            attachmentRecord.setIsDelete(YesOrNoEnum.NO.getCode());
            attachmentRecordMapperService.save(attachmentRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBy(String businessNo, String businessSubNo, String businessType) {
        attachmentRecordMapperService.deleteBy(businessNo, businessSubNo, businessType);
    }

    @Override
    public List<AttachmentRecordPO> queryBy(String businessNo) {
        return attachmentRecordMapperService.queryBy(businessNo);
    }

    @Override
    public List<AttachmentRecordPO> queryAll(String businessNo) {
        return attachmentRecordMapperService.queryAll(businessNo);
    }

    @Override
    public List<AttachmentRecordPO> queryBy(String businessNo, String businessType) {
        return attachmentRecordMapperService.queryBy(businessNo, businessType);
    }

    @Override
    public List<AttachmentRecordPO> queryBy(List<String> businessNos, String businessType) {
        return attachmentRecordMapperService.queryBy(businessNos, businessType);
    }

    @Override
    public List<AttachmentRecordPO> queryBy(List<String> businessNos, List<String> businessTypes) {
        return attachmentRecordMapperService.queryBy(businessNos, businessTypes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(SupplierQuotationCopyReqDTO paramDTO) {
        List<AttachmentRecordPO> fullAttachments = queryBy(paramDTO.getBusinessNo(), paramDTO.getBusinessType());
        List<AttachmentRecordPO> attachments = fullAttachments
                .stream().filter(o -> Objects.equals(o.getBusinessSubNo(), paramDTO.getFromSubNo()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachments)) {
            for (AttachmentRecordPO attachment : attachments) {
                attachment.setId(null);
                attachment.setBusinessSubNo(paramDTO.getToSubNo());
                Date now = DateUtil.date();
                attachment.setCreateTime(now);
                attachment.setUpdateTime(now);
                attachmentRecordMapperService.save(attachment);
            }
        }
    }

    @Override
    public Map<String, List<AttachmentRecordRespDTO>> listByBusinessNoAndBusinessType(String businessNo, List<String> businessTypes) {
        if (StringUtils.isBlank(businessNo)) {
            return Maps.newHashMap();
        }
        List<AttachmentRecordPO> poList = attachmentRecordMapperService.queryBy(Lists.newArrayList(businessNo), businessTypes);
        if (CollectionUtils.isEmpty(poList)) {
            return Maps.newHashMap();
        }
        Map<String, List<AttachmentRecordPO>> poMap = poList.stream().collect(Collectors.groupingBy(AttachmentRecordPO::getBusinessType));
        return toResp(poMap);
    }

    @Override
    public Map<String, List<AttachmentRecordRespDTO>> listByBusinessNoAndBusinessType(List<String> businessNoList, String businessType) {
        if (CollectionUtils.isEmpty(businessNoList)) {
            return Maps.newHashMap();
        }
        List<AttachmentRecordPO> poList = attachmentRecordMapperService.queryBy(businessNoList, businessType);
        if (CollectionUtils.isEmpty(poList)) {
            return Maps.newHashMap();
        }
        Map<String, List<AttachmentRecordPO>> poMap = poList.stream().collect(Collectors.groupingBy(AttachmentRecordPO::getBusinessNo));
        return toResp(poMap);
    }

    @Override
    public List<AttachmentRecordRespDTO> list(List<String> businessNoList, List<String> businessTypes) {
        if (CollectionUtils.isEmpty(businessNoList)) {
            return Lists.newArrayList();
        }
        List<AttachmentRecordPO> poList = attachmentRecordMapperService.queryBy(businessNoList, businessTypes);
        if (CollectionUtils.isEmpty(poList)) {
            return Lists.newArrayList();
        }
        List<AttachmentRecordRespDTO> dtoList = Lists.newArrayList();
        for (AttachmentRecordPO po : poList) {
            AttachmentRecordRespDTO dto = BeanUtil.copyProperties(po, AttachmentRecordRespDTO.class);
            dto.setName(po.getFileOriginalName());
            dto.setBusinessType(po.getBusinessType());
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Override
    public Map<String, List<AttachmentRecordRespDTO>> listByBusinessNoAndBusinessType(List<String> businessNoList, List<String> businessTypeList) {
        if (CollectionUtils.isEmpty(businessNoList)) {
            return Maps.newHashMap();
        }
        List<AttachmentRecordPO> poList = attachmentRecordMapperService.queryBy(businessNoList, businessTypeList);
        if (CollectionUtils.isEmpty(poList)) {
            return Maps.newHashMap();
        }
        Map<String, List<AttachmentRecordPO>> poMap = poList.stream().collect(Collectors.groupingBy(AttachmentRecordPO::getBusinessNo));
        return toResp(poMap);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeBatch(List<String> businessNos, List<Long> filterList, List<String> businessTypes) {
        removeBatchByBusinessNos(businessNos, filterList, businessTypes);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdate(List<AttachmentRecordPO> poList) {
        modifyBatch(poList);
    }

    @Override
    public void saveOrUpdateAndRemove(List<AttachmentRecordPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        Set<String> businessNoSet = Sets.newHashSet();
        Set<String> businessTypeSet = Sets.newHashSet();
        List<Long> updateIds = Lists.newArrayList();
        for (AttachmentRecordPO po : poList) {
            businessNoSet.add(po.getBusinessNo());
            businessTypeSet.add(po.getBusinessType());
            if (po.getId() != null) {
                updateIds.add(po.getId());
            }
        }
        removeBatchByBusinessNos(new ArrayList<>(businessNoSet), updateIds, new ArrayList<>(businessTypeSet));
        modifyBatch(poList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateAndRemove(String businessNo, String businessType, List<AttachmentRecordReqDTO> annexList, OperateUserReqDTO reqDTO) {
        Date now = DateUtil.date();
        List<AttachmentRecordPO> oldRecords = queryBy(businessNo, businessType);
        List<Long> oldIdList = CollectionUtils.isEmpty(oldRecords) ? Lists.newArrayList() : oldRecords.stream().map(AttachmentRecordPO::getId).collect(Collectors.toList());
        List<Long> updateIdList = Lists.newArrayList();
        List<AttachmentRecordPO> poList = Lists.newArrayList();
        for (AttachmentRecordReqDTO dto : annexList) {
            AttachmentRecordPO po = BeanUtil.copyProperties(dto, AttachmentRecordPO.class);
            po.setBusinessNo(businessNo);
            po.setBusinessType(businessType);
            if (po.getId() == null) {
                BaseEntityUtil.setCommon(po, reqDTO.getOperateBy(), reqDTO.getOperateName(), now);
            } else {
                BaseEntityUtil.setCommonForU(po, reqDTO.getOperateBy(), reqDTO.getOperateName(), now);
                updateIdList.add(po.getId());
            }
            poList.add(po);
        }
        oldIdList.removeAll(updateIdList);
        if (CollectionUtils.isNotEmpty(oldIdList)) {
            attachmentRecordMapperService.removeByIds(oldIdList);
        }
        if (CollectionUtils.isNotEmpty(poList)) {
            attachmentRecordMapperService.saveOrUpdateBatch(poList);
        }
    }

    @Override
    public void addData(String businessNo, String businessType, List<AttachmentRecordReqDTO> recordReqList, OperateUserReqDTO reqDTO) {
        if(CollectionUtils.isEmpty(recordReqList)){
            return;
        }
        List<AttachmentRecordPO> poList =  Lists.newArrayList();
        Date now = DateUtil.date();
        for (AttachmentRecordReqDTO dto : recordReqList) {
            AttachmentRecordPO po = BeanUtil.copyProperties(dto, AttachmentRecordPO.class);
            po.setBusinessNo(businessNo);
            po.setBusinessType(businessType);
            BaseEntityUtil.setCommon(po, reqDTO.getOperateBy(), reqDTO.getOperateName(), now);
            poList.add(po);
        }
        attachmentRecordMapperService.saveBatch(poList);
    }

    private Map<String, List<AttachmentRecordRespDTO>> toResp(Map<String, List<AttachmentRecordPO>> poMap) {
        Map<String, List<AttachmentRecordRespDTO>> dtoMap = Maps.newHashMap();
        for (Map.Entry<String, List<AttachmentRecordPO>> entry : poMap.entrySet()) {
            String key = entry.getKey();
            List<AttachmentRecordPO> records = poMap.get(key);
            dtoMap.put(key, CollectionUtils.isEmpty(records) ? null : convertAttachmentRecordResp(records));
        }
        return dtoMap;
    }

    @Override
    public List<AttachmentRecordRespDTO> convertAttachmentRecordResp(List<AttachmentRecordPO> poList) {
        List<AttachmentRecordRespDTO> dtoList = Lists.newArrayList();
        for (AttachmentRecordPO po : poList) {
            AttachmentRecordRespDTO dto = BeanUtil.copyProperties(po, AttachmentRecordRespDTO.class);
            dto.setName(po.getFileOriginalName());
            dto.setBusinessType(po.getBusinessType());
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Override
    public void deleteById(Long id) {
        attachmentRecordMapperService.removeById(id);
    }

    @Override
    public AttachmentRecordPO getById(Long id) {
        return attachmentRecordMapperService.getById(id);
    }

    @Override
    public AttachmentRecordSimpleRespDTO queryByFileName(String fileName) {
        AttachmentRecordPO attachmentRecord = attachmentRecordMapperService.queryByFileName(fileName);
        return BeanUtil.toBean(attachmentRecord, AttachmentRecordSimpleRespDTO.class);
    }

    private void removeBatchByBusinessNos(List<String> businessNos, List<Long> filterList, List<String> businessTypes) {
        if (CollectionUtils.isEmpty(businessNos)) {
            return;
        }
        List<AttachmentRecordPO> poList = attachmentRecordMapperService.list(Wrappers.<AttachmentRecordPO>lambdaQuery().in(AttachmentRecordPO::getBusinessNo, businessNos).in(AttachmentRecordPO::getBusinessType, businessTypes));
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> idList = poList.stream().map(AttachmentRecordPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterList)) {
            idList.removeAll(filterList);
        }
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        attachmentRecordMapperService.removeByIds(idList);
    }

    private void modifyBatch(List<AttachmentRecordPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        attachmentRecordMapperService.saveOrUpdateBatch(poList);
    }
}
