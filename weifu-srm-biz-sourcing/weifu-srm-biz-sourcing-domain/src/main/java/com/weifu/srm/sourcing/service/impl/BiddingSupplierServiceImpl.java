package com.weifu.srm.sourcing.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.communication.enums.TodoClsEnum;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.sourcing.repository.atomicservice.BiddingMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.BiddingSupplierMapperService;
import com.weifu.srm.sourcing.repository.constants.MsgConstants;
import com.weifu.srm.sourcing.repository.enums.BiddingStatusEnum;
import com.weifu.srm.sourcing.repository.enums.BiddingUsePreviousPriceTypeEnum;
import com.weifu.srm.sourcing.repository.po.BiddingPO;
import com.weifu.srm.sourcing.repository.po.BiddingSupplierItemPO;
import com.weifu.srm.sourcing.repository.po.BiddingSupplierPO;
import com.weifu.srm.sourcing.request.bidding.BiddingSupplierReqDTO;
import com.weifu.srm.sourcing.request.bidding.SupplierBiddingIntentionReqDTO;
import com.weifu.srm.sourcing.request.bidding.SupplierBiddingSaveOpenWindowsReqDTO;
import com.weifu.srm.sourcing.request.bidding.SupplierSubmitPriceReqDTO;
import com.weifu.srm.sourcing.service.BiddingSupplierItemService;
import com.weifu.srm.sourcing.service.BiddingSupplierService;
import com.weifu.srm.sourcing.service.biz.SourcingTodoBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiddingSupplierServiceImpl implements BiddingSupplierService {

    private final BiddingMapperService biddingMapperService;
    private final BiddingSupplierMapperService biddingSupplierMapperService;
    private final BiddingSupplierItemService biddingSupplierItemService;
    private final SourcingTodoBiz sourcingTodoBiz;
    private final LocaleMessage localeMessage;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(BiddingPO bidding, List<BiddingSupplierReqDTO> paramDTOs) {

        Map<String, BiddingSupplierReqDTO> reqBiddingSupplierMap = paramDTOs.stream()
                .collect(Collectors.toMap(BiddingSupplierReqDTO::getSapSupplierCode, o -> o));

        List<BiddingSupplierPO> dbBiddingSuppliers = biddingSupplierMapperService.queryAll(bidding.getBiddingNo());
        List<String> dbSapSupplierCodes = dbBiddingSuppliers.stream()
                .map(BiddingSupplierPO::getSapSupplierCode)
                .collect(Collectors.toList());

        List<BiddingSupplierReqDTO> addBiddingSuppliers = paramDTOs.stream()
                .filter(o -> !dbSapSupplierCodes.contains(o.getSapSupplierCode()))
                .collect(Collectors.toList());

        List<BiddingSupplierPO> updateBiddingSuppliers = dbBiddingSuppliers.stream()
                .filter(o -> dbSapSupplierCodes.contains(o.getSapSupplierCode()))
                .collect(Collectors.toList());

        List<BiddingSupplierPO> deleteBiddingSuppliers = dbBiddingSuppliers.stream()
                .filter(o -> !dbSapSupplierCodes.contains(o.getSapSupplierCode()))
                .collect(Collectors.toList());

        this.insert(bidding, addBiddingSuppliers);
        this.update(bidding, updateBiddingSuppliers, reqBiddingSupplierMap);
        this.delete(bidding, deleteBiddingSuppliers);
    }

    private void insert(BiddingPO bidding, List<BiddingSupplierReqDTO> biddingSuppliers) {
        String biddingNo = bidding.getBiddingNo();
        if (CollectionUtils.isNotEmpty(biddingSuppliers)) {
            for (BiddingSupplierReqDTO paramDTO : biddingSuppliers) {
                BiddingSupplierPO biddingSupplier = BeanUtil.toBean(paramDTO, BiddingSupplierPO.class);
                biddingSupplier.setBiddingNo(biddingNo);
                biddingSupplier.setIsOpenedWindows(YesOrNoEnum.NO.getCode());
                BaseEntityUtil.setCommon(biddingSupplier, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
                biddingSupplier.setIsDelete(YesOrNoEnum.NO.getCode());
                biddingSupplierMapperService.save(biddingSupplier);
            }
        }
    }

    private void update(BiddingPO bidding, List<BiddingSupplierPO> biddingSuppliers, Map<String, BiddingSupplierReqDTO> reqBiddingSupplierMap) {
        if (CollectionUtils.isNotEmpty(biddingSuppliers)) {
            for (BiddingSupplierPO biddingSupplier : biddingSuppliers) {
                if (Objects.equals(YesOrNoEnum.YES.getCode(), biddingSupplier.getIsDelete())) {
                    biddingSupplierMapperService.restoreById(biddingSupplier.getId());
                }
                BiddingSupplierReqDTO paramDTO = reqBiddingSupplierMap.get(biddingSupplier.getSapSupplierCode());
                BeanUtil.copyProperties(paramDTO, biddingSupplier, CopyOptions.create().ignoreNullValue());
                BaseEntityUtil.setCommonForU(biddingSupplier, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
                biddingSupplierMapperService.updateById(biddingSupplier);
            }
        }
    }

    private void delete(BiddingPO bidding, List<BiddingSupplierPO> biddingSuppliers) {
        if (CollectionUtils.isNotEmpty(biddingSuppliers)) {
            List<Long> ids = biddingSuppliers.stream().map(BiddingSupplierPO::getId)
                    .collect(Collectors.toList());
            biddingSupplierMapperService.removeByIds(ids);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBiddingIntention(SupplierBiddingIntentionReqDTO paramDTO) {
        BiddingPO bidding = biddingMapperService.queryBy(paramDTO.getBiddingNo());
        this.validateUpdateBiddingIntentionRule(bidding);
        List<BiddingSupplierPO> biddingSuppliers = biddingSupplierMapperService.queryBy(paramDTO.getBiddingNo());
        BiddingSupplierPO biddingSupplier = biddingSuppliers.stream()
                .filter(o -> Objects.equals(o.getSapSupplierCode(), paramDTO.getSapSupplierCode()))
                .findFirst().orElse(null);
        if (biddingSupplier != null) {
            biddingSupplier.setBiddingIntention(paramDTO.getBiddingIntention());
            BaseEntityUtil.setCommonForU(biddingSupplier, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
            biddingSupplierMapperService.updateById(biddingSupplier);
            sourcingTodoBiz.closeTodo(TodoClsEnum.BIDDING, bidding.getBiddingNo(), biddingSupplier.getSapSupplierCode());
        }
    }

    private void validateUpdateBiddingIntentionRule(BiddingPO bidding) {
        Date feedbackDeadline = bidding.getFeedbackDeadline();
        int compare = DateUtil.compare(DateUtil.date(), feedbackDeadline, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
        if (!Objects.equals(BiddingStatusEnum.SUBMITTED.getCode(), bidding.getStatus()) || compare >= 0) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.BIDDING_FEEDBACK_TIMEOUT_ERROR_MSG));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOpenWindows(SupplierBiddingSaveOpenWindowsReqDTO paramDTO) {
        List<BiddingSupplierPO> biddingSuppliers = biddingSupplierMapperService.queryBy(paramDTO.getBiddingNo());
        BiddingSupplierPO biddingSupplier = biddingSuppliers.stream()
                .filter(o -> Objects.equals(o.getSapSupplierCode(), paramDTO.getSapSupplierCode()))
                .findFirst().orElse(null);
        if (biddingSupplier != null) {
            biddingSupplier.setIsOpenedWindows(YesOrNoEnum.YES.getCode());
            BaseEntityUtil.setCommonForU(biddingSupplier, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
            biddingSupplierMapperService.updateById(biddingSupplier);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrice(SupplierSubmitPriceReqDTO paramDTO) {
        List<BiddingSupplierPO> biddingSuppliers = queryBy(List.of(paramDTO.getBiddingNo()));
        BiddingSupplierPO biddingSupplier = biddingSuppliers.stream()
                .filter(o -> Objects.equals(paramDTO.getSapSupplierCode(), o.getSapSupplierCode()))
                .findFirst().orElse(null);
        if (biddingSupplier != null) {
            biddingSupplier.setIsPriced(YesOrNoEnum.YES.getCode());
            BaseEntityUtil.setCommonForU(biddingSupplier, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
            biddingSupplierMapperService.updateById(biddingSupplier);
            // 保存供应商报价...
            biddingSupplierItemService.updatePrice(paramDTO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkUsePreviousPrice(BiddingPO bidding) {
        List<BiddingSupplierPO> biddingSuppliers = queryBy(List.of(bidding.getBiddingNo()));
        for (BiddingSupplierPO biddingSupplier : biddingSuppliers) {
            if (Objects.equals(YesOrNoEnum.YES.getCode(), biddingSupplier.getBiddingIntention())) {
                List<BiddingSupplierItemPO> items = biddingSupplierItemService
                        .queryBy(bidding.getBiddingNo(), bidding.getRound());
                BiddingSupplierItemPO item = items.stream()
                        .filter(o -> Objects.equals(o.getSapSupplierCode(), biddingSupplier.getSapSupplierCode()))
                        .findFirst().orElse(null);
                if (item != null && item.getPrice() == null) {
                    SupplierSubmitPriceReqDTO paramDTO = new SupplierSubmitPriceReqDTO();
                    paramDTO.setBiddingNo(bidding.getBiddingNo());
                    paramDTO.setSapSupplierCode(biddingSupplier.getSapSupplierCode());
                    paramDTO.setRound(bidding.getRound());
                    paramDTO.setUsePreviousPriceType(BiddingUsePreviousPriceTypeEnum.AUTO.getCode());
                    this.usePreviousPrice(paramDTO);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void usePreviousPrice(SupplierSubmitPriceReqDTO paramDTO) {
        biddingSupplierItemService.usePreviousPrice(paramDTO);
    }

    @Override
    public List<BiddingSupplierPO> queryBy(List<String> biddingNos) {
        return biddingSupplierMapperService.queryBy(biddingNos);
    }

    @Override
    public int statisticsPendingBy(String sapSupplierCode) {
        int x = biddingSupplierMapperService.queryCountBySupplier(sapSupplierCode,
                BiddingStatusEnum.SUBMITTED.getCode(), YesOrNoEnum.YES.getCode(), YesOrNoEnum.YES.getCode());
        int y = biddingSupplierMapperService.queryCountBySupplier(sapSupplierCode,
                BiddingStatusEnum.BIDDING.getCode(), YesOrNoEnum.YES.getCode(), YesOrNoEnum.NO.getCode());
        return x + y;
    }
}
