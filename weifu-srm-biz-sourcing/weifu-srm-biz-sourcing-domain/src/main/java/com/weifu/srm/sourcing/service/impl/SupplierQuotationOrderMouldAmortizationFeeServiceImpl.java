package com.weifu.srm.sourcing.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderMouldAmortizationFeeMapperService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderMouldAmortizationFeePO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.request.SupplierQuotationCopyReqDTO;
import com.weifu.srm.sourcing.request.SupplierQuotationOrderMouldAmortizationFeeReqDTO;
import com.weifu.srm.sourcing.request.SupplierQuotationOrderSaveReqDTO;
import com.weifu.srm.sourcing.service.SupplierQuotationOrderMouldAmortizationFeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierQuotationOrderMouldAmortizationFeeServiceImpl implements SupplierQuotationOrderMouldAmortizationFeeService {

    private final SupplierQuotationOrderMouldAmortizationFeeMapperService supplierQuotationOrderMouldAmortizationFeeMapperService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SupplierQuotationOrderPO sqo, SupplierQuotationOrderSaveReqDTO paramDTO) {
        supplierQuotationOrderMouldAmortizationFeeMapperService.deleteBy(sqo.getId(), paramDTO.getId());
        List<SupplierQuotationOrderMouldAmortizationFeeReqDTO> mouldAmortizationFeeReqDTOs = paramDTO.getMouldAmortizationFees();
        if (CollectionUtils.isNotEmpty(mouldAmortizationFeeReqDTOs)) {
            List<SupplierQuotationOrderMouldAmortizationFeePO> mouldAmortizationFees = BeanUtil
                    .copyToList(mouldAmortizationFeeReqDTOs, SupplierQuotationOrderMouldAmortizationFeePO.class);
            for (SupplierQuotationOrderMouldAmortizationFeePO mouldAmortizationFee : mouldAmortizationFees) {
                mouldAmortizationFee.setInquiryNo(sqo.getInquiryNo());
                mouldAmortizationFee.setQuotationOrderId(sqo.getId());
                mouldAmortizationFee.setQuotationOrderMaterialId(paramDTO.getId());
                mouldAmortizationFee.setRequirementPartsNo(paramDTO.getRequirementPartsNo());
                mouldAmortizationFee.setMaterialCode(paramDTO.getMaterialCode());
                BaseEntityUtil.setCommon(mouldAmortizationFee, sqo.getUpdateBy(), sqo.getUpdateName(), DateUtil.date());
                mouldAmortizationFee.setIsDelete(YesOrNoEnum.NO.getCode());
                supplierQuotationOrderMouldAmortizationFeeMapperService.save(mouldAmortizationFee);
            }
        }
    }

    @Override
    public List<SupplierQuotationOrderMouldAmortizationFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return supplierQuotationOrderMouldAmortizationFeeMapperService.queryBy(quotationOrderId, quotationOrderMaterialId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(SupplierQuotationCopyReqDTO paramDTO) {
        List<SupplierQuotationOrderMouldAmortizationFeePO> mouldAmortizationFees =
                queryBy(paramDTO.getFromQuotationOrderId(), paramDTO.getFromQuotationOrderMaterialId());
        if (CollectionUtils.isNotEmpty(mouldAmortizationFees)) {
            for (SupplierQuotationOrderMouldAmortizationFeePO mouldAmortizationFee : mouldAmortizationFees) {
                mouldAmortizationFee.setId(null);
                mouldAmortizationFee.setQuotationOrderId(paramDTO.getToQuotationOrderId());
                mouldAmortizationFee.setQuotationOrderMaterialId(paramDTO.getToQuotationOrderMaterialId());
                Date now = DateUtil.date();
                mouldAmortizationFee.setCreateTime(now);
                mouldAmortizationFee.setUpdateTime(now);
                supplierQuotationOrderMouldAmortizationFeeMapperService.save(mouldAmortizationFee);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        supplierQuotationOrderMouldAmortizationFeeMapperService.deleteBy(quotationOrderId, quotationOrderMaterialId);
    }
}
