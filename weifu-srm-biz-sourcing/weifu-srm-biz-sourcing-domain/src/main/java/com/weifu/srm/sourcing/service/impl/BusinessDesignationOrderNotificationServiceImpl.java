package com.weifu.srm.sourcing.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.sourcing.enums.DesignationOrderNotificationStatusEnum;
import com.weifu.srm.sourcing.enums.SupplierClassificationEnum;
import com.weifu.srm.sourcing.manager.DataPermissionManager;
import com.weifu.srm.sourcing.repository.atomicservice.BusinessDesignationOrderNotificationMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderSupplierMapperService;
import com.weifu.srm.sourcing.repository.enums.AttachmentRecordTypeEnum;
import com.weifu.srm.sourcing.repository.enums.BizNoRuleEnum;
import com.weifu.srm.sourcing.repository.enums.DataPermissionTypeEnum;
import com.weifu.srm.sourcing.repository.po.AttachmentRecordPO;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderNotificationItemPO;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderNotificationPO;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderPO;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderSupplierPO;
import com.weifu.srm.sourcing.repository.po.InquiryOrderPO;
import com.weifu.srm.sourcing.repository.po.InquiryOrderSupplierPO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderDiscardApplyReqDTO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderNotificationQueryReqDTO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderSendReqDTO;
import com.weifu.srm.sourcing.response.AttachmentRecordRespDTO;
import com.weifu.srm.sourcing.response.BusinessDesignationOrderNotificationItemRespDTO;
import com.weifu.srm.sourcing.response.BusinessDesignationOrderNotificationRespDTO;
import com.weifu.srm.sourcing.service.AttachmentRecordService;
import com.weifu.srm.sourcing.service.BusinessDesignationOrderNotificationItemService;
import com.weifu.srm.sourcing.service.BusinessDesignationOrderNotificationService;
import com.weifu.srm.sourcing.service.BusinessDesignationOrderSupplierService;
import com.weifu.srm.sourcing.util.BizNoUtil;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessDesignationOrderNotificationServiceImpl implements BusinessDesignationOrderNotificationService {

    private final AttachmentRecordService attachmentRecordService;
    private final InquiryOrderMapperService inquiryOrderMapperService;
    private final InquiryOrderSupplierMapperService inquiryOrderSupplierMapperService;
    private final BusinessDesignationOrderNotificationMapperService businessDesignationOrderNotificationMapperService;
    private final BusinessDesignationOrderNotificationItemService businessDesignationOrderNotificationItemService;
    private final BusinessDesignationOrderSupplierService businessDesignationOrderSupplierService;
    private final DataPermissionManager dataPermissionManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generate(BusinessDesignationOrderPO designationOrder) {
        // 删除已有通知书...
        businessDesignationOrderNotificationMapperService.deleteBy(designationOrder.getDesignationOrderNo());

        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(designationOrder.getInquiryNo());

        List<BusinessDesignationOrderSupplierPO> designationSuppliers =
                businessDesignationOrderSupplierService.queryBy(designationOrder.getDesignationOrderNo());
        List<String> sapSupplierCodes = designationSuppliers.stream()
                .map(BusinessDesignationOrderSupplierPO::getSapSupplierCode)
                .distinct()
                .collect(Collectors.toList());

        List<InquiryOrderSupplierPO> inquirySuppliers = inquiryOrderSupplierMapperService.queryBy(designationOrder.getInquiryNo(), YesOrNoEnum.YES.getCode());
        Map<String, InquiryOrderSupplierPO> inquirySupplierMap = inquirySuppliers.stream()
                .collect(Collectors.toMap(InquiryOrderSupplierPO::getSapSupplierCode, o -> o));

        for (int i = 0; i < sapSupplierCodes.size(); i++) {
            String sapSupplierCode = sapSupplierCodes.get(i);
            BusinessDesignationOrderNotificationPO notification = new BusinessDesignationOrderNotificationPO();
            notification.setDesignationOrderNo(designationOrder.getDesignationOrderNo());
            notification.setDesignationOrderNotificationNo(BizNoUtil.generateNo(BizNoRuleEnum.DESIGNATION_ORDER_NOTIFICATION,
                    designationOrder.getDesignationOrderNo(), String.format(BizNoRuleEnum.DESIGNATION_ORDER_NOTIFICATION.getLength(), (i + 1))));
            notification.setSapSupplierCode(sapSupplierCode);
            InquiryOrderSupplierPO inquirySupplier = inquirySupplierMap.get(sapSupplierCode);
            if (inquirySupplier != null) {
                notification.setSupplierName(inquirySupplier.getSupplierName());
                notification.setSupplierNameEn(inquirySupplier.getSupplierNameEn());
                boolean isStrategySupplier = Objects.equals(SupplierClassificationEnum.STRATEGY_SUPPLIER.getCode(), inquirySupplier.getSupplierClassification());
                notification.setIsStrategySupplier(isStrategySupplier ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                notification.setIsRecommendedSupplier(inquirySupplier.getIsRecommendedSupplier());
                notification.setRecommendedAttribute(inquirySupplier.getRecommendedAttribute());
            }
            notification.setCategoryCode(designationOrder.getCategoryCode());
            notification.setInquiryNo(inquiryOrder.getInquiryNo());
            notification.setInquiryName(inquiryOrder.getInquiryName());
            BaseEntityUtil.setCommon(notification, designationOrder.getUpdateBy(), designationOrder.getUpdateName(), DateUtil.date());
            notification.setStatus(DesignationOrderNotificationStatusEnum.TO_BE_SEND.getCode());
            notification.setIsDelete(YesOrNoEnum.NO.getCode());
            businessDesignationOrderNotificationMapperService.save(notification);
            // 生成通知书明细...
            businessDesignationOrderNotificationItemService.generate(designationOrder, notification);
        }
    }

    @Override
    public PageResponse<BusinessDesignationOrderNotificationRespDTO> queryPage(BusinessDesignationOrderNotificationQueryReqDTO paramDTO) {
        List<DataPermissionRespDTO.DataPermissionKeyRespDTO> keys = List.of();
        if (Objects.equals(YesOrNoEnum.YES.getCode(), paramDTO.getIsAdminView())) {
            DataPermissionRespDTO dataPermissionDTO = dataPermissionManager
                    .queryUserDataPermission(paramDTO.getUserId(), DataPermissionTypeEnum.PC_INTERNAL_PAGE_SOURCING_BID_NOTICE_MANAGE.getCode());
            log.info("Business designation order notification data permission info：{}", JacksonUtil.bean2Json(dataPermissionDTO));
            if (dataPermissionDTO.isNo()) {
                log.info("分页查询商务定点通知书无数据权限，userId：{}", paramDTO.getUserId());
                return PageResponse.toResult(paramDTO.getPageNum(), paramDTO.getPageSize(), 0L, List.of());
            }
            keys = dataPermissionDTO.getKeys();
        }
        Page<BusinessDesignationOrderNotificationPO> page = new Page<>(paramDTO.getPageNum(), paramDTO.getPageSize());
        IPage<BusinessDesignationOrderNotificationPO> pr = businessDesignationOrderNotificationMapperService.queryPage(page, paramDTO, keys);
        List<BusinessDesignationOrderNotificationRespDTO> respDTOs = BeanUtil.copyToList(pr.getRecords(), BusinessDesignationOrderNotificationRespDTO.class);
        if (CollectionUtils.isNotEmpty(respDTOs)) {
            List<String> designationOrderNos = respDTOs.stream()
                    .map(BusinessDesignationOrderNotificationRespDTO::getDesignationOrderNo)
                    .collect(Collectors.toList());
            List<AttachmentRecordPO> fullAttachments = attachmentRecordService.queryBy(designationOrderNos,
                    AttachmentRecordTypeEnum.BUSINESS_DESIGNATION_ORDER_NOTIFICATION_FILE.getCode());

            List<String> inquiryNos = respDTOs.stream()
                    .map(BusinessDesignationOrderNotificationRespDTO::getInquiryNo)
                    .distinct()
                    .collect(Collectors.toList());
            List<InquiryOrderPO> inquiryOrders = inquiryOrderMapperService.queryBy(inquiryNos);
            Map<String, InquiryOrderPO> inquiryOrderMap = inquiryOrders.stream()
                    .collect(Collectors.toMap(InquiryOrderPO::getInquiryNo, o -> o));

            for (BusinessDesignationOrderNotificationRespDTO respDTO : respDTOs) {
                InquiryOrderPO inquiryOrder = inquiryOrderMap.get(respDTO.getInquiryNo());
                if (inquiryOrder != null) {
                    respDTO.setInquiryEngineerUserId(inquiryOrder.getInquiryEngineerUserId());
                    respDTO.setInquiryEngineerUserName(inquiryOrder.getInquiryEngineerUserName());
                    respDTO.setInquiryEngineerEmail(inquiryOrder.getInquiryEngineerEmail());
                }
                List<AttachmentRecordPO> attachments = fullAttachments.stream()
                        .filter(o -> Objects.equals(respDTO.getDesignationOrderNo(), o.getBusinessNo()))
                        .filter(o -> Objects.equals(respDTO.getSapSupplierCode(), o.getBusinessSubNo()))
                        .collect(Collectors.toList());
                respDTO.setAttachments(BeanUtil.copyToList(attachments, AttachmentRecordRespDTO.class));
            }

        }
        return PageResponse.toResult(paramDTO.getPageNum(), paramDTO.getPageSize(), pr.getTotal(), respDTOs);
    }

    @Override
    public BusinessDesignationOrderNotificationRespDTO queryNotificationDetail(String designationOrderNotificationNo) {
        BusinessDesignationOrderNotificationPO record = businessDesignationOrderNotificationMapperService.queryBy(designationOrderNotificationNo);
        BusinessDesignationOrderNotificationRespDTO respDTO = BeanUtil.copyProperties(record, BusinessDesignationOrderNotificationRespDTO.class);

        List<AttachmentRecordPO> fullAttachments = attachmentRecordService
                .queryBy(record.getInquiryNo(), AttachmentRecordTypeEnum.MATERIAL_DRAWING_FILE.getCode());

        List<BusinessDesignationOrderNotificationItemRespDTO> itemDTOs = new ArrayList<>();
        List<BusinessDesignationOrderNotificationItemPO> items = businessDesignationOrderNotificationItemService.queryBy(record.getDesignationOrderNotificationNo());
        for (BusinessDesignationOrderNotificationItemPO item : items) {
            BusinessDesignationOrderNotificationItemRespDTO itemDTO = BeanUtil.copyProperties(item, BusinessDesignationOrderNotificationItemRespDTO.class);

            AttachmentRecordPO attachment = fullAttachments.stream()
                    .filter(o -> Objects.equals(item.getRequirementPartsNo(), o.getBusinessSubNo()))
                    .findFirst().orElse(null);
            itemDTO.setDrawingAttachment(BeanUtil.copyProperties(attachment, AttachmentRecordRespDTO.class));
            itemDTOs.add(itemDTO);
        }
        respDTO.setItems(itemDTOs);
        return respDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void discard(BusinessDesignationOrderDiscardApplyReqDTO paramDTO) {
        businessDesignationOrderNotificationMapperService.lambdaUpdate()
                .set(BusinessDesignationOrderNotificationPO::getStatus, DesignationOrderNotificationStatusEnum.DISCARD.getCode())
                .set(BusinessDesignationOrderNotificationPO::getUpdateBy, paramDTO.getOperateBy())
                .set(BusinessDesignationOrderNotificationPO::getUpdateName, paramDTO.getOperateName())
                .set(BusinessDesignationOrderNotificationPO::getUpdateTime, DateUtil.date())
                .eq(BusinessDesignationOrderNotificationPO::getDesignationOrderNo, paramDTO.getDesignationOrderNo())
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void send(BusinessDesignationOrderPO designationOrder, BusinessDesignationOrderSendReqDTO paramDTO) {
        businessDesignationOrderNotificationMapperService.lambdaUpdate()
                .set(BusinessDesignationOrderNotificationPO::getStatus, DesignationOrderNotificationStatusEnum.SENT.getCode())
                .set(BusinessDesignationOrderNotificationPO::getSentBy, paramDTO.getOperateBy())
                .set(BusinessDesignationOrderNotificationPO::getSentTime, designationOrder.getSentTime())
                .set(BusinessDesignationOrderNotificationPO::getUpdateBy, paramDTO.getOperateBy())
                .set(BusinessDesignationOrderNotificationPO::getUpdateName, paramDTO.getOperateName())
                .set(BusinessDesignationOrderNotificationPO::getUpdateTime, DateUtil.date())
                .eq(BusinessDesignationOrderNotificationPO::getDesignationOrderNo, paramDTO.getDesignationOrderNo())
                .update();
    }

    @Override
    public List<BusinessDesignationOrderNotificationPO> queryBy(String designationOrderNo) {
        return businessDesignationOrderNotificationMapperService.queryByDesignationOrderNo(designationOrderNo);
    }
}
