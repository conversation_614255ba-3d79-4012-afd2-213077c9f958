package com.weifu.srm.sourcing.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderQuotationLadderMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderLadderFeeMapperService;
import com.weifu.srm.sourcing.repository.po.InquiryOrderQuotationLadderPO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderLadderFeePO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.request.SupplierQuotationCopyReqDTO;
import com.weifu.srm.sourcing.request.SupplierQuotationOrderLadderFeeReqDTO;
import com.weifu.srm.sourcing.request.SupplierQuotationOrderSaveReqDTO;
import com.weifu.srm.sourcing.service.SupplierQuotationOrderLadderFeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierQuotationOrderLadderFeeServiceImpl implements SupplierQuotationOrderLadderFeeService {

    private final InquiryOrderQuotationLadderMapperService inquiryOrderQuotationLadderMapperService;
    private final SupplierQuotationOrderLadderFeeMapperService supplierQuotationOrderLadderFeeMapperService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SupplierQuotationOrderPO sqo, SupplierQuotationOrderSaveReqDTO paramDTO) {
        supplierQuotationOrderLadderFeeMapperService.deleteBy(sqo.getId(), paramDTO.getId());
        List<SupplierQuotationOrderLadderFeeReqDTO> ladderFeeReqDTOs = paramDTO.getLadderFees();
        if (CollectionUtils.isNotEmpty(ladderFeeReqDTOs)) {
            List<InquiryOrderQuotationLadderPO> quotationLadders = inquiryOrderQuotationLadderMapperService
                    .queryBy(sqo.getInquiryNo(), paramDTO.getRequirementPartsNo());
            Map<Long, InquiryOrderQuotationLadderPO> quotationLadderMap = quotationLadders.stream()
                    .collect(Collectors.toMap(InquiryOrderQuotationLadderPO::getId, o -> o));

            List<SupplierQuotationOrderLadderFeePO> ladderFees = BeanUtil
                    .copyToList(ladderFeeReqDTOs, SupplierQuotationOrderLadderFeePO.class);

            for (SupplierQuotationOrderLadderFeePO ladderFee : ladderFees) {
                ladderFee.setInquiryNo(sqo.getInquiryNo());
                ladderFee.setQuotationOrderId(sqo.getId());
                ladderFee.setQuotationOrderMaterialId(paramDTO.getId());
                ladderFee.setRequirementPartsNo(paramDTO.getRequirementPartsNo());
                ladderFee.setMaterialCode(paramDTO.getMaterialCode());
                InquiryOrderQuotationLadderPO quotationLadder = quotationLadderMap.get(ladderFee.getQuotationLadderId());
                if (quotationLadder != null) {
                    ladderFee.setQuotationLadderId(quotationLadder.getId());
                    ladderFee.setEnableSupplierEdit(quotationLadder.getEnableSupplierEdit());
                    ladderFee.setLadderPriceType(quotationLadder.getLadderPriceType());
                    if (Objects.equals(YesOrNoEnum.NO.getCode(), quotationLadder.getEnableSupplierEdit())) {
                        ladderFee.setMinQty(quotationLadder.getMinQty());
                        ladderFee.setMaxQty(quotationLadder.getMaxQty());
                    }
                }
                BaseEntityUtil.setCommon(ladderFee, sqo.getUpdateBy(), sqo.getUpdateName(), DateUtil.date());
                ladderFee.setIsDelete(YesOrNoEnum.NO.getCode());
                supplierQuotationOrderLadderFeeMapperService.save(ladderFee);
            }
        }
    }

    @Override
    public List<SupplierQuotationOrderLadderFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return supplierQuotationOrderLadderFeeMapperService.queryBy(quotationOrderId, quotationOrderMaterialId);
    }

    @Override
    public List<SupplierQuotationOrderLadderFeePO> queryBy(List<Long> quotationOrderMaterialIds) {
        if (CollectionUtils.isNotEmpty(quotationOrderMaterialIds)) {
            return supplierQuotationOrderLadderFeeMapperService.queryBy(quotationOrderMaterialIds);
        }
        return List.of();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(SupplierQuotationCopyReqDTO paramDTO) {
        List<SupplierQuotationOrderLadderFeePO> ladderFees =
                queryBy(paramDTO.getFromQuotationOrderId(), paramDTO.getFromQuotationOrderMaterialId());
        if (CollectionUtils.isNotEmpty(ladderFees)) {
            for (SupplierQuotationOrderLadderFeePO ladderFee : ladderFees) {
                ladderFee.setId(null);
                ladderFee.setQuotationOrderId(paramDTO.getToQuotationOrderId());
                ladderFee.setQuotationOrderMaterialId(paramDTO.getToQuotationOrderMaterialId());
                Date now = DateUtil.date();
                ladderFee.setCreateTime(now);
                ladderFee.setUpdateTime(now);
                supplierQuotationOrderLadderFeeMapperService.save(ladderFee);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        supplierQuotationOrderLadderFeeMapperService.deleteBy(quotationOrderId, quotationOrderMaterialId);
    }
}
