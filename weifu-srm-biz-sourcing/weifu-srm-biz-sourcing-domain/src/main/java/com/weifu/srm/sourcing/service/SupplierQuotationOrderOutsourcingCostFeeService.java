package com.weifu.srm.sourcing.service;

import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderOutsourcingCostFeePO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.request.SupplierQuotationCopyReqDTO;
import com.weifu.srm.sourcing.request.SupplierQuotationOrderSaveReqDTO;

import java.util.List;

public interface SupplierQuotationOrderOutsourcingCostFeeService {

    void save(SupplierQuotationOrderPO sqo, SupplierQuotationOrderSaveReqDTO paramDTO);

    List<SupplierQuotationOrderOutsourcingCostFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    void copy(SupplierQuotationCopyReqDTO paramDTO);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);
}
