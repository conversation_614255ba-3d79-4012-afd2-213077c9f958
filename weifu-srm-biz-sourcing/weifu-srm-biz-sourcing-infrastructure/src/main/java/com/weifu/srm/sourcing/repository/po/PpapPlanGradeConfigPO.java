package com.weifu.srm.sourcing.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description ppap计划等级设置
 * @Date 2024-09-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ppap_plan_grade_config")
public class PpapPlanGradeConfigPO extends BaseEntity {


    /**
     * 文件清单类型
     */
    private String fileType;

    /**
     * 责任人类型：
     */
    private String liabilityType;

    /**
     * 是否锁定
     */
    private Integer isLock;

    /**
     * 必传项标识
     */
    private String required;


}
