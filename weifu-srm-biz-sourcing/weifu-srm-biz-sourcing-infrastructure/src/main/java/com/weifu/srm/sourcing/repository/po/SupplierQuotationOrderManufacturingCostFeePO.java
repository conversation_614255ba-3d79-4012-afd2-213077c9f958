package com.weifu.srm.sourcing.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName(value = "supplier_quotation_order_manufacturing_cost_fee")
public class SupplierQuotationOrderManufacturingCostFeePO extends BaseEntity {
    /**
     * 询价单号
     */
    private String inquiryNo;
    private Long quotationOrderId;
    private Long quotationOrderMaterialId;
    /**
     * 需求编码
     */
    private String requirementPartsNo;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 工序
     */
    private String processLine;
    /**
     * 直接使用设备名称
     */
    private String directUseEquipmentName;
    /**
     * 设备原值（元）
     */
    private BigDecimal equipmentOriValue;
    /**
     * 折旧年限（年）
     */
    private Integer depreciationLife;
    /**
     * 设备残值率（%）
     */
    private BigDecimal equipmentSalvageRate;
    /**
     * 年工作日（天）
     */
    private Integer annualWorkingDays;
    /**
     * 生产班次
     */
    private Integer productionSift;
    /**
     * 单班工作时间（小时）
     */
    private Integer singleShiftWorkingHours;
    /**
     * 设备开动率（%）
     */
    private BigDecimal equipmentActuationRate;
    /**
     * 一模几件（件、节拍）
     */
    private Integer piecesPreMould;
    /**
     * 实际节拍
     */
    private Integer actualBeat;
    /**
     * 单价设备折旧（元/件）
     */
    private BigDecimal equipmentDepreciationPrice;
    /**
     * 电费（元/度）
     */
    private BigDecimal electricPrice;
    /**
     * 设备额定功率（KW）
     */
    private Integer equipmentPower;
    /**
     * 设备功率系数（%）
     */
    private BigDecimal equipmentPowerCoe;
    /**
     * 单价设备电耗（元/件）
     */
    private BigDecimal equipmentElectricPrice;
    /**
     * 单件设备成本（元/件）
     */
    private BigDecimal equipmentCostPrice;
    /**
     * 单班直接人数
     */
    private Integer singleShiftDirectPeopleNumber;
    /**
     * 人工小时费率（元/小时）
     */
    private BigDecimal laborHourPrice;
    /**
     * 单件人工成本（元/件）
     */
    private BigDecimal equipmentLaborCostPrice;
    /**
     * 单件天然气用量（立方）
     */
    private BigDecimal equipmentNaturalGasUsage;
    /**
     * 天然气单价（元/立方）
     */
    private BigDecimal naturalGasPrice;
    /**
     * 单件天然气成本（元/件）
     */
    private BigDecimal equipmentNaturalGasCostPrice;
    /**
     * 单件工具成本（元/件）
     */
    private BigDecimal equipmentToolCostPrice;
    /**
     * 易耗分摊（元/件）
     */
    private BigDecimal consumableApportionPrice;
    /**
     * 工艺废品率（%）
     */
    private BigDecimal scrapRate;
    /**
     * 废品损失（元/件）
     */
    private BigDecimal scrapLossPrice;
    /**
     * 单项工序制造费用
     */
    private BigDecimal amountFee;
}
