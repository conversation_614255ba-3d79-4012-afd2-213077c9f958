package com.weifu.srm.sourcing.repository.enums;

import com.weifu.srm.sourcing.repository.constants.SourcingCommonConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 业务场景类型前缀
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum BizNoRuleEnum {
    INQUIRY_ORDER("inquiry-order", "RFQ{period}{no}", SourcingCommonConstants.SIMPLE_DATE_PATTERN, "%04d", "询价单"),
    DESIGNATION_ORDER("designation-order", "BFP{period}{no}", SourcingCommonConstants.SIMPLE_DATE_PATTERN, "%04d", "商务定点单"),
    DESIGNATION_ORDER_NOTIFICATION("designation-order-notification", "F{0}{1}", SourcingCommonConstants.SIMPLE_DATE_PATTERN, "%02d", "商务定点单通知书"),
    BIDDING("bidding", "CP{period}{no}", SourcingCommonConstants.SIMPLE_DATE_PATTERN, "%04d", "竞价单"),
    PPAP_PLAN_NO("ppap-plan-no", "PPAP{period}{no}", SourcingCommonConstants.SIMPLE_DATE_PATTERN, "%06d", "PPAP计划"),
    PROJECT_CHANGE_NO("project-change-no", "PCN{period}{no}", "yy", "%06d", "工程变更");
    private final String idType;
    private final String template;
    private final String period;
    private final String length;
    private final String description;
}
