package com.weifu.srm.sourcing.controller;


import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.sourcing.api.PpapReleaseApi;
import com.weifu.srm.sourcing.request.IdReqDTO;
import com.weifu.srm.sourcing.request.OperateUserReqDTO;
import com.weifu.srm.sourcing.request.ppap.*;
import com.weifu.srm.sourcing.response.AttachmentRecordRespDTO;
import com.weifu.srm.sourcing.response.ppap.AttachmentRecordInfoRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseDetailRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseEngineerRowRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseFileRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseInfoRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleasePSWTimeRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseReportRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseTaskRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseWorkbenchRespDTO;
import com.weifu.srm.sourcing.service.PpapReleaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 批产放行 前端控制器
 * @Date 2024-09-04
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
public class PpapReleaseController implements PpapReleaseApi {
    private final PpapReleaseService ppapReleaseService;

    @Override
    public ApiResponse<PageResponse<PpapReleaseRespDTO>> page(PpapReleasePageReqDTO reqDTO) {
        return ApiResponse.success(ppapReleaseService.page(reqDTO));
    }

    @Override
    public ApiResponse<PpapReleaseDetailRespDTO> detail(String releaseNo) {
        return ApiResponse.success(ppapReleaseService.detail(releaseNo));
    }

    @Override
    public ApiResponse<PpapReleaseInfoRespDTO> info(String releaseNo) {
        return ApiResponse.success(ppapReleaseService.info(releaseNo));
    }

    @Override
    public ApiResponse<Void> sqe(PpapReleaseSqeReqDTO reqDTO) {
        ppapReleaseService.sqe(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> notifyMaster(PpapReleaseNotifyMasterReqDTO reqDTO) {
        ppapReleaseService.notifyMaster(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> masterConfirm(PpapReleaseMasterConfirmReqDTO reqDTO) {
        ppapReleaseService.masterConfirm(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> giveBack(PpapReleaseApprovalReqDTO reqDTO) {
        ppapReleaseService.giveBack(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> submitMasterConfirm(PpapReleaseApprovalReqDTO reqDTO) {
        ppapReleaseService.submitMasterConfirm(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> masterVerify(PpapReleaseVerifyReqDTO reqDTO) {
        ppapReleaseService.masterVerify(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> originatingUpdateTask(PpapReleaseApprovalReqDTO reqDTO) {
        ppapReleaseService.originatingUpdateTask(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> approval(PpapReleaseApprovalReqDTO reqDTO) {
        ppapReleaseService.approval(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> signature(PpapReleaseSignatureReqDTO reqDTO) {
        ppapReleaseService.signature(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<List<PpapReleaseTaskRespDTO>> listReleasePlanTask(String planNo, String liabilityType) {
        return ApiResponse.success(ppapReleaseService.listReleasePlanTask(planNo, liabilityType));
    }

    @Override
    public ApiResponse<Void> saveVerifyReportFile(PpapReleasePlanTaskSaveReqDTO reqDTO) {
        ppapReleaseService.saveVerifyReportFile(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> completePlanTask(PpapReleasePlanTaskCompleteReqDTO reqDTO) {
        ppapReleaseService.completePlanTask(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> close(PpapReleaseCloseReqDTO reqDTO) {
        ppapReleaseService.close(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<PageResponse<PpapReleaseReportRespDTO>> pageReport(PpapReleaseReportPageReqDTO reqDTO) {
        return ApiResponse.success(ppapReleaseService.pageReport(reqDTO));
    }

    @Override
    public ApiResponse<PpapReleaseWorkbenchRespDTO> workbench(PpapReleaseWorkbenchReqDTO reqDTO) {
        return ApiResponse.success(ppapReleaseService.workbench(reqDTO));
    }

    @Override
    public ApiResponse<List<PpapReleaseEngineerRowRespDTO>> executeSituation(PpapReleaseExecuteSituationReqDTO reqDTO) {
        return ApiResponse.success(ppapReleaseService.executeSituation(reqDTO));
    }

    @Override
    public ApiResponse<PpapReleaseFileRespDTO> getReleaseFile(String releaseNo) {
        return ApiResponse.success(ppapReleaseService.getReleaseFile(releaseNo));
    }

    @Override
    public ApiResponse<List<AttachmentRecordRespDTO>> listAnnex(String releaseNo) {
        return ApiResponse.success(ppapReleaseService.listAnnex(releaseNo));
    }

    @Override
    public ApiResponse<List<PpapReleasePSWTimeRespDTO>> pswList(PpapReleasePSWTimeReqDTO reqDTO) {
        return ApiResponse.success(ppapReleaseService.pswList(reqDTO));
    }

    @Override
    public ApiResponse<List<AttachmentRecordInfoRespDTO>> listVerifyReportFile(String planNo) {
        return ApiResponse.success(ppapReleaseService.listVerifyReportFile(planNo));
    }

    @Override
    public ApiResponse<Void> deleteVerifyReportFile(IdReqDTO reqDTO) {
        ppapReleaseService.deleteVerifyReportFile(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<List<AttachmentRecordRespDTO>> listTaskAllFile(String planNo) {
        return ApiResponse.success(ppapReleaseService.listTaskAllFile(planNo));
    }

    @Override
    public ApiResponse<Integer> countByInternal(OperateUserReqDTO reqDTO) {
        return ApiResponse.success(ppapReleaseService.countByInternal(reqDTO));
    }

    @Override
    public ApiResponse<List<PpapReleaseRespDTO>> listByPendingProcessing(OperateUserReqDTO reqDTO) {
        return ApiResponse.success(ppapReleaseService.listByPendingProcessing(reqDTO));
    }
}

