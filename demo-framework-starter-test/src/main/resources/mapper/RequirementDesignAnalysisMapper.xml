<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.framework.mapper.RequirementDesignAnalysisMapper">

    <select id="listRequirementName" resultType="java.lang.String">
        select distinct requirement_name from requirement_design_analysis
        order by requirement_name

    </select>

</mapper>
