<?xml version="1.0" encoding="UTF-8"?>
<!--关闭文件扫描 scan=false -->
<configuration debug="false" scan="false">
    <springProperty scop="context" name="spring.application.name" source="spring.application.name"
                    defaultValue="atm-at-web"/>
    <!--    控制台颜色控制 %red(输出内容) 格式  %d{} 日期时间格式  %-5level日志级别   [%thread] 线程  %msg%n 日志内容 -->
    <!--    日志文件保存路径  项目/logs/子项目/info.log  项目/logs/子项目/warn.log -->
    <property name="log.path" value="logs/${spring.application.name}"/>
    <!-- 控制台输出样式 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%red(%d{MM-dd HH:mm:ss.SSS}) %highlight(%-5level) %green([%thread]) %yellow([%logger{50}]) - %cyan(%msg%n)
            </pattern>
        </encoder>
    </appender>

    <!-- info级别日志文件输出 -->
    <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM}/info.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level [%thread] [%logger{50}] - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 警告级别日志文件输出 -->
    <appender name="warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM}/warn.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level [%thread] [%logger{50}] - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>warn</level>
        </filter>
    </appender>

    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="warn"/>
        <appender-ref ref="info"/>
    </root>

    <!--    指定某个类下某个函数的日志输出控制-->
<!--    <turboFilter class="com.ys.filter.timing.TimingLogFilter"/>-->

    <!-- 屏蔽类sql输出 -->
    <!--    <logger name="com.ys.mapper.act.TaskPlMapper" level="info" additivity="false"/>-->
    <!--    <logger name="com.ys.mapper.act.PlMapper" level="info" additivity="false"/>-->
    <!--    <logger name="com.ys.mapper.act.ActMapper" level="info" additivity="false"/>-->
</configuration>
