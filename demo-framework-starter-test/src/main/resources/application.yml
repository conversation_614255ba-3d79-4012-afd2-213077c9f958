server:
  port: 8081
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://*************:3306/test?autoReconnect=true&useUnicode=true&characterEncoding=utf8&useSSL=false&rewriteBatchedStatements=true
    username: root
    password: 123456



#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.demo.framework.model.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      # 逻辑删除全局属性名(驼峰和下划线都支持)
      logic-delete-field: valid
      logic-delete-value: 0 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 1 # 逻辑未删除值(默认为 0)
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

springdoc:
  # get请求多参数时不需要添加额外的@ParameterObject和@Parameter注解
  default-flat-param-object: true
  # 启用swaggerUI
  swagger-ui:
    #自定义swagger前端请求路径，输入http：127.0.0.1:8080/swagger-ui.html会自动重定向到swagger页面
    path: /swagger-ui.html
    enabled: true
    #    tags-sorter: alpha # 标签的排序方式 alpha:按照子母顺序排序（@ApiSupport注解排序不生效，因此需要设置）
    #    operations-sorter: alpha # 接口的排序方式 alpha:按照子母顺序排序（@ApiOperationSupport注解排序生效，因此这里不作设置）
    operations-sorter: order # 设置规则为order，该规则会使用Knife4j的增强排序扩展规则`x-order`
  # 启用文档，默认开启
  api-docs:
    path: /v3/api-docs    #swagger后端请求地址
    enabled: true




knife4j:
  #是否启用增强设置
  enable: true
  #开启生产环境屏蔽
  production: false
  #是否启用登录认证
  basic:
    enable: true
    username: admin
    password: 123456
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    swagger-model-name: 用户模块
#redisson:
#  singleServerConfig:
#    address: "redis://*************:6379"
##    password: "${spring.redis.password}"
#    database: ${spring.redis.database}
#    clientName: "redisson-client"
#    connectionPoolSize: 64
#    connectionMinimumIdleSize: 24
#    idleConnectionTimeout: 10000
#    connectTimeout: 10000
#    timeout: 3000
#    retryAttempts: 3
#    retryInterval: 1500
cosid:
  worker:
    # 节点ID，分布式环境下每个节点需设置不同的值
    node-id: 1
  snowflake:
    # 时间戳左移22位，默认值22
    timestamp-left-shift: 22
    # 序列号占用12位，默认值12
    sequence-bit: 12