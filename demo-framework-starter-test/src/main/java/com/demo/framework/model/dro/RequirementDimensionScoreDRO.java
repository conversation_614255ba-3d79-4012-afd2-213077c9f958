package com.demo.framework.model.dro;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "需求文档指标评分响应对象")
public class RequirementDimensionScoreDRO {
    
    @Schema(description = "主键ID", example = "1")
    private Integer id;

    @Schema(description = "文件名称", example = "requirement_score.docx")
    private String fileName;

    @Schema(description = "功能模块名称", example = "用户管理模块")
    private String moduleTitle;

    @Schema(description = "需求名称", example = "用户注册功能")
    private String requirementName;

    @Schema(description = "文件类型", example = "application/pdf")
    private String contentType;

    @Schema(description = "评分结果描述", example = "二进制评分结果数据")
    private byte[] requirementDescription;

    @Schema(description = "评分指标", example = "功能完整性")
    private String pointDimension;

    @Schema(description = "总分", example = "100.0", minimum = "0")
    private BigDecimal fullScore;

    @Schema(description = "评分", example = "85.5", minimum = "0")
    private BigDecimal score;

    @Schema(description = "创建时间", example = "2024-01-01T10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "最后更新时间", example = "2024-01-01T10:00:00")
    private LocalDateTime updateTime;
}
