package com.demo.framework.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 技术设计文档表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@TableName("requirement_design_detail")
@Schema(description = "需求设计详情实体")
public class RequirementDesignDetail   {


    @Schema(description = "唯一主键", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "文件名称", example = "requirement_design.docx")
    private String fileName;

    @Schema(description = "功能模块名称", example = "用户管理模块")
    private String moduleTitle;

    @Schema(description = "需求名称", example = "用户注册功能")
    private String requirementName;

    @Schema(description = "内容类型", example = "application/pdf")
    private String contentType;

    @Schema(description = "需求设计文档内容", example = "二进制文档数据")
    private byte[] requirementDesign;

    @Schema(description = "创建时间", example = "2024-01-01T10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "最后更新时间", example = "2024-01-01T10:00:00")
    private LocalDateTime updateTime;


}
