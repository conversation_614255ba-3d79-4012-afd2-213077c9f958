package com.demo.framework.model.dio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "需求文档指标评分输入对象")
public class RequirementDimensionScoreDIO {
    
    @Schema(description = "文件名称", example = "requirement_score.docx")
    private String fileName;

    @Schema(description = "功能模块名称", example = "用户管理模块")
    private String moduleTitle;

    @Schema(description = "需求名称", example = "用户注册功能")
    private String requirementName;

    @Schema(description = "文件类型", example = "application/pdf")
    private String contentType;

    @Schema(description = "评分结果描述", example = "二进制评分结果数据")
    private byte[] requirementDescription;

    @Schema(description = "评分指标", example = "功能完整性")
    private String pointDimension;

    @Schema(description = "总分", example = "100.0", minimum = "0")
    private BigDecimal fullScore;

    @Schema(description = "评分", example = "85.5", minimum = "0")
    private BigDecimal score;
}
