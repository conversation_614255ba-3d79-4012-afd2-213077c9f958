package com.demo.framework.model.dio;

import com.demo.framework.common.core.dto.BasePageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class RequirementDesignAnalysisPageDIO extends BasePageDTO {
    private String fileName;

    /**
     * 功能模块名称
     */
    private String moduleTitle;

    /**
     * 需求名称
     */
    private String requirementName;

}
