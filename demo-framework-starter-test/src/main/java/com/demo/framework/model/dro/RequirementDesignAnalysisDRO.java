package com.demo.framework.model.dro;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class RequirementDesignAnalysisDRO {
    private Integer id;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 功能模块名称
     */
    private String moduleTitle;

    /**
     * 需求名称
     */
    private String requirementName;
    /**
     * 评分维度
     */
    private String pointDimension;

    /**
     * AI理解度
     */
    private BigDecimal aiComprehension;

    /**
     * AI产出质量
     */
    private BigDecimal aiOutputQuality;

    /**
     * 维度得分
     */
    private BigDecimal dimensionScore;

    /**
     * 需求说明
     */
    private String requirementDescription;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
}
