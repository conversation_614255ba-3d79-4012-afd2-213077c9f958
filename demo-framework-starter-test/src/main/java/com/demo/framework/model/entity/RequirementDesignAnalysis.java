package com.demo.framework.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 技术设计分析表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@TableName("requirement_design_analysis")
public class RequirementDesignAnalysis   {
    /**
     * 唯一主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 功能模块名称
     */
    private String moduleTitle;

    /**
     * 需求名称
     */
    private String requirementName;

    /**
     * 评分维度
     */
    private String pointDimension;

    /**
     * AI理解度
     */
    private BigDecimal aiComprehension;

    /**
     * AI产出质量
     */
    private BigDecimal aiOutputQuality;

    /**
     * 维度得分
     */
    private BigDecimal dimensionScore;

    /**
     * 需求说明
     */
    private String requirementDescription;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;


}
