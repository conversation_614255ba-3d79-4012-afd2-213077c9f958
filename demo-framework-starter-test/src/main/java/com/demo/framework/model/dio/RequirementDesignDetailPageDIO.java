package com.demo.framework.model.dio;

import com.demo.framework.common.core.dto.BasePageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "需求设计详情分页查询输入对象")
public class RequirementDesignDetailPageDIO extends BasePageDTO {
    private String fileName;

    /**
     * 功能模块名称
     */
    private String moduleTitle;

    /**
     * 需求名称
     */
    private String requirementName;

}
