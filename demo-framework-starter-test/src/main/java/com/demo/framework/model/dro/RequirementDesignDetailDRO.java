package com.demo.framework.model.dro;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RequirementDesignDetailDRO {
    private Integer id;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 功能模块名称
     */
    private String moduleTitle;

    /**
     * 需求名称
     */
    private String requirementName;

    private String contentType;
    /**
     * 需求设计
     */
    private byte[] requirementDesign;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
}
