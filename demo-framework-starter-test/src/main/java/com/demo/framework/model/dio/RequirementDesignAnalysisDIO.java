package com.demo.framework.model.dio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "需求设计分析输入对象")
public class RequirementDesignAnalysisDIO {
    @Schema(description = "文件名称", example = "requirement_analysis.docx")
    private String fileName;

    @Schema(description = "功能模块名称", example = "用户管理模块")
    private String moduleTitle;

    @Schema(description = "需求名称", example = "用户注册功能")
    private String requirementName;

    @Schema(description = "AI理解度", example = "8.5", minimum = "0", maximum = "10")
    private BigDecimal aiComprehension;

    @Schema(description = "AI产出质量", example = "9.0", minimum = "0", maximum = "10")
    private BigDecimal aiOutputQuality;

    @Schema(description = "维度得分", example = "8.8", minimum = "0", maximum = "10")
    private BigDecimal dimensionScore;

    @Schema(description = "需求说明", example = "用户可以通过邮箱和手机号进行注册")
    private String requirementDescription;

    @Schema(description = "评分维度", example = "功能完整性")
    private String pointDimension;
}
