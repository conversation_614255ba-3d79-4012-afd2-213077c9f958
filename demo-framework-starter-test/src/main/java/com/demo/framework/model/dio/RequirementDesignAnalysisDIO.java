package com.demo.framework.model.dio;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class RequirementDesignAnalysisDIO {
    private String fileName;

    /**
     * 功能模块名称
     */
    private String moduleTitle;

    /**
     * 需求名称
     */
    private String requirementName;

    /**
     * AI理解度
     */
    private BigDecimal aiComprehension;

    /**
     * AI产出质量
     */
    private BigDecimal aiOutputQuality;

    /**
     * 维度得分
     */
    private BigDecimal dimensionScore;

    /**
     * 需求说明
     */
    private String requirementDescription;

    private String pointDimension;
}
