package com.demo.framework.model.dio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "需求设计详情输入对象")
public class RequirementDesignDetailDIO {
    private String fileName;

    /**
     * 功能模块名称
     */
    private String moduleTitle;

    /**
     * 需求名称
     */
    private String requirementName;
    private String contentType;

    /**
     * 需求设计
     */
    private byte[] requirementDesign;
}
