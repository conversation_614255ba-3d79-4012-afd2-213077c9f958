package com.demo.framework.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 需求文档指标评分表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("requirement_dimension_score")
@Schema(description = "需求文档指标评分实体")
public class RequirementDimensionScore  {

    @Schema(description = "唯一主键", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "文件名称", example = "requirement_score.docx")
    private String fileName;

    @Schema(description = "功能模块名称", example = "用户管理模块")
    private String moduleTitle;

    @Schema(description = "需求名称", example = "用户注册功能")
    private String requirementName;

    @Schema(description = "文件类型", example = "application/pdf")
    private String contentType;

    @Schema(description = "评分结果描述", example = "二进制评分结果数据")
    private byte[] requirementDescription;

    @Schema(description = "评分指标", example = "功能完整性")
    private String pointDimension;

    @Schema(description = "总分", example = "100.0", minimum = "0")
    private BigDecimal fullScore;

    @Schema(description = "评分", example = "85.5", minimum = "0")
    private BigDecimal score;

    @Schema(description = "创建时间", example = "2024-01-01T10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "最后更新时间", example = "2024-01-01T10:00:00")
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getModuleTitle() {
        return moduleTitle;
    }

    public void setModuleTitle(String moduleTitle) {
        this.moduleTitle = moduleTitle;
    }

    public String getRequirementName() {
        return requirementName;
    }

    public void setRequirementName(String requirementName) {
        this.requirementName = requirementName;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public byte[] getRequirementDescription() {
        return requirementDescription;
    }

    public void setRequirementDescription(byte[] requirementDescription) {
        this.requirementDescription = requirementDescription;
    }

    public String getPointDimension() {
        return pointDimension;
    }

    public void setPointDimension(String pointDimension) {
        this.pointDimension = pointDimension;
    }

    public BigDecimal getFullScore() {
        return fullScore;
    }

    public void setFullScore(BigDecimal fullScore) {
        this.fullScore = fullScore;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "RequirementDimensionScore{" +
            "id = " + id +
            ", fileName = " + fileName +
            ", moduleTitle = " + moduleTitle +
            ", requirementName = " + requirementName +
            ", contentType = " + contentType +
            ", requirementDescription = " + requirementDescription +
            ", pointDimension = " + pointDimension +
            ", fullScore = " + fullScore +
            ", score = " + score +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            "}";
    }
}
