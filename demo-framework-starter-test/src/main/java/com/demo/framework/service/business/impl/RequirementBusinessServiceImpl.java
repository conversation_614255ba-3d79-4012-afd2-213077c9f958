package com.demo.framework.service.business.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demo.framework.common.core.dto.ResponseDTO;
import com.demo.framework.model.dio.RequirementDesignAnalysisDIO;
import com.demo.framework.model.dio.RequirementDesignAnalysisPageDIO;
import com.demo.framework.model.dio.RequirementDesignDetailDIO;
import com.demo.framework.model.dio.RequirementDesignDetailPageDIO;
import com.demo.framework.model.dro.RequirementDesignAnalysisDRO;
import com.demo.framework.model.dro.RequirementDesignDetailDRO;
import com.demo.framework.model.entity.RequirementDesignAnalysis;
import com.demo.framework.model.entity.RequirementDesignDetail;
import com.demo.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.demo.framework.service.business.RequirementBusinessService;
import com.demo.framework.service.service.IRequirementDesignAnalysisService;
import com.demo.framework.service.service.IRequirementDesignDetailService;
import com.demo.framework.service.service.IRequirementDimensionScoreService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

@Service
@AllArgsConstructor
public class RequirementBusinessServiceImpl implements RequirementBusinessService {
    private final IRequirementDesignAnalysisService resourceDesignAnalysisService;
    private final IRequirementDesignDetailService requirementDesignDetailService;
    private final IRequirementDimensionScoreService requirementDimensionScoreService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer saveRequirementDetail(RequirementDesignDetailDIO dio) {
        RequirementDesignDetail reqDesignDetail = BeanUtil.copyProperties(dio, RequirementDesignDetail.class);
        LocalDateTime now = LocalDateTime.now();
        reqDesignDetail.setCreateTime(now);
        reqDesignDetail.setUpdateTime(now);
        requirementDesignDetailService.save(reqDesignDetail);
        return reqDesignDetail.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer saveRequirementAnalysis(List<RequirementDesignAnalysisDIO> dioList) {
        List<RequirementDesignAnalysis> requestDesignAnalysisList = BeanUtil.copyToList(dioList, RequirementDesignAnalysis.class);
        LocalDateTime now = LocalDateTime.now();
        for (RequirementDesignAnalysis requirementDesignAnalysis : requestDesignAnalysisList) {
            requirementDesignAnalysis.setCreateTime(now);
            requirementDesignAnalysis.setUpdateTime(now);
        }
        resourceDesignAnalysisService.saveBatch(requestDesignAnalysisList);
        return requestDesignAnalysisList.size();
    }

    @Override
    public ResponseDTO<List<RequirementDesignDetailDRO>> pageRequirementDetail(RequirementDesignDetailPageDIO dio) {
        Page<RequirementDesignDetail> page = new Page<>(dio.getPageNo(), dio.getPageSize());
        LambdaQueryWrapperX<RequirementDesignDetail> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        lambdaQueryWrapper.likeIfPresent(RequirementDesignDetail::getFileName,dio.getFileName());
        lambdaQueryWrapper.likeIfPresent(RequirementDesignDetail::getModuleTitle,dio.getModuleTitle());
        lambdaQueryWrapper.likeIfPresent(RequirementDesignDetail::getRequirementName,dio.getRequirementName());
        IPage<RequirementDesignDetail> pageResult = requirementDesignDetailService.page(page,lambdaQueryWrapper);
        List<RequirementDesignDetailDRO> droList =  BeanUtil.copyToList(pageResult.getRecords(), RequirementDesignDetailDRO.class);
        return ResponseDTO.success(droList,pageResult.getTotal());
    }

    @Override
    public ResponseDTO<List<RequirementDesignAnalysisDRO>> pageRequirementAnalysis(RequirementDesignAnalysisPageDIO dio) {
        IPage<RequirementDesignAnalysis> page = new Page<>(dio.getPageNo(), dio.getPageSize());
        LambdaQueryWrapperX<RequirementDesignAnalysis> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        lambdaQueryWrapper.likeIfPresent(RequirementDesignAnalysis::getFileName,dio.getFileName());
        lambdaQueryWrapper.likeIfPresent(RequirementDesignAnalysis::getModuleTitle,dio.getModuleTitle());
        lambdaQueryWrapper.eqIfPresent(RequirementDesignAnalysis::getRequirementName,dio.getRequirementName());
        IPage<RequirementDesignAnalysis> pageResult = resourceDesignAnalysisService.page(page,lambdaQueryWrapper);
        List<RequirementDesignAnalysisDRO> droList =  BeanUtil.copyToList(pageResult.getRecords(), RequirementDesignAnalysisDRO.class);
        return ResponseDTO.success(droList,pageResult.getTotal());
    }

    @Override
    public RequirementDesignDetailDRO downloadFile(Integer id) {
        RequirementDesignDetail requirementDesignDetail = requirementDesignDetailService.getById(id);
        return BeanUtil.copyProperties(requirementDesignDetail, RequirementDesignDetailDRO.class);
    }

    @Override
    public ResponseDTO<List<String>> listRequirementName() {
        List<String> list = resourceDesignAnalysisService.listRequirementName();
        return ResponseDTO.success(list);
    }

    @Override
    public String handleFileUpload(MultipartFile file, String moduleTitle, String requirementName) {
        if (!file.isEmpty()) {
            try {
                // 处理文件，例如保存到磁盘
                byte[] bytes = file.getBytes();
                // 假设你有一个方法 saveToFileSystem 来保存文件
                saveToFileSystem(bytes, file.getContentType(), file.getOriginalFilename(), moduleTitle, requirementName);
                return "You successfully uploaded '" + file.getOriginalFilename() + "'!";
            } catch (IOException e) {
                return "Failed to upload " + file.getOriginalFilename() + " => " + e.getMessage();
            }
        } else {
            return "Failed to upload. The file is empty.";
        }
    }

    private void saveToFileSystem(byte[] data, String contentType, String filename, String moduleTitle, String requirementName) throws IOException {
        RequirementDesignDetailDIO dio = new RequirementDesignDetailDIO();
        dio.setRequirementDesign(data);
        dio.setFileName(filename);
        dio.setModuleTitle(moduleTitle);
        dio.setRequirementName(requirementName);
        dio.setContentType(contentType);
        // 实现文件保存逻辑
        saveRequirementDetail(dio);
    }
}
