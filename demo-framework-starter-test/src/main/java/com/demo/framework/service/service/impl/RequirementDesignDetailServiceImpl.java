package com.demo.framework.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.framework.mapper.RequirementDesignDetailMapper;
import com.demo.framework.model.entity.RequirementDesignDetail;
import com.demo.framework.service.service.IRequirementDesignDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 技术设计文档表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class RequirementDesignDetailServiceImpl extends ServiceImpl<RequirementDesignDetailMapper, RequirementDesignDetail> implements IRequirementDesignDetailService {

}
