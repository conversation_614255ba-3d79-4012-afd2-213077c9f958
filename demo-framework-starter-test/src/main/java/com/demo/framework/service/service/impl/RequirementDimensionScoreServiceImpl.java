package com.demo.framework.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.framework.mapper.RequirementDimensionScoreMapper;
import com.demo.framework.model.entity.RequirementDimensionScore;
import com.demo.framework.service.service.IRequirementDimensionScoreService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 需求文档指标评分表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class RequirementDimensionScoreServiceImpl extends ServiceImpl<RequirementDimensionScoreMapper, RequirementDimensionScore> implements IRequirementDimensionScoreService {

}
