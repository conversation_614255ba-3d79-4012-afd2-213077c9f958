package com.demo.framework.service.business;

import com.demo.framework.common.core.dto.ResponseDTO;
import com.demo.framework.model.dio.RequirementDesignAnalysisDIO;
import com.demo.framework.model.dio.RequirementDesignAnalysisPageDIO;
import com.demo.framework.model.dio.RequirementDesignDetailDIO;
import com.demo.framework.model.dio.RequirementDesignDetailPageDIO;
import com.demo.framework.model.dro.RequirementDesignAnalysisDRO;
import com.demo.framework.model.dro.RequirementDesignDetailDRO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface RequirementBusinessService {
    Integer saveRequirementDetail(RequirementDesignDetailDIO dio);

    Integer saveRequirementAnalysis(List<RequirementDesignAnalysisDIO> dioList);

    ResponseDTO<List<RequirementDesignDetailDRO>> pageRequirementDetail(RequirementDesignDetailPageDIO dio);

    ResponseDTO<List<RequirementDesignAnalysisDRO>> pageRequirementAnalysis(RequirementDesignAnalysisPageDIO dio);

    RequirementDesignDetailDRO downloadFile(Integer id);

    ResponseDTO<List<String>> listRequirementName();

    String handleFileUpload(MultipartFile file, String moduleTitle, String requirementName);
}
