package com.demo.framework.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.framework.mapper.RequirementDesignAnalysisMapper;
import com.demo.framework.model.entity.RequirementDesignAnalysis;
import com.demo.framework.service.service.IRequirementDesignAnalysisService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 技术设计分析表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class RequirementDesignAnalysisServiceImpl extends ServiceImpl<RequirementDesignAnalysisMapper, RequirementDesignAnalysis> implements IRequirementDesignAnalysisService {
    @Override
    public List<String> listRequirementName() {
        return this.getBaseMapper().listRequirementName();
    }
}
