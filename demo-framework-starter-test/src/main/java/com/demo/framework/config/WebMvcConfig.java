//package com.demo.framework.config;
//
//import com.github.xiaoymin.knife4j.core.enums.OpenAPILanguageEnums;
//import com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting;
//import io.swagger.v3.oas.models.info.Info;
//import org.springdoc.core.customizers.OpenApiCustomizer;
//import org.springdoc.core.models.GroupedOpenApi;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
///**
// * web层配置类,实现静态资源映射，将knife4j相关资源放行，保证生成的接口文档能够正常进行展示
// * <AUTHOR>
// */
//@Configuration
//public class WebMvcConfig implements WebMvcConfigurer {
//
//    /**
//     * 设置静态资源映射
//     */
//    @Override
//    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        // 添加静态资源映射规则
//        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
//        //配置 knife4j 的静态资源请求映射地址
//        registry.addResourceHandler("/doc.html")
//                .addResourceLocations("classpath:/META-INF/resources/");
//        registry.addResourceHandler("/webjars/**")
//                .addResourceLocations("classpath:/META-INF/resources/webjars/");
//        System.out.println("WebMvcConfig===============");
//    }
//    // SpringDoc OpenAPI 配置
//    @Bean
//    public OpenApiCustomizer openApiCustomizer() {
//        return openApi -> {
//            openApi.getPaths().values().stream()
//                    .flatMap(pathItem -> pathItem.readOperations().stream())
//                    .forEach(operation -> operation.addTagsItem("default"));
//
//            openApi.setInfo(new Info()
//                    .title("API 文档")
//                    .version("1.0.0"));
//
//            openApi.getInfo().getContact()
//                    .name("多宝")
//                    .email("<EMAIL>");
//        };
//    }
//
//    @Bean
//    public GroupedOpenApi defaultGroupedOpenApi() {
//        return GroupedOpenApi.builder()
//                .group("default")
//                .pathsToMatch("/**")
//                .packagesToScan("com.xiaominfo.knife4j.demo.web")
//                .build();
//    }
//
//    // Knife4j 配置
//    @Bean
//    public Knife4jSetting knifeSetting() {
//        Knife4jSetting setting = new Knife4jSetting();
//        setting.setLanguage(OpenAPILanguageEnums.ZH_CN);
//        return setting;
//    }
//}

