package com.demo.framework.controller;

import com.demo.framework.common.core.dto.ResponseDTO;
import com.demo.framework.model.dio.RequirementDesignAnalysisDIO;
import com.demo.framework.model.dio.RequirementDesignAnalysisPageDIO;
import com.demo.framework.model.dio.RequirementDesignDetailDIO;
import com.demo.framework.model.dio.RequirementDesignDetailPageDIO;
import com.demo.framework.model.dro.RequirementDesignAnalysisDRO;
import com.demo.framework.model.dro.RequirementDesignDetailDRO;
import com.demo.framework.service.business.RequirementBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/requirement")
@Tag(name = "需求", description = "需求相关操作")
@Validated
public class RequirementController {
    private final RequirementBusinessService requirementBusinessService;

    @Operation(summary = "新增设计文档")
    @PostMapping("/design/save")
    public ResponseDTO<Integer> saveRequirementDetail(@RequestBody @Valid RequirementDesignDetailDIO dio) {
        return ResponseDTO.success(requirementBusinessService.saveRequirementDetail(dio));
    }

    @Operation(summary = "新增分析文档")
    @PostMapping("/analysis/save")
    public ResponseDTO<Integer> saveRequirementAnalysis(@RequestBody @Valid List<RequirementDesignAnalysisDIO> dioList) {
        return ResponseDTO.success(requirementBusinessService.saveRequirementAnalysis(dioList));
    }

    @PostMapping("/design/page")
    @Operation(summary = "设计文档分页查询")
    public ResponseDTO<List<RequirementDesignDetailDRO>> pageRequirementDetail(@RequestBody @Valid RequirementDesignDetailPageDIO dio) {
        return requirementBusinessService.pageRequirementDetail(dio);
    }

    @PostMapping("/analysis/page")
    @Operation(summary = "分析文档分页查询")
    public ResponseDTO<List<RequirementDesignAnalysisDRO>> pageRequirementAnalysis(@RequestBody @Valid RequirementDesignAnalysisPageDIO dio) {
        return requirementBusinessService.pageRequirementAnalysis(dio);
    }

    @Operation(summary = "需求名称列表")
    @GetMapping("/design/list")
    public ResponseDTO<List<String>> listRequirementName(){
        return requirementBusinessService.listRequirementName();
    }
    @Operation(summary = "上传需求设计文档")
    @PostMapping("/upload")
    public ResponseDTO<String> handleFileUpload(@RequestParam("file") MultipartFile file,
                                   @RequestParam(value = "moduleTitle", required = false) String moduleTitle,
                                   @RequestParam(value = "requirementName", required = false) String requirementName) {
        return ResponseDTO.success(requirementBusinessService.handleFileUpload(file,moduleTitle,requirementName));
    }



    @Operation(summary = "下载需求文档")
    @GetMapping("/download/{id}")
    public ResponseEntity<ByteArrayResource> downloadFile(@PathVariable("id") Integer id) {
        RequirementDesignDetailDRO dbFile = requirementBusinessService.downloadFile(id);
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(dbFile.getContentType()))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + dbFile.getFileName() + "\"")
                .body(new ByteArrayResource(dbFile.getRequirementDesign()));
    }


    @Operation(summary = "查看md文档")
    @GetMapping("/markdown/{id}")
    public ResponseDTO<ByteArrayResource> getDesign(@PathVariable("id") Integer id) {
        RequirementDesignDetailDRO dbFile = requirementBusinessService.downloadFile(id);
        return ResponseDTO.success(new ByteArrayResource(dbFile.getRequirementDesign()));
    }
}
