package com.weifu.srm.supplier.service.biz.recover;

import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.constants.MQSupplierTopicConstants;
import com.weifu.srm.supplier.dto.SupplierBasicStatusUpdateDTO;
import com.weifu.srm.supplier.manager.BlackListManager;
import com.weifu.srm.supplier.manager.GreyListManager;
import com.weifu.srm.supplier.manager.SupplierBasicInfoDetailManager;
import com.weifu.srm.supplier.mq.SupplierRecoverMQ;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.enums.CommonStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierKeyInfoChangeTypeEnum;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierRecoverRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 供应商恢复审核结果处理
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SupplierRecoverApplyHandleBiz {

    private final SupplierRecoverRecordMapperService supplierRecoverRecordMapperService;
    private final SupplierBasicInfoDetailManager supplierBasicInfoDetailManager;
    private final TransactionTemplate transactionTemplate;
    private final GreyListManager greyListManager;
    private final BlackListManager blackListManager;
    private final MqManager mqManager;

    public void handleRecoverAudit(TicketStatusChangedMQ ticketInfo) {
        if (TicketStatusEnum.APPROVING.equalsCode(ticketInfo.getStatus())) {
            log.info("this audit is process  status={}", ticketInfo);
            supplierRecoverRecordMapperService.lambdaUpdate()
                    .eq(SupplierRecoverRecordPO::getApplyNo, ticketInfo.getBusinessNo())
                    .set(SupplierRecoverRecordPO::getTicketNo, ticketInfo.getTicketNo())
                    .update();
            return;
        }
        // 修改审批状态
        updateAuditStatus(ticketInfo);
        // 拒绝/关闭不进行任何操作
        if (TicketStatusEnum.REJECTED.equalsCode(ticketInfo.getStatus()) || TicketStatusEnum.CANCELED.equalsCode(ticketInfo.getStatus())) {
            return;
        }
        SupplierRecoverRecordPO recoverRecord = supplierRecoverRecordMapperService.lambdaQuery()
                .eq(SupplierRecoverRecordPO::getApplyNo, ticketInfo.getBusinessNo())
                .one();
        String supplierCode = recoverRecord.getSupplierCode();
        SupplierBasicInfoPO basicInfo = supplierBasicInfoDetailManager.getSupplierBasicInfoByCode(supplierCode);
        transactionTemplate.executeWithoutResult(status -> {
            // 修改供应商状态 移除黑/灰名单
            basicInfo.setStatus(SupplierBasicStatusEnum.REGISTERED_SUPPLIER.getCode());
            BaseEntityUtil.setCommonForU(basicInfo, ticketInfo.getOperateBy(), ticketInfo.getOperateName(), null);
            SupplierBasicStatusUpdateDTO dto = new SupplierBasicStatusUpdateDTO();
            dto.setContent("供应商退出后恢复");
            dto.setChangeType(SupplierKeyInfoChangeTypeEnum.STATUS);
            dto.setRemark(SupplierBasicStatusEnum.getByCode(basicInfo.getStatus()).getChineseName().concat("->").concat(SupplierBasicStatusEnum.REGISTERED_SUPPLIER.getChineseName()));
            dto.setSupplierBasicInfoPO(basicInfo);
            dto.setBusinessNo(ticketInfo.getBusinessNo());

            // 修改状态
            supplierBasicInfoDetailManager.updateSupplierBasicStatus(dto);
            // 移除灰名单
            greyListManager.removeGreyListWhenSupplierRecover(basicInfo.getSapSupplierCode(),ticketInfo.getUpdateTime());
            // 移除黑名单
            blackListManager.removeBlackListWhenSupplierRecover(basicInfo.getSapSupplierCode(),ticketInfo.getUpdateTime());

            SupplierRecoverMQ mq = new SupplierRecoverMQ();
            mq.setBusinessNo(ticketInfo.getBusinessNo());
            mq.setSupplierCode(basicInfo.getSapSupplierCode());
            mq.setOperationUserId(ticketInfo.getOperateBy());
            mq.setOperationUser(ticketInfo.getOperateName());
            mq.setOperationTime(ticketInfo.getUpdateTime());
            // 发送供应商恢复通知MQ
            mqManager.sendTopic(MQSupplierTopicConstants.SUPPLIER_RECOVER_LIST_TOPIC, JacksonUtil.bean2Json(mq));
        });
    }


    private void updateAuditStatus(TicketStatusChangedMQ ticketInfo) {
        supplierRecoverRecordMapperService.lambdaUpdate()
                .eq(SupplierRecoverRecordPO::getApplyNo, ticketInfo.getBusinessNo())
                .set(SupplierRecoverRecordPO::getStatus, getAuditStatus(ticketInfo))
                .set(SupplierRecoverRecordPO::getUpdateBy, ticketInfo.getOperateBy())
                .set(SupplierRecoverRecordPO::getUpdateName, ticketInfo.getOperateName())
                .set(SupplierRecoverRecordPO::getRecoverTime, ticketInfo.getUpdateTime())
                .update();
    }

    private String getAuditStatus(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        switch (statusEnum) {
            case APPROVED:
                return CommonStatusEnum.AGREE_STATUS.getCode();
            case CANCELED:
                return CommonStatusEnum.CANCEL.getCode();
            case REJECTED:
                return CommonStatusEnum.REJECT_STATUS.getCode();
            default:
                log.error("not support status={}", ticketInfo);
                throw new BizFailException("not support status");
        }
    }
}
