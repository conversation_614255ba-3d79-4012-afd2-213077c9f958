package com.weifu.srm.supplier.convert;

import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierGradeAdjustApplyItemPO;
import com.weifu.srm.supplier.request.SupplierGradeAdjustDetailReqDTO;
import com.weifu.srm.supplier.request.SupplierGradeHistoryReqDTO;
import com.weifu.srm.supplier.request.SupplierGradeReqDTO;
import com.weifu.srm.supplier.response.SupplierGradeHistoryRespDTO;
import com.weifu.srm.supplier.response.SupplierGradeRespDTO;
import com.weifu.srm.supplier.response.SupplierGradeWorkDetailRespDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SupplierGradeConvert {

    SupplierBasicInfoPO toPo(SupplierGradeReqDTO supplierGradeReqDTO);

    List<SupplierGradeRespDTO> toResult(List<SupplierBasicInfoPO> supplierBasicMsgPOS);

    SupplierGradeAdjustApplyItemPO toApplyPo(SupplierGradeAdjustDetailReqDTO supplierGradeAdjustDetailReqDTOS);

    SupplierGradeWorkDetailRespDTO toApplyResult(SupplierGradeAdjustApplyItemPO supplierGradeAdjustApplyItemPO);

    SupplierGradeAdjustApplyItemPO historyToPO(SupplierGradeHistoryReqDTO supplierGradeHistoryReqDTO);

    List<SupplierGradeHistoryRespDTO> toHistoryResult(List<SupplierGradeAdjustApplyItemPO> supplierGradeAdjustApplyItemPOS);
}
