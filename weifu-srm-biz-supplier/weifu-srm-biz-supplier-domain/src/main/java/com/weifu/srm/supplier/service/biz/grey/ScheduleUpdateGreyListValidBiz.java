package com.weifu.srm.supplier.service.biz.grey;

import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.convert.GreyListConvert;
import com.weifu.srm.supplier.manager.*;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.SupplierGreyListPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListRecordDetailPO;
import io.swagger.util.Yaml;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 供应商灰名单生效
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ScheduleUpdateGreyListValidBiz {

    private final SupplierGreyListRecordDetailMapperService greyListRecordDetailMapperService;
    private final SupplierGreyListMapperService greyListMapperService;
    private final GreyListConvert greyListConvert;
    private final TransactionTemplate transactionTemplate;
    private final SupplierGradeAdjustManager supplierGradeAdjustManager;
    private final GreyListManager greyListManager;

    public void processWaitValidData() {
        Date currentDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<SupplierGreyListRecordDetailPO> list = greyListRecordDetailMapperService.lambdaQuery()
                .eq(SupplierGreyListRecordDetailPO::getStatus, SupplierGreyListStatusEnum.AUDITED_WAITING.getCode())
                .le(SupplierGreyListRecordDetailPO::getValidTime, currentDate)
                .list();
        for (SupplierGreyListRecordDetailPO detailPO : list) {
            transactionTemplate.executeWithoutResult(status -> {
                // 修改明细状态
                greyListRecordDetailMapperService.lambdaUpdate()
                        .eq(SupplierGreyListRecordDetailPO::getId, detailPO.getId())
                        .set(SupplierGreyListRecordDetailPO::getStatus, SupplierGreyListStatusEnum.LIMITED.getCode())
                        .update();
                SupplierGreyListPO greyList = greyListConvert.toGreyList(detailPO);
                BaseEntityUtil.setCommon(greyList, detailPO.getUpdateBy(), detailPO.getUpdateName(), null);
                // 存入灰名单表
                greyListMapperService.save(greyList);
                // 修改供应商分级
                supplierGradeAdjustManager.updateSupplierGradeToNoGrade(detailPO.getValidNo(), detailPO.getSupplierCode(), "灰名单导致分级失效", -1L, "schedule");
                // 发送通知
                greyListManager.sendGreyListNotice(detailPO, YesOrNoEnum.YES.getCode());
            });
        }
    }
}
