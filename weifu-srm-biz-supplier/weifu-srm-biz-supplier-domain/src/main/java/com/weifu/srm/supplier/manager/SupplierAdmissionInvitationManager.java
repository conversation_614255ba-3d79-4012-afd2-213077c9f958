package com.weifu.srm.supplier.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.convert.CategoryConvert;
import com.weifu.srm.supplier.convert.SupplierAdmissionInvitationConvert;
import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.request.SupplierAdmissionInvitationReqDTO;
import com.weifu.srm.supplier.response.SupplierAdmissionInvitationDetailRespDTO;
import com.weifu.srm.supplier.util.BusinessNoGenerateUtil;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierAdmissionInvitationManager {
    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;
    private final SupplierAdmissionCategoryRecordMapperService categoryRecordMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final LocaleMessage localeMessage;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierAdmissionInvitationConvert invitationConvert;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final CategoryConvert categoryConvert;
    private final QuestionnaireTemplateMapperService questionnaireTemplateMapperService;
    private final IdService idService;
    private final UserSupplierRelationshipInfoMapperService relationshipInfoMapperService;
    private final SysUserManager userManager;

    public void checkRegisterUser(SupplierAdmissionInvitationReqDTO req){
        // 不考虑并发锁
            UserSupplierRelationshipInfoPO one = relationshipInfoMapperService.lambdaQuery()
                    .eq(UserSupplierRelationshipInfoPO::getSupplierName, req.getSupplierName())
                    .eq(UserSupplierRelationshipInfoPO::getRoleId, SupplierUserRoleEnum.SUPPLIER_REGISTER_CONTACTS.getCode())
                    .one();
            if (ObjectUtils.isNotEmpty(one)){
                BaseSysUserRespDTO userInfo = userManager.getUserDetailById(one.getSysUserId());
                if (userInfo == null || !req.getPhone().equals(userInfo.getPhone()) || !req.getSupplierRegistrationContactName().equals(userInfo.getRealName()) ||
                        !req.getSupplierRegistrationContactEmail().equals(userInfo.getEmail())) {
                    throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_REGISTER_USER_NOT_WITH_OTHER));
                }
                return;
            }
        List<String> supplierAdmissionInvitationStatus  = Arrays.asList(AdmissionInvitationStatusEnum.INVITATION_AUDIT_STATUS.getCode(),
                AdmissionInvitationStatusEnum.INVITATION_STATUS.getCode(),AdmissionInvitationStatusEnum.AGREE_STATUS.getCode(),
                AdmissionInvitationStatusEnum.APPROVED_STATUS.getCode());
        List<SupplierAdmissionInvitationRecordPO> list = invitationInfoMapperService.lambdaQuery()
                .eq(SupplierAdmissionInvitationRecordPO::getSupplierName, req.getSupplierName())
                .in(SupplierAdmissionInvitationRecordPO::getSupplierAdmissionInvitationStatus,supplierAdmissionInvitationStatus)
                .orderByDesc(SupplierAdmissionInvitationRecordPO::getCreateTime)
                .list();
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        SupplierAdmissionInvitationRecordPO invitationRecordPO = list.get(0);
        if (!invitationRecordPO.getPhone().equals(req.getPhone()) || !invitationRecordPO.getSupplierRegistrationContactName().equals(req.getSupplierRegistrationContactName()) ||
        !invitationRecordPO.getSupplierRegistrationContactEmail().equals(req.getSupplierRegistrationContactEmail())) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_REGISTER_USER_NOT_WITH_OTHER));
        }
    }

    /**
     * 检查并获取准入邀请单号
     */
    public String getAndCheckInvitationNo(SupplierAdmissionInvitationReqDTO req){
        // 生成准入邀请单号
        if (StringUtils.isNotBlank(req.getInvitationNo())) {
            SupplierAdmissionInvitationRecordPO po = invitationInfoMapperService.lambdaQuery()
                    .eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, req.getInvitationNo())
                    .one();
            if (ObjectUtils.isNotEmpty(po) && !AdmissionInvitationStatusEnum.INIT_STATUS_LIST.contains(po.getSupplierAdmissionInvitationStatus())) {
                log.error("this invitation ticket can not be update ={}", req);
                throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.INVITATION_RECORD_NOT_SUPPORT_UPDATE));
            }
            return req.getInvitationNo();
        }else {
            // 生成单号
            return BusinessNoGenerateUtil.getNextBusinessNo(SupplierBizEnum.SUPPLIER_ADMISSION_INVITATION.getBizTypeCodee(),idService);
        }
    }
    /**
     * 封装准入邀请附件信息
     */
    public List<AttachmentRecordPO> boxSuppAttachment(SupplierAdmissionInvitationReqDTO req) {
        List<AttachmentRecordPO> records = new ArrayList<>(16);
        // 指定证明文件
        List<AttachmentMessageReqDTO> customerDesignationProofAttachments = req.getCustomerDesignationProofAttachments();
        if (customerDesignationProofAttachments != null && !customerDesignationProofAttachments.isEmpty()) {
            List<AttachmentRecordPO> poList = attachmentMessageConvert.toPOList(customerDesignationProofAttachments);
            for (AttachmentRecordPO attachmentRecordPO : poList) {
                attachmentRecordPO.setBusinessNo(req.getInvitationNo());
                attachmentRecordPO.setBusinessType(AttachmentBizTypeConstants.CUSTOMER_SPECIFIED_SUPPORTING_DOCUMENTS);
                BaseEntityUtil.setCommon(attachmentRecordPO, req.getOperationUserId(), req.getOperationUser(), null);
            }
            records.addAll(poList);
        }
        // 补充材料附件
        List<AttachmentMessageReqDTO> attachmentLists = req.getSuppAttachments();
        if (attachmentLists != null && !attachmentLists.isEmpty()) {
            List<AttachmentRecordPO> poList = attachmentMessageConvert.toPOList(attachmentLists);
            for (AttachmentRecordPO attachmentRecordPO : poList) {
                attachmentRecordPO.setBusinessNo(req.getInvitationNo());
                attachmentRecordPO.setBusinessType(AttachmentBizTypeConstants.ADMISSION_INVITATION_SUPPLEMENTARY_FILE);
                BaseEntityUtil.setCommon(attachmentRecordPO, req.getOperationUserId(), req.getOperationUser(), null);
            }
            records.addAll(poList);
        }
        return records;
    }

    /**
     * 封装准入邀请中的品类信息
     * @param req
     * @return
     */
    public  List<SupplierAdmissionCategoryRecordPO> boxSuppCategory(SupplierAdmissionInvitationReqDTO req){
        // 准入品类数据
        return req.getAdmissionCategories().stream().map(item -> {
            SupplierAdmissionCategoryRecordPO categoryPO = new SupplierAdmissionCategoryRecordPO();
            categoryPO.setInvitationNo(req.getInvitationNo());
            categoryPO.setCategoryCode(item.getCategoryCode());
            categoryPO.setCategoryName(item.getCategoryName());
            categoryPO.setAdmissionInvitationBy(req.getOperationUserId());
            categoryPO.setAdmissionInvitationName(req.getOperationUser());
            categoryPO.setAdmissionType(req.getAdmissionType());
            categoryPO.setSupplierName(req.getSupplierName());
            categoryPO.setSupplierType(req.getSupplierType());
            categoryPO.setAdmissionInvitationDivisionCode(req.getDivisionCode());
            BaseEntityUtil.setCommon(categoryPO, req.getOperationUserId(), req.getOperationUser(), null);
            if (ObjectUtils.isNotEmpty(req.getAdmissionEndTime())) {
                categoryPO.setExpirationDate(req.getAdmissionEndTime());
            }
            return categoryPO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取准入邀请详情信息
     */
    public SupplierAdmissionInvitationDetailRespDTO getInvitationDetail(String invitationNo) {
        if (StringUtils.isBlank(invitationNo)) {
            throw new BizFailException(localeMessage.getMessage("supplier.invitation.no.empty"));
        }
        // 查询邀请基础表
        LambdaQueryWrapper<SupplierAdmissionInvitationRecordPO> baseWrapper = new LambdaQueryWrapper<>();
        baseWrapper.eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, invitationNo);
        baseWrapper.eq(SupplierAdmissionInvitationRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
        SupplierAdmissionInvitationRecordPO basicPO = invitationInfoMapperService.getOne(baseWrapper);
        if (ObjectUtils.isEmpty(basicPO)) {
            throw new BizFailException(localeMessage.getMessage("supplier.invitation.not.exist"));
        }
        // 查询附件表
        LambdaQueryWrapper<AttachmentRecordPO> attachWrapper = new LambdaQueryWrapper<>();
        attachWrapper.eq(AttachmentRecordPO::getBusinessNo, invitationNo);
        List<AttachmentRecordPO> attachList = attachmentRecordMapperService.list(attachWrapper);
        // 查询准入品类
        LambdaQueryWrapper<SupplierAdmissionCategoryRecordPO> categoryWrapper = new LambdaQueryWrapper<>();
        categoryWrapper.eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, invitationNo);
        categoryWrapper.eq(SupplierAdmissionCategoryRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
        List<SupplierAdmissionCategoryRecordPO> categories = categoryRecordMapperService.list(categoryWrapper);
        // 查询供应商基础数据表
        LambdaQueryWrapper<SupplierBasicInfoPO> basicInfoWrapper = new LambdaQueryWrapper<>();
        basicInfoWrapper.eq(SupplierBasicInfoPO::getSupplierName, basicPO.getSupplierName());
        SupplierBasicInfoPO supplierBasicInfo = supplierBasicInfoMapperService.getOne(basicInfoWrapper);
        // 组装返回数据
        SupplierAdmissionInvitationDetailRespDTO detailDTO = invitationConvert.toDetailDTO(basicPO);
        if (ObjectUtils.isNotEmpty(supplierBasicInfo)) {
            detailDTO.setSapSupplierCode(supplierBasicInfo.getSapSupplierCode());
        }
        detailDTO.setAdmissionCategories(categoryConvert.toListRespDTO(categories));
        if (CollectionUtils.isNotEmpty(attachList)) {
            List<AttachmentRecordPO> attachmentList = attachList.stream()
                    .filter(recordPO -> AttachmentBizTypeConstants.ADMISSION_INVITATION_SUPPLEMENTARY_FILE.equals(recordPO.getBusinessType())).collect(Collectors.toList());
            detailDTO.setSuppAttachments(attachmentMessageConvert.toList(attachmentList));
            List<AttachmentRecordPO> specifiedList = attachList.stream()
                    .filter(recordPO -> AttachmentBizTypeConstants.CUSTOMER_SPECIFIED_SUPPORTING_DOCUMENTS.equals(recordPO.getBusinessType())).collect(Collectors.toList());
            detailDTO.setCustomerDesignationProofAttachments(attachmentMessageConvert.toList(specifiedList));
        }
        // 设置调查表模版名称
        if (StringUtils.isBlank(detailDTO.getQuestionnaireTemplateCode())){
            return detailDTO;
        }
        QuestionnaireTemplatePO one = questionnaireTemplateMapperService.lambdaQuery().eq(QuestionnaireTemplatePO::getTemplateCode, detailDTO.getQuestionnaireTemplateCode()).one();
        if (ObjectUtils.isEmpty(one)){
            return detailDTO;
        }
        detailDTO.setQuestionnaireTemplateName(one.getTemplateDesc());
        return detailDTO;
    }



}
