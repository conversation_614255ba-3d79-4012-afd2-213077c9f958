package com.weifu.srm.supplier.service.biz.black;

import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.supplier.constants.MQSupplierTopicConstants;
import com.weifu.srm.supplier.manager.BlackListManager;
import com.weifu.srm.supplier.manager.SupplierGradeAdjustManager;
import com.weifu.srm.supplier.mq.SupplierBlackListMQ;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBlackListRecordDetailMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBlackListRecordMapperService;
import com.weifu.srm.supplier.repository.enums.CommonStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierBlackListStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierBlackListRecordDetailPO;
import com.weifu.srm.supplier.repository.po.SupplierBlackListRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 处理灰名单审批结果
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BlackListApplyHandleBiz {

    private final SupplierBlackListRecordMapperService blackListRecordMapperService;
    private final SupplierBlackListRecordDetailMapperService blackListRecordDetailMapperService;
    private final BlackListManager blackListManager;
    private final TransactionTemplate transactionTemplate;
    private final SupplierGradeAdjustManager supplierGradeAdjustManager;
    private final MqManager mqManager;

    public void handleBlackListApply(TicketStatusChangedMQ ticketInfo) {
        if (TicketStatusEnum.APPROVING.equalsCode(ticketInfo.getStatus())) {
            log.info("this audit is process  status={}", ticketInfo);
            blackListRecordDetailMapperService.lambdaUpdate()
                    .eq(SupplierBlackListRecordDetailPO::getValidNo, ticketInfo.getBusinessNo())
                    .set(SupplierBlackListRecordDetailPO::getValidTicketNo, ticketInfo.getTicketNo())
                    .update();
            return;
        }
        // 修改审批状态
        updateAuditStatus(ticketInfo);
        // 拒绝不进行任何操作
        if (TicketStatusEnum.REJECTED.equalsCode(ticketInfo.getStatus()) || TicketStatusEnum.CANCELED.equalsCode(ticketInfo.getStatus())) {
            return;
        }
        // 检查明细生效时间
        Date currentDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<SupplierBlackListRecordDetailPO> list = blackListRecordDetailMapperService.lambdaQuery()
                .eq(SupplierBlackListRecordDetailPO::getValidNo, ticketInfo.getBusinessNo())
                .le(SupplierBlackListRecordDetailPO::getValidTime, currentDate)
                .list();
        for (SupplierBlackListRecordDetailPO detailPO : list) {
            transactionTemplate.executeWithoutResult(status -> {
                // 修改明细
                blackListRecordDetailMapperService.lambdaUpdate()
                        .eq(SupplierBlackListRecordDetailPO::getId, detailPO.getId())
                        .set(SupplierBlackListRecordDetailPO::getStatus, SupplierBlackListStatusEnum.LIMITED.getCode())
                        .set(SupplierBlackListRecordDetailPO::getUpdateBy, detailPO.getUpdateBy())
                        .set(SupplierBlackListRecordDetailPO::getUpdateName, detailPO.getUpdateName())
                        .update();
                // 存入黑名单表
                blackListManager.saveBlackList(detailPO);
                // 检查修改供应商分级
                supplierGradeAdjustManager.updateSupplierGradeToNoGrade(detailPO.getValidNo(), detailPO.getSupplierCode(), "黑名单导致分级失效", ticketInfo.getOperateBy(), ticketInfo.getOperateName());

                SupplierBlackListMQ mq = new SupplierBlackListMQ();
                mq.setAddOrRemove(YesOrNoEnum.YES.getCode());
                mq.setBusinessNo(detailPO.getValidNo());
                mq.setSupplierCode(detailPO.getSupplierCode());
                mq.setOperationUser(ticketInfo.getOperateName());
                mq.setOperationUserId(ticketInfo.getOperateBy());
                mq.setOperationTime(ticketInfo.getUpdateTime());
                // 发送站内消息
                blackListManager.noticeBlackList(detailPO,mq);
                // 发送供应商黑名单生效MQ
                mqManager.sendTopic(MQSupplierTopicConstants.SUPPLIER_BLACK_LIST_TOPIC, JacksonUtil.bean2Json(mq));
                });
        }
    }

    private String getAuditStatus(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        switch (statusEnum) {
            case APPROVED:
                return CommonStatusEnum.AGREE_STATUS.getCode();
            case CANCELED:
                return CommonStatusEnum.DRAFT.getCode();
            case REJECTED:
                return CommonStatusEnum.REJECT_STATUS.getCode();
            case APPROVING:
                return CommonStatusEnum.AUDIT_STATUS.getCode();
            default:
                log.error("not support status={}", ticketInfo);
                throw new BizFailException("not support status");
        }
    }

    private String getBlackListDetailStatus(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        switch (statusEnum) {
            case APPROVED:
                return SupplierBlackListStatusEnum.AUDITED_WAITING.getCode();
            case REJECTED:
                return SupplierBlackListStatusEnum.LIMIT_AUDIT_REJECT.getCode();
            default:
                throw new BizFailException("not support status");
        }
    }

    private void updateAuditStatus(TicketStatusChangedMQ ticketInfo) {
        // 修改申请表
        blackListRecordMapperService.lambdaUpdate()
                .eq(SupplierBlackListRecordPO::getApplyNo, ticketInfo.getBusinessNo())
                .set(SupplierBlackListRecordPO::getStatus, getAuditStatus(ticketInfo))
                .set(SupplierBlackListRecordPO::getUpdateBy, ticketInfo.getOperateBy())
                .set(SupplierBlackListRecordPO::getUpdateName, ticketInfo.getOperateName())
                .update();
        if (TicketStatusEnum.CANCELED.equalsCode(ticketInfo.getStatus())) {
            // remove 数据
            blackListRecordDetailMapperService.lambdaUpdate()
                    .eq(SupplierBlackListRecordDetailPO::getValidNo, ticketInfo.getBusinessNo())
                    .set(SupplierBlackListRecordDetailPO::getIsDelete, YesOrNoEnum.YES.getCode())
                    .set(SupplierBlackListRecordDetailPO::getUpdateBy, ticketInfo.getOperateBy())
                    .set(SupplierBlackListRecordDetailPO::getUpdateName, ticketInfo.getOperateName())
                    .update();
            return;
        }
        // 修改明细表
        blackListRecordDetailMapperService.lambdaUpdate()
                .eq(SupplierBlackListRecordDetailPO::getValidNo, ticketInfo.getBusinessNo())
                .set(SupplierBlackListRecordDetailPO::getStatus, getBlackListDetailStatus(ticketInfo))
                .set(SupplierBlackListRecordDetailPO::getUpdateBy, ticketInfo.getOperateBy())
                .set(SupplierBlackListRecordDetailPO::getUpdateName, ticketInfo.getOperateName())
                .update();
    }


}
