package com.weifu.srm.supplier.service.biz.grey;

import com.weifu.srm.masterdata.response.CategoryResultDTO;
import com.weifu.srm.supplier.convert.GreyListConvert;
import com.weifu.srm.supplier.dto.GreyListItemDTO;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.manager.remote.user.SysDivisionManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryRelationshipMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGreyListMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGreyListRecordDetailMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierGreyListStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListRecordDetailPO;
import com.weifu.srm.supplier.response.grey.SupplierGreyListItemCategoryDivisionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class GreyListCategoryItemBiz {

    private final SupplierGreyListRecordDetailMapperService greyListRecordDetailMapperService;
    private final SupplierGreyListMapperService supplierGreyListMapperService;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final CategoryManager categoryManager;
    private final GreyListConvert greyListConvert;
    private final SysDivisionManager sysDivisionManager;

    public SupplierGreyListItemCategoryDivisionRespDTO queryGreyListItemCategoryBySupplierCode(String supplierCode) {
        List<SupplierGreyListItemCategoryDivisionRespDTO.CategoryDivision> categoryDivisions = categoryDivisions(supplierCode);
        List<SupplierGreyListItemCategoryDivisionRespDTO.CustomMap> categories = getSupplierLevel2Category(supplierCode);
        List<SupplierGreyListItemCategoryDivisionRespDTO.CustomMap> allDivisions = getAllDivisions();
        SupplierGreyListItemCategoryDivisionRespDTO result = new SupplierGreyListItemCategoryDivisionRespDTO();
        result.setSupplierCode(supplierCode);
        result.setCategoryDivisions(categoryDivisions);
        result.setCategories(categories);
        result.setDivisions(allDivisions);
        return result;
    }

    private List<SupplierGreyListItemCategoryDivisionRespDTO.CategoryDivision> categoryDivisions(String supplierCode) {
        // 查询申请记录表中的数据
        List<SupplierGreyListRecordDetailPO> detailList = greyListRecordDetailMapperService.lambdaQuery()
                .eq(SupplierGreyListRecordDetailPO::getSupplierCode, supplierCode)
                .in(SupplierGreyListRecordDetailPO::getStatus, SupplierGreyListStatusEnum.validateStatus)
                .list();
        // 查询灰名单中的数据
        List<SupplierGreyListPO> greyList = supplierGreyListMapperService.lambdaQuery()
                .eq(SupplierGreyListPO::getSupplierCode, supplierCode)
                .list();
        List<GreyListItemDTO> list = new ArrayList<>();
        List<GreyListItemDTO> greyListItemDTOListByDetail = greyListConvert.toGreyListItemDTOListByDetail(detailList);
        List<GreyListItemDTO> greyListItemDTOListByGreyList = greyListConvert.toGreyListItemDTOListByGreyList(greyList);
        list.addAll(greyListItemDTOListByGreyList);
        list.addAll(greyListItemDTOListByDetail);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 按照 格式进行封装
        Map<String, List<GreyListItemDTO>> categoryMapList = list.stream().collect(Collectors.groupingBy(GreyListItemDTO::getLevel2CategoryCode));
        List<CategoryResultDTO> categoryList = categoryManager.getCategoryList(new ArrayList<>(categoryMapList.keySet()));
        // 取品类中文名称
        Map<String, String> categoryMap = categoryList.stream().collect(Collectors.toMap(CategoryResultDTO::getCategoryCode, CategoryResultDTO::getCategoryName));
        return categoryMapList.entrySet().stream().map(entry -> {
            SupplierGreyListItemCategoryDivisionRespDTO.CategoryDivision categoryDivision = new SupplierGreyListItemCategoryDivisionRespDTO.CategoryDivision();
            categoryDivision.setCategoryCode(entry.getKey());
            categoryDivision.setCategoryName(categoryMap.get(entry.getKey()));
            categoryDivision.setDivisionIds(entry.getValue().stream().map(GreyListItemDTO::getDivisionId).distinct().collect(Collectors.toList()));
            return categoryDivision;
        }).collect(Collectors.toList());
    }

    private List<SupplierGreyListItemCategoryDivisionRespDTO.CustomMap> getSupplierLevel2Category(String supplierCode) {
        List<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOS = supplierCategoryRelationshipMapperService.queryBySapSupplierCodeList(Arrays.asList(supplierCode));
        if (CollectionUtils.isEmpty(supplierCategoryRelationshipPOS)) {
            return Collections.emptyList();
        }
        List<String> codes = supplierCategoryRelationshipPOS.stream().map(SupplierCategoryRelationshipPO::getCategoryCode).collect(Collectors.toList());
        return categoryManager.getCategoryList(codes)
                .stream()
                .map(categoryResultDTO -> {
                    SupplierGreyListItemCategoryDivisionRespDTO.CustomMap customMap = new SupplierGreyListItemCategoryDivisionRespDTO.CustomMap();
                    customMap.setId(categoryResultDTO.getParentCategoryCode());
                    customMap.setName(categoryResultDTO.getParentCategoryName());
                    return customMap;
                }).distinct().collect(Collectors.toList());
    }

    private List<SupplierGreyListItemCategoryDivisionRespDTO.CustomMap> getAllDivisions() {
       return sysDivisionManager.getAllDivisions().stream().map(sysDivisionRespDTO -> {
            SupplierGreyListItemCategoryDivisionRespDTO.CustomMap customMap = new SupplierGreyListItemCategoryDivisionRespDTO.CustomMap();
            customMap.setId(sysDivisionRespDTO.getDivisionId());
            customMap.setName(sysDivisionRespDTO.getDivisionName());
            return customMap;
        }).distinct().collect(Collectors.toList());
    }

}
