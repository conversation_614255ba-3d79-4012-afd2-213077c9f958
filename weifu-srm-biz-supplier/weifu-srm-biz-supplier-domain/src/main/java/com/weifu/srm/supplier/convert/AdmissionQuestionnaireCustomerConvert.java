package com.weifu.srm.supplier.convert;

import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireCustomerPO;
import com.weifu.srm.supplier.response.AdmissionQuestionnaireCustomerRespDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/18 13:12
 * @Description
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface AdmissionQuestionnaireCustomerConvert {
    List<AdmissionQuestionnaireCustomerRespDTO> toRespDto(List<AdmissionQuestionnaireCustomerPO> po);
}
