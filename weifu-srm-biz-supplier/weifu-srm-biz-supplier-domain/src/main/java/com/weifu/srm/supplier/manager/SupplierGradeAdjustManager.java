package com.weifu.srm.supplier.manager;

import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGradeAdjustApplyItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGradeAdjustApplyMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierGradeTypeEnum;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierGradeAdjustApplyItemPO;
import com.weifu.srm.supplier.repository.po.SupplierGradeAdjustApplyPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierGradeAdjustManager {

    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierGradeAdjustApplyMapperService gradeAdjustApplyMapperService;
    private final SupplierGradeAdjustApplyItemMapperService gradeAdjustApplyItemMapperService;

    @Transactional
    public void updateSupplierGradeToNoGrade(String businessNo,String supplierCode, String desc,Long userId, String userName){
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getSapSupplierCode, supplierCode)
                .one();
        if (StringUtils.isBlank(basicInfoPO.getSupplierClassification()) || SupplierGradeTypeEnum.NON_GRADE.getCode().equals(basicInfoPO.getSupplierClassification())){
            return;
        }
        supplierBasicInfoMapperService.lambdaUpdate()
                .eq(SupplierBasicInfoPO::getSapSupplierCode, supplierCode)
                .set(SupplierBasicInfoPO::getSupplierClassification, SupplierGradeTypeEnum.NON_GRADE.getCode())
                .update();
        // 插入分级记录
        SupplierGradeAdjustApplyPO adjustApplyPO = new SupplierGradeAdjustApplyPO();
        Date date = new Date();
        adjustApplyPO.setApplyRemark(desc);
        adjustApplyPO.setApplyDesc(desc);
        adjustApplyPO.setApplyNo(businessNo);
        adjustApplyPO.setApplyStatus("agree");
        adjustApplyPO.setPublishTime(new Date());
        BaseEntityUtil.setCommon(adjustApplyPO, userId,userName, date);
        gradeAdjustApplyMapperService.save(adjustApplyPO);
        SupplierGradeAdjustApplyItemPO itemPO = new SupplierGradeAdjustApplyItemPO();
        itemPO.setApplyNo(businessNo);
        itemPO.setSupplierCode(basicInfoPO.getSapSupplierCode());
        itemPO.setSupplierName(basicInfoPO.getSupplierName());
        itemPO.setSupplierGradeBefore(basicInfoPO.getSupplierClassification());
        itemPO.setSupplierGradeAfter(SupplierGradeTypeEnum.NON_GRADE.getCode());
        BaseEntityUtil.setCommon(itemPO, userId, userName, date);
        gradeAdjustApplyItemMapperService.save(itemPO);
    }
}
