package com.weifu.srm.supplier.service.biz.black;

import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.supplier.constants.MQSupplierTopicConstants;
import com.weifu.srm.supplier.manager.BlackListManager;
import com.weifu.srm.supplier.manager.SupplierGradeAdjustManager;
import com.weifu.srm.supplier.mq.SupplierBlackListMQ;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.enums.SupplierBlackListStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierBlackListRecordDetailPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 供应商黑名单生效
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ScheduleUpdateBlackListValidBiz {

    private final SupplierBlackListRecordDetailMapperService blackListRecordDetailMapperService;
    private final TransactionTemplate transactionTemplate;
    private final SupplierGradeAdjustManager supplierGradeAdjustManager;
    private final BlackListManager blackListManager;
    private final MqManager mqManager;

    public void processWaitValidData() {
        Date currentDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<SupplierBlackListRecordDetailPO> list = blackListRecordDetailMapperService.lambdaQuery()
                .eq(SupplierBlackListRecordDetailPO::getStatus, SupplierBlackListStatusEnum.AUDITED_WAITING.getCode())
                .le(SupplierBlackListRecordDetailPO::getValidTime, currentDate)
                .list();
        for (SupplierBlackListRecordDetailPO detailPO : list) {
            transactionTemplate.executeWithoutResult(status -> {
                // 修改明细状态
                blackListRecordDetailMapperService.lambdaUpdate()
                        .eq(SupplierBlackListRecordDetailPO::getId, detailPO.getId())
                        .set(SupplierBlackListRecordDetailPO::getStatus, SupplierBlackListStatusEnum.LIMITED.getCode())
                        .update();
                // 存入黑名单表
                blackListManager.saveBlackList(detailPO);
                // 检查修改供应商分级
                supplierGradeAdjustManager.updateSupplierGradeToNoGrade(detailPO.getValidNo(), detailPO.getSupplierCode(), "黑名单导致分级失效", -1L, "schedule");

                SupplierBlackListMQ mq = new SupplierBlackListMQ();
                mq.setAddOrRemove(YesOrNoEnum.YES.getCode());
                mq.setBusinessNo(detailPO.getValidNo());
                mq.setSupplierCode(detailPO.getSupplierCode());
                mq.setOperationUser("schedule");
                mq.setOperationUserId(-1L);
                mq.setOperationTime(new Date());
                // 发送站内消息
                blackListManager.noticeBlackList(detailPO,mq);
                // 发送供应商黑名单生效MQ
                mqManager.sendTopic(MQSupplierTopicConstants.SUPPLIER_BLACK_LIST_TOPIC, JacksonUtil.bean2Json(mq));
              });
        }
    }
}
