package com.weifu.srm.supplier.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.integration.api.CMSApi;
import com.weifu.srm.integration.request.cms.QueryCmsContractReqDTO;
import com.weifu.srm.integration.request.cms.SupplierItemReqDTO;
import com.weifu.srm.integration.response.cms.CmsContractInfoRespDTO;
import com.weifu.srm.integration.response.cms.ContractItemRespDTO;
import com.weifu.srm.user.api.SysDivisionApi;
import com.weifu.srm.user.response.division.SysDivisionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class CmsManager {
    private final CMSApi cmsApi;
    private final SysDivisionApi sysDivisionApi;

    /**
     * 根据供应商编码查询框架合同信息
     *
     * @param supplierCodes 供应商编码
     * @return K:供应商编码-事业部编码 V:{@link CmsContractInfoRespDTO}
     */
    public Map<String, List<ContractItemRespDTO>> queryCmsContractInfo(List<String> supplierCodes,String divisionCode) {
        if (CollUtil.isEmpty(supplierCodes) || CharSequenceUtil.isBlank(divisionCode)) return Map.of();

        ApiResponse<List<SysDivisionRespDTO>> divisionResp = sysDivisionApi.listByDivisionIds(Arrays.asList(divisionCode));
        String businessLineCode = null;
        if(Objects.nonNull(divisionResp) && CollUtil.isNotEmpty(divisionResp.getData())){
            List<SysDivisionRespDTO> divisionList = divisionResp.getData();
            businessLineCode = CollUtil.getFirst(divisionList).getBusinessLineCode();
        }

        QueryCmsContractReqDTO param = new QueryCmsContractReqDTO();
        param.setContractCreateFor("1");
        String finalBusinessLineCode = businessLineCode;
        param.setSupplierItems(supplierCodes.stream().map(v -> {
            SupplierItemReqDTO item = new SupplierItemReqDTO();
            item.setSupplierCode(v);
            item.setBusinessLineCode(finalBusinessLineCode);
            return item;
        }).collect(Collectors.toList()));

        log.info("查询CMS 合同 param:{}", JSONUtil.toJsonStr(param));
        ApiResponse<List<CmsContractInfoRespDTO>> resp = cmsApi.queryCmsContractInfo(param);
        log.info("查询CMS 合同 resp:{}", JSONUtil.toJsonStr(resp));
        if (Boolean.FALSE.equals(resp.getSucc())) {
            log.error("CMS合同查询接口异常 error:{}", resp.getMsg());
            return Map.of();
        }
        if (CollUtil.isEmpty(resp.getData())) {
            return Map.of();
        }
        List<CmsContractInfoRespDTO> list = resp.getData();

        Map<String, List<ContractItemRespDTO>> map = new HashMap<>();
        list.stream()
                .filter(v->CharSequenceUtil.equals(v.getBusinessLineCode(),finalBusinessLineCode))
                .forEach(v -> {
            map.put(CharSequenceUtil.format("{}-{}",v.getSupplierCode(),divisionCode), this.descByEndDate(v.getContractItems()));
        });
        return map;
    }

    private List<ContractItemRespDTO> descByEndDate(List<ContractItemRespDTO> contractItems) {
        if (CollUtil.isEmpty(contractItems)) return null;

        /*return contractItems.stream().sorted(Comparator.comparing(v -> {
            if (StringUtils.isBlank(v.getValidityPeriodEnd())) return -1;
            try {
                return Math.toIntExact(DateUtil.parse(v.getValidityPeriodEnd(), DatePattern.NORM_DATE_PATTERN).getTime());
            } catch (Exception e) {
                return -1;
            }
        }, Comparator.reverseOrder())).collect(Collectors.toList());*/
        return contractItems.stream()
            .sorted(Comparator.comparing(
                (ContractItemRespDTO item) -> {
                    try{
                        return LocalDate.parse(item.getValidityPeriodEnd(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    }catch(Exception e){
                        return LocalDate.MIN;
                    }
            }).reversed())  // 使用 reversed() 实现降序排序
            .collect(Collectors.toList());
    }
}
