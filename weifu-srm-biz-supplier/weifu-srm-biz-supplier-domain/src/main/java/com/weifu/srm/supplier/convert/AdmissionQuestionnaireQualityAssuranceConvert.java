package com.weifu.srm.supplier.convert;

import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireQualityAssurancePO;
import com.weifu.srm.supplier.response.AdmissionQuestionnaireQualityAssuranceRespDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/18 13:12
 * @Description
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface AdmissionQuestionnaireQualityAssuranceConvert {
    List<AdmissionQuestionnaireQualityAssuranceRespDTO> toRespDto(List<AdmissionQuestionnaireQualityAssurancePO> po);
}
