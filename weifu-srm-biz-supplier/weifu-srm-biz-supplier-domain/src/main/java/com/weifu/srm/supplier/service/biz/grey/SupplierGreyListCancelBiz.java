package com.weifu.srm.supplier.service.biz.grey;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGreyListRecordDetailMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGreyListRecordMapperService;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.SupplierBizEnum;
import com.weifu.srm.supplier.repository.enums.SupplierGreyListStatusEnum;
import com.weifu.srm.supplier.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListRecordDetailPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListRecordPO;
import com.weifu.srm.supplier.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyCancelReqDTO;
import com.weifu.srm.supplier.util.BusinessNoGenerateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class SupplierGreyListCancelBiz {

    private final IdService idService;
    private final SupplierGreyListRecordDetailMapperService greyListRecordDetailMapperService;
    private final SupplierGreyListRecordMapperService greyListRecordMapperService;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final MQServiceManager mqServiceManager;
    private final LocaleMessage localeMessage;


    @Transactional
    public String applyCancel(SupplierGreyListApplyCancelReqDTO req){
        // 检查Item
        checkItemsStatus(req.getGreyList());
        // 保存记录
        // 工单关联号
        String ticketRelationNo = BusinessNoGenerateUtil.getNextBusinessNo(SupplierBizEnum.GREY_LIST_APPLY_RELATION.getBizTypeCodee(), idService);
        saveRecord(req,ticketRelationNo);
        // 发送MQ
        CreateTicketMQ createTicketMQ = boxCreateTicketMQ(req, ticketRelationNo);
        mqServiceManager.sendMQ(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
        return ticketRelationNo;
    }

    private void saveRecord(SupplierGreyListApplyCancelReqDTO req, String ticketRelationNo){
        SupplierGreyListRecordPO recordPO = new SupplierGreyListRecordPO();
        recordPO.setApplyNo(ticketRelationNo);
        recordPO.setApplyType(YesOrNoEnum.NO.getCode());
        recordPO.setApplyDesc(req.getApplyDesc());
        recordPO.setRemark(req.getApplyRemark());
        BaseEntityUtil.setCommon(recordPO,req.getUserId(),req.getUserName(),null);
        // 保存记录
        greyListRecordMapperService.save(recordPO);
        // 修改明细
        greyListRecordDetailMapperService.lambdaUpdate()
                .in(SupplierGreyListRecordDetailPO::getId, req.getGreyList())
                .set(SupplierGreyListRecordDetailPO::getStatus, SupplierGreyListStatusEnum.LIMITED_CANCELING.getCode())
                .set(SupplierGreyListRecordDetailPO::getInvalidNo, ticketRelationNo)
                .update();
        List<AttachmentMessageReqDTO> applyAttachments = req.getApplyAttachments();
        List<AttachmentRecordPO> attachmentList = attachmentMessageConvert.toPOList(applyAttachments);
        for (AttachmentRecordPO attachmentRecordPO : attachmentList) {
            attachmentRecordPO.setBusinessNo(ticketRelationNo);
            attachmentRecordPO.setBusinessType(AttachmentBizTypeConstants.GRAY_LIST_APPLY_CANCEL_FILE);
            BaseEntityUtil.setCommon(attachmentRecordPO,req.getUserId(),req.getUserName(),null);
        }
        // 保存附件
        attachmentRecordMapperService.saveBatch(attachmentList);
    }

    private void checkItemsStatus(List<Integer> greyList) {
        List<SupplierGreyListRecordDetailPO> list = greyListRecordDetailMapperService.lambdaQuery()
                .in(SupplierGreyListRecordDetailPO::getId, greyList)
                .in(SupplierGreyListRecordDetailPO::getStatus, SupplierGreyListStatusEnum.cloudCancelStatus)
                .list();
        if (greyList.size() != list.size()) {
            log.error("exist processing greyList or completed greyList");
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.GREY_LIST_EXIST));
        }
    }


    /**
     * 发送工单创建MQ
     */
    private CreateTicketMQ boxCreateTicketMQ(SupplierGreyListApplyCancelReqDTO req, String ticketRelationNo) {
        CreateTicketMQ mq = new CreateTicketMQ();
        mq.setBusinessNo(ticketRelationNo);
        mq.setTicketType(TicketTypeEnum.GRAYLIST_RESTRICTION_RELEASE_APPLY.getCode());
        mq.setSubmitBy(req.getUserId());
        mq.setSubmitName(req.getUserName());
        mq.setSubmitDesc(req.getApplyDesc());
        mq.setSubmitRemark(req.getApplyRemark());
        return mq;
    }
}
