package com.weifu.srm.supplier.service.biz.grey;

import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.supplier.manager.GreyListManager;
import com.weifu.srm.supplier.manager.SupplierGradeAdjustManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGreyListRecordDetailMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGreyListRecordMapperService;
import com.weifu.srm.supplier.repository.enums.CommonStatusEnum;
import com.weifu.srm.supplier.repository.enums.NoticeTemplateEnum;
import com.weifu.srm.supplier.repository.enums.SupplierGreyListStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierGreyListRecordDetailPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 处理灰名单审批结果
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class GreyListApplyHandleBiz {

    private final SupplierGreyListRecordMapperService greyListRecordMapperService;
    private final SupplierGreyListRecordDetailMapperService greyListRecordDetailMapperService;
    private final GreyListManager greyListManager;
    private final TransactionTemplate transactionTemplate;
    private final SupplierGradeAdjustManager supplierGradeAdjustManager;

    public void handleGreyListApply(TicketStatusChangedMQ ticketInfo) {
        if (TicketStatusEnum.APPROVING.equalsCode(ticketInfo.getStatus())) {
            log.info("this audit is process  status={}", ticketInfo);
            greyListRecordDetailMapperService.lambdaUpdate()
                    .eq(SupplierGreyListRecordDetailPO::getValidNo, ticketInfo.getBusinessNo())
                    .set(SupplierGreyListRecordDetailPO::getValidTicketNo, ticketInfo.getTicketNo())
                    .update();
            return;
        }
        // 修改申请状态
        transactionTemplate.executeWithoutResult(status -> updateAuditStatus(ticketInfo));
        // 拒绝/关闭 不进行任何操作
        if (TicketStatusEnum.REJECTED.equalsCode(ticketInfo.getStatus()) || TicketStatusEnum.CANCELED.equalsCode(ticketInfo.getStatus())) {
            return;
        }
        // 检查明细生效时间
        Date currentDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<SupplierGreyListRecordDetailPO> list = greyListRecordDetailMapperService.lambdaQuery()
                .eq(SupplierGreyListRecordDetailPO::getValidNo, ticketInfo.getBusinessNo())
                .eq(SupplierGreyListRecordDetailPO::getStatus, SupplierGreyListStatusEnum.AUDITED_WAITING.getCode())
                .le(SupplierGreyListRecordDetailPO::getValidTime, currentDate)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (SupplierGreyListRecordDetailPO detailPO : list) {
            transactionTemplate.executeWithoutResult(status -> {
                greyListRecordDetailMapperService.lambdaUpdate()
                        .eq(SupplierGreyListRecordDetailPO::getId, detailPO.getId())
                        .set(SupplierGreyListRecordDetailPO::getStatus, SupplierGreyListStatusEnum.LIMITED.getCode())
                        .update();
                // 存入灰名单表
                greyListManager.addGreyList(detailPO);
                // 检查修改供应商分级
                supplierGradeAdjustManager.updateSupplierGradeToNoGrade(detailPO.getValidNo(), detailPO.getSupplierCode(), "灰名单导致分级失效", ticketInfo.getOperateBy(), ticketInfo.getOperateName());
                // 发送通知, Yes表示新增，No 表示移除
                greyListManager.sendGreyListNotice(detailPO,YesOrNoEnum.YES.getCode());
            });
        }
    }

    private String getAuditStatus(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        switch (statusEnum) {
            case APPROVED:
                return CommonStatusEnum.AGREE_STATUS.getCode();
            case CANCELED:
                return CommonStatusEnum.DRAFT.getCode();
            case REJECTED:
                return CommonStatusEnum.REJECT_STATUS.getCode();
            default:
                log.error("not support status={}", ticketInfo);
                throw new BizFailException("not support status");
        }
    }

    private String getGreyListDetailStatus(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        switch (statusEnum) {
            case APPROVED:
                return SupplierGreyListStatusEnum.AUDITED_WAITING.getCode();
            case CANCELED:
                return SupplierGreyListStatusEnum.DRAFT.getCode();
            case REJECTED:
                return SupplierGreyListStatusEnum.LIMIT_AUDIT_REJECT.getCode();
            default:
                throw new BizFailException("not support status");
        }
    }

    private void updateAuditStatus(TicketStatusChangedMQ ticketInfo) {
        // 修改申请表
        greyListRecordMapperService.lambdaUpdate()
                .eq(SupplierGreyListRecordPO::getApplyNo, ticketInfo.getBusinessNo())
                .eq(SupplierGreyListRecordPO::getStatus, CommonStatusEnum.AUDIT_STATUS.getCode())
                .set(SupplierGreyListRecordPO::getStatus, getAuditStatus(ticketInfo))
                .set(SupplierGreyListRecordPO::getUpdateBy, ticketInfo.getOperateBy())
                .set(SupplierGreyListRecordPO::getUpdateName, ticketInfo.getOperateName())
                .update();
        if (TicketStatusEnum.CANCELED.equalsCode(ticketInfo.getStatus())) {
            // remove 数据
            greyListRecordDetailMapperService.lambdaUpdate()
                    .eq(SupplierGreyListRecordDetailPO::getValidNo, ticketInfo.getBusinessNo())
                    .set(SupplierGreyListRecordDetailPO::getIsDelete, YesOrNoEnum.YES.getCode())
                    .set(SupplierGreyListRecordDetailPO::getUpdateBy, ticketInfo.getOperateBy())
                    .set(SupplierGreyListRecordDetailPO::getUpdateName, ticketInfo.getOperateName())
                    .update();
            return;
        }
        // 修改明细表
        greyListRecordDetailMapperService.lambdaUpdate()
                .eq(SupplierGreyListRecordDetailPO::getValidNo, ticketInfo.getBusinessNo())
                .eq(SupplierGreyListRecordDetailPO::getStatus, SupplierGreyListStatusEnum.LIMIT_AUDIT.getCode())
                .set(SupplierGreyListRecordDetailPO::getStatus, getGreyListDetailStatus(ticketInfo))
                .set(SupplierGreyListRecordDetailPO::getUpdateBy, ticketInfo.getOperateBy())
                .set(SupplierGreyListRecordDetailPO::getUpdateName, ticketInfo.getOperateName())
                .update();
    }


}
