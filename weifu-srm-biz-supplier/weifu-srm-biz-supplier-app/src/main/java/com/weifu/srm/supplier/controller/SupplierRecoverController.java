package com.weifu.srm.supplier.controller;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.api.SupplierRecoverApi;
import com.weifu.srm.supplier.request.recover.SupplierRecoverApplyListReqDTO;
import com.weifu.srm.supplier.request.recover.SupplierRecoverApplyReqDTO;
import com.weifu.srm.supplier.response.recover.SupplierRecoverRecordDetailRespDTO;
import com.weifu.srm.supplier.response.recover.SupplierRecoverRecordListRespDTO;
import com.weifu.srm.supplier.service.SupplierRecoverService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "供应商恢复管理")
@RestController
@RequiredArgsConstructor
public class SupplierRecoverController implements SupplierRecoverApi {

    private final SupplierRecoverService supplierRecoverService;

    @Override
    public ApiResponse<PageResponse<SupplierRecoverRecordListRespDTO>> queryListPage(SupplierRecoverApplyListReqDTO req) {
        PageResponse<SupplierRecoverRecordListRespDTO> result = supplierRecoverService.queryListPage(req);
        return ApiResponse.success(result);
    }

    @Override
    public ApiResponse<String> applyRecover(SupplierRecoverApplyReqDTO req) {
        String applyNo = supplierRecoverService.applyRecover(req);
        return ApiResponse.success(applyNo);
    }

    @Override
    public ApiResponse<SupplierRecoverRecordDetailRespDTO> applyDetail(String applyNo) {
        SupplierRecoverRecordDetailRespDTO result = supplierRecoverService.applyDetail(applyNo);
        return ApiResponse.success(result);
    }
}
