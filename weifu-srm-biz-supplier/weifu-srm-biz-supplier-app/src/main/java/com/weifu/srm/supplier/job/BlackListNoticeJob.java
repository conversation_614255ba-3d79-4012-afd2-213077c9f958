package com.weifu.srm.supplier.job;

import com.weifu.srm.supplier.service.SupplierBlackListService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class BlackListNoticeJob {

    private final SupplierBlackListService supplierBlackListService;

    /**
     * 黑名单生效
     */
    @XxlJob("SupplierBlackListOutNotice-Supplier")
    public void runTask() {
        XxlJobHelper.log("SupplierBlackListOutNotice#runTask()");
        try {
            supplierBlackListService.scheduleCheckBlackListSuggestOutTime();
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            log.error("SupplierBlackListOutNotice任务执行异常", e);
            XxlJobHelper.handleFail("异常信息：" + e.getMessage());
        }
    }

}
