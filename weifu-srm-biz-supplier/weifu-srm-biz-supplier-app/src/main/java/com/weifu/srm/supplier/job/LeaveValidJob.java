package com.weifu.srm.supplier.job;

import com.weifu.srm.supplier.service.SupplierLeaveService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class LeaveValidJob {

    private final SupplierLeaveService supplierLeaveService;

    /**
     * 供应商退出待生效
     */
    @XxlJob("supplierLeaveWaitValid-Supplier")
    public void runTask() {
        XxlJobHelper.log("supplierLeaveWaitValid#runTask()");
        try {
            supplierLeaveService.scheduleCheckWaitLeave();
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            log.error("supplierLeaveWaitValid任务执行异常", e);
            XxlJobHelper.handleFail("异常信息：" + e.getMessage());
        }
    }

}
