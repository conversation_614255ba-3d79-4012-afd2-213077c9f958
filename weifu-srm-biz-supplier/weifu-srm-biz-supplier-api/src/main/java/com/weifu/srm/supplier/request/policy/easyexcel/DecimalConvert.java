package com.weifu.srm.supplier.request.policy.easyexcel;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.supplier.constants.LanguageConstant;
import com.weifu.srm.supplier.utils.MessageUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Objects;


public class DecimalConvert extends BaseConvert implements Converter<BigDecimal> {
    @Override
    public BigDecimal convertToJavaData(ReadConverterContext<?> context) throws Exception {
        Integer rowIndex = context.getReadCellData().getRowIndex();
        ReadCellData<?> readCellData = context.getReadCellData();
        String value = getValueByNumber(readCellData);
        if (CharSequenceUtil.isEmpty(value)) return null;

        Field field = context.getContentProperty().getField();
        String name = "";
        if (Objects.nonNull(field)) {
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (Objects.nonNull(annotation)) {
                String[] values = annotation.value();
                if (null != values) name = values[0];
            }
        }

        if (!NumberUtil.isNumber(value)) {
            if (CharSequenceUtil.isNotEmpty(name)) {
                throw new BizFailException(MessageUtils.message(LanguageConstant.POLICY_VERIFY_IS_NUMBER,rowIndex,name));
            } else {
                throw new BizFailException(MessageUtils.message(LanguageConstant.POLICY_VERIFY_IS_NUMBER1, rowIndex, value));
            }
        }
        return new BigDecimal(value);
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<BigDecimal> context) throws Exception {
        BigDecimal javaValue = context.getValue();
        return new WriteCellData<>(NumberUtil.toStr(javaValue,""));
    }
}
