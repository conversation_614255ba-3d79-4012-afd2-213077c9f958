package com.weifu.srm.supplier.request.leave;

import com.weifu.srm.supplier.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@NoArgsConstructor
public class SupplierLeaveExistReqDTO {

    @NotNull(message = "申请描述不能为空")
    @ApiModelProperty(value = "申请描述")
    @Length(max = 225,message = "申请描述不能超过225个字符")
    private String applyDesc;
    @ApiModelProperty("申请附件")
    @NotNull(message = "申请附件不能为空")
    private List<AttachmentMessageReqDTO> applyAttachments;
    @ApiModelProperty(value = "申请备注")
    @Length(max = 225,message = "申请备注不能超过225个字符")
    private String applyRemark;
    @NotNull(message = "申请条目不能为空")
    @ApiModelProperty(value = "申请条目")
    @Size(min = 1,message = "待退出明细不能为空")
    private List<Integer> leaveList;
    /**操作人Id*/
    private Long userId;
    /**操作人姓名*/
    private String userName;

}
