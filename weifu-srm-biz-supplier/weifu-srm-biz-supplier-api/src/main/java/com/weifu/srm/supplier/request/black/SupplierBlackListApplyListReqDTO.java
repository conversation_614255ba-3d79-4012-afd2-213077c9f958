package com.weifu.srm.supplier.request.black;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class SupplierBlackListApplyListReqDTO extends PageRequest {
    @ApiModelProperty("限制编号")
    private String limitNo;
    /** 供应商唯一编码 */
    @ApiModelProperty(value = "供应商唯一编码",notes = "")
    private List<String> supplierCodes ;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName ;
    @ApiModelProperty(value = "供应商状态")
    private List<String> supplierStatus ;
    /** 黑名单状态（） */
    @ApiModelProperty(value = "黑名单状态（）",notes = "")
    private List<String> status ;
    /** 黑名单申请工单号 */
    @ApiModelProperty(value = "黑名单申请工单号",notes = "")
    private String validTicketNo ;
    /** 生效时间 */
    @ApiModelProperty(value = "生效时间-开始",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date startValidTime ;
    /** 生效时间 */
    @ApiModelProperty(value = "生效时间-截止",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date endValidTime ;
    /** 黑名单解除申请工单号 */
    @ApiModelProperty(value = "黑名单解除申请工单号",notes = "")
    private String invalidTicketNo ;
    /** 黑名单解除生效时间 */
    @ApiModelProperty(value = "黑名单解除生效时间-开始",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date startInvalidTime ;
    /** 黑名单解除生效时间 */
    @ApiModelProperty(value = "黑名单解除生效时间-截止",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date endInvalidTime ;
    /** 黑名单申请创建时间 */
    @ApiModelProperty(value = "黑名单申请创建时间-开始",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date startCreateTime ;
    /** 黑名单申请创建时间 */
    @ApiModelProperty(value = "黑名单申请创建时间-截止",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date endCreateTime ;

    /** 建议退出时间 */
    @ApiModelProperty(value = "建议退出时间-开始",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date startOutTime ;
    /** 建议退出时间 */
    @ApiModelProperty(value = "建议退出时间-截止",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date endOutTime ;

}
