package com.weifu.srm.supplier.utils;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class ValidationUtils {
    /**
     * javax 校验数据并获取数据中所有校验信息
     *
     * @param data  数据 为空不校验
     * @param group 校验分组
     * @return 返回空则没有校验不同过的信息
     */
    public static <T> List<String> validation(T data, Class<?>... group) {
        if (Objects.isNull(data)) return List.of();
        // 创建Validator
        ValidatorFactory factory = null;
        try {
            factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();

            // 验证对象
            Set<ConstraintViolation<T>> constraintViolations = null;
            if (null != group) {
                constraintViolations = validator.validate(data, group);
            } else {
                constraintViolations = validator.validate(data);
            }

            // 输出验证失败的信息
            if (null != constraintViolations) {
                return constraintViolations.stream()
                        .map(ConstraintViolation::getMessage)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("校验数据失败 error:", e);
        } finally {
            // 关闭ValidatorFactory
            if (Objects.nonNull(factory)) factory.close();
        }

        return List.of();
    }


    /**
     * javax 校验数据并获取第一个错误信息
     *
     * @param data  数据 为空不校验
     * @param group 校验分组
     * @return 返回空则没有校验不通过的信息
     */
    public static <T> String validationOne(T data, Class<?>... group) {
        List<String> message = validation(data, group);
        if (CollUtil.isNotEmpty(message)) {
            return message.get(0);
        }
        return null;
    }
}
