package com.weifu.srm.supplier.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.repository.atomicservice.AdmissionQuestionnairePersonnelMapperService;
import com.weifu.srm.supplier.repository.mapper.AdmissionQuestionnairePersonnelMapper;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnairePersonnelPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:59
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class AdmissionQuestionnairePersonnelMapperServiceImpl extends ServiceImpl<AdmissionQuestionnairePersonnelMapper, AdmissionQuestionnairePersonnelPO>
        implements AdmissionQuestionnairePersonnelMapperService {
    private final AdmissionQuestionnairePersonnelMapper personnelMapper;
    @Override
    public Boolean removeByAdmissionNo(String admissionNo) {
        return personnelMapper.deleteByAdmissionNo(admissionNo);
    }

    @Override
    public List<AdmissionQuestionnairePersonnelPO> findByAdmissionNo(String admissionNo) {
        return this.list(new LambdaQueryWrapper<AdmissionQuestionnairePersonnelPO>()
                .eq(AdmissionQuestionnairePersonnelPO::getAdmissionNo,admissionNo)
                .eq(AdmissionQuestionnairePersonnelPO::getIsDelete, YesOrNoEnum.NO.getCode()));
    }
}
