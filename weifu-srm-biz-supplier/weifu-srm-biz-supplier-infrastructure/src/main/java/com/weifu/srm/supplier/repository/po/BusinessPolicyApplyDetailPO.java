package com.weifu.srm.supplier.repository.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;

import com.weifu.srm.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 商务政策列表
 *
 * @TableName business_policy_apply_detail
 */
@TableName(value = "business_policy_apply_detail")
@Data
public class BusinessPolicyApplyDetailPO extends BaseEntity implements Serializable {
    /**
     * 申请主表编号
     */
    @TableField(value = "apply_no")
    private String applyNo;

    /**
     *商务政策编号
     */
    @TableField(value = "policy_no")
    private String policyNo;

    /**
     * 变更类型
     * NEW_CREATE:新增
     * ADJUST:调整
     *
     */
    @TableField(value = "type")
    private String type;

    /**
     * 状态
     * DRAFT 草稿
     * APPROVING 审核中
     * APPROVED 审核通过
     * REJECTED 审核拒绝
     */
    @TableField(value = "status")
    private String status;

    /**

    /**
     *商务政策列表ID
     */
    @TableField(value = "policy_list_id")
    private Long policyListId;
    /**
     * 供应商编码
     */
    @TableField(value = "sap_supplier_code")
    private String sapSupplierCode;
    /**
     * 供应商名称
     */
    @TableField(value = "supplier_name")
    private String supplierName;
    /**
     * 事业部编码
     */
    @TableField(value = "division_code")
    private String divisionCode;
    /**
     * 事业部名称
     */
    @TableField(value = "division_name")
    private String divisionName;
    /**
     * 业务小类编码
     */
    @TableField(value = "business_subclass_code")
    private String businessSubclassCode;
    /**
     * 业务小类名称
     */
    @TableField(value = "business_subclass_name")
    private String businessSubclassName;
    /**
     * 业务大类编码
     */
    @TableField(value = "business_category_code")
    private String businessCategoryCode;
    /**
     * 业务大类名称
     */
    @TableField(value = "business_category_name")
    private String businessCategoryName;
    /**
     * 付款周期
     */
    @TableField(value = "payment_cycle")
    private Long paymentCycle;
    /**
     * 付款条款类型
     */
    @TableField(value = "payment_terms_type")
    private String paymentTermsType;
    /**
     * 付款基准日期类型
     * 字典 PAYMENT_BASE_DATE_TYPE
     */
    @TableField(value = "payment_base_date_type",updateStrategy = FieldStrategy.IGNORED)
    private String paymentBaseDateType;
    /**
     * 质保等抵押金
     */
    @TableField(value = "collateral_amount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal collateralAmount;
    /**
     * 票据（%）
     */
    @TableField(value = "bill",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal bill;
    /**
     * 银企直连（%）
     */
    @TableField(value = "bank_enterprise_link",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal bankEnterpriseLink;
    /**
     * 应收票据-银票（%）
     */
    @TableField(value = "bill_receivable",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal billReceivable;
    /**
     * 其它（%）
     */
    @TableField(value = "other",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal other;
    /**
     * 币种
     */
    @TableField(value = "currency",updateStrategy = FieldStrategy.IGNORED)
    private String currency;
    /**
     * 是否启用
     */
    @TableField(value = "is_enable")
    private Boolean isEnable;

    @ApiModelProperty(value = "付款周期(调整后)")
    @TableField(value = "payment_cycle_after")
    private Long paymentCycleAfter;

    @ApiModelProperty(value = "付款基准日期类型(调整后)")
    @TableField(value = "payment_base_date_type_after",updateStrategy = FieldStrategy.IGNORED)
    private String paymentBaseDateTypeAfter;

    @ApiModelProperty(value = "质保等抵押金(调整后)")
    @TableField(value = "collateral_amount_after",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal collateralAmountAfter;

    @ApiModelProperty(value = "票据（%）(调整后)")
    @TableField(value = "bill_after",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal billAfter;

    @ApiModelProperty(value = "银企直连（%）(调整后)")
    @TableField(value = "bank_enterprise_link_after",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal bankEnterpriseLinkAfter;

    @ApiModelProperty(value = "应收票据-银票（%）(调整后)")
    @TableField(value = "bill_receivable_after",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal billReceivableAfter;

    @ApiModelProperty(value = "其它（%）(调整后)")
    @TableField(value = "other_after",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal otherAfter;

    @ApiModelProperty(value = "币种(调整后)")
    @TableField(value = "currency_after",updateStrategy = FieldStrategy.IGNORED)
    private String currencyAfter;

    @ApiModelProperty(value = "是否启用(调整后)")
    @TableField(value = "is_enable_after")
    private Boolean isEnableAfter;

    @TableField("is_delete")
    @TableLogic(value = "0",delval = "1")
    private Integer isDelete;
}