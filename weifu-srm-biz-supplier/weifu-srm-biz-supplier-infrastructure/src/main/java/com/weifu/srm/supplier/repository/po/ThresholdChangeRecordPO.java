package com.weifu.srm.supplier.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName(value="threshold_change_record")
public class ThresholdChangeRecordPO extends BaseEntity implements Serializable {

    /** 年度采购金额（元）-旧 */

    private BigDecimal annualPurchaseOldAmt ;
    /** 年度采购金额（元）-新 */

    private BigDecimal annualPurchaseNewAmt ;
    /** 年度采购次数-旧 */

    private Long annualPurchaseOldCnt ;
    /** 年度采购次数-新 */

    private Long annualPurchaseNewCnt ;
    /** 最大单次采购金额（元）_旧 */

    private BigDecimal mastPurchaseOldAmt ;
    /** 最大单次采购金额（元）_新 */

    private BigDecimal mastPurchaseNewAmt ;
    /** 交易有效周期（月）_旧 */

    private Long transactionOldPeriod ;
    /** 交易有效周期（月）_新 */

    private Long transactionNewPeriod ;
}
