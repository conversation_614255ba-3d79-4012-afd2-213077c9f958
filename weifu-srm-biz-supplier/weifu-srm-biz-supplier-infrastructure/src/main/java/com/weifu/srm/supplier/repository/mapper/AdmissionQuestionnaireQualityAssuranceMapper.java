package com.weifu.srm.supplier.repository.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireQualityAssurancePO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:41
 * @Description
 * @Version 1.0
 */
@Mapper
public interface AdmissionQuestionnaireQualityAssuranceMapper extends BaseMapper<AdmissionQuestionnaireQualityAssurancePO> {
    @Delete("delete from admission_questionnaire_quality_assurance where admission_no = #{admissionNo} ")
    Boolean deleteByAdmissionNo(String admissionNo);
}
