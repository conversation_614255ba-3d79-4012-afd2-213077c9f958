package com.weifu.srm.supplier.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import lombok.Data;

@Data
@TableName("supplier_category_adjust_apply_item")
public class SupplierCategoryAdjustApplyItemPO extends BaseEntity {

    /** 申请编号 */
    private String applyNo;
    /** 供应商编码 */
    private String sapSupplierCode;
    /** 一级品类编码 */
    private String oneLevelCategoryCode;
    /** 一级品类名称 */
    private String oneLevelCategoryName;
    /** 一级品类名称（英文） */
    private String oneLevelCategoryNameEn;
    /** 二级品类编码 */
    private String twoLevelCategoryCode;
    /** 二级品类名称 */
    private String twoLevelCategoryName;
    /** 二级品类名称（英文） */
    private String twoLevelCategoryNameEn;
    /** 三级品类编码 */
    private String threeLevelCategoryCode;
    /** 三级品类名称 */
    private String threeLevelCategoryName;
    /** 三级品类名称（英文） */
    private String threeLevelCategoryNameEn;

}
