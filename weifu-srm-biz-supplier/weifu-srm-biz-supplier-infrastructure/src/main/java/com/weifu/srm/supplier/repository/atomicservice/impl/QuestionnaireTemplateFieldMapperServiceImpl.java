package com.weifu.srm.supplier.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateFieldMapperService;
import com.weifu.srm.supplier.repository.mapper.QuestionnaireTemplateFieldMapper;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplateFieldPO;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplateFieldSamplePO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * <AUTHOR>
 * @Date 2024/7/05 14:38
 * @Description
 * @Version 1.0
 */
@RequiredArgsConstructor
@Service
public class QuestionnaireTemplateFieldMapperServiceImpl
        extends ServiceImpl<QuestionnaireTemplateFieldMapper, QuestionnaireTemplateFieldPO>
        implements QuestionnaireTemplateFieldMapperService {
    private final QuestionnaireTemplateFieldMapper questionnaireTemplateFieldMapper;


    @Override
    public List<QuestionnaireTemplateFieldPO> queryTemplateFieldListByCode(String templateCode) {
        LambdaQueryWrapper<QuestionnaireTemplateFieldPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionnaireTemplateFieldPO::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.eq(QuestionnaireTemplateFieldPO::getTemplateCode, templateCode);
        return this.list(queryWrapper);
    }

    @Override
    public Boolean deleteByTemplateCode(String templateCode) {
        return questionnaireTemplateFieldMapper.deleteByTemplateCode(templateCode);
    }

    @Override
    public Integer updateTemplateField(Wrapper<QuestionnaireTemplateFieldPO> wrapper) {
        return questionnaireTemplateFieldMapper.update(null, wrapper);
    }
}
