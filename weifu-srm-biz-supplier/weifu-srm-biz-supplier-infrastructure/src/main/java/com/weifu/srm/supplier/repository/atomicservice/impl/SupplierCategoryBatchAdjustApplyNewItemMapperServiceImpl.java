package com.weifu.srm.supplier.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryBatchAdjustApplyNewItemMapperService;
import com.weifu.srm.supplier.repository.mapper.SupplierCategoryBatchAdjustApplyNewItemMapper;
import com.weifu.srm.supplier.repository.po.SupplierCategoryBatchAdjustApplyNewItemPO;
import org.springframework.stereotype.Service;

@Service
public class SupplierCategoryBatchAdjustApplyNewItemMapperServiceImpl extends ServiceImpl<SupplierCategoryBatchAdjustApplyNewItemMapper, SupplierCategoryBatchAdjustApplyNewItemPO> implements SupplierCategoryBatchAdjustApplyNewItemMapperService {
}
