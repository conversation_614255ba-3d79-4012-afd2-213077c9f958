package com.weifu.srm.supplier.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("supplier_category_batch_adjust_apply")
public class SupplierCategoryBatchAdjustApplyPO extends BaseEntity {

    /** 申请编号 */
    private String applyNo;
    /** 申请状态 */
    private String applyStatus;
    /** 申请人 */
    private Long applyBy;
    /** 申请人姓名 */
    private String applyName;
    /** 申请说明 */
    private String applyDesc;
    /** 申请时间 */
    private Date applyTime;

}
