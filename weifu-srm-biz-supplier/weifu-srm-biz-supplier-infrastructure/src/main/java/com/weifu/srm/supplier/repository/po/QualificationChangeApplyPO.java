package com.weifu.srm.supplier.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "资质变更申请表",description = "")
@TableName("qualification_change_apply")
@Data
public class QualificationChangeApplyPO extends BaseEntity implements Serializable{

    /**
     * 资质变更编号
     */
    @ApiModelProperty(name = "资质变更编号")
    private String qualificationChangeNo;
    /**
     * 供应商编码
     */
    @ApiModelProperty(name = "供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "供应商名称")
    private String supplierName;
    /**
     * 变更类型（基本信息变更; 财务信息变更; 质量资质证书添加; 非质量资质证书添加; 质量资质证书删除; 非质量资质证书删除; 关联方信息变更）
     */
    @ApiModelProperty(name = "变更类型（基本信息变更; 财务信息变更; 质量资质证书添加; 非质量资质证书添加; 质量资质证书删除; 非质量资质证书删除; 关联方信息变更）")
    private String changeType;
    /**
     * 变更状态（已提交；审批通过；审批拒绝；已撤回）
     */
    @ApiModelProperty(name = "变更状态（已提交；审批通过；审批拒绝；已撤回）")
    private String changeStatus;
    /**
     * 工单编号
     */
    @ApiModelProperty(name = "工单编号")
    private String ticketNo;
    /**
     * 审批时间
     */
    @ApiModelProperty(name = "审批时间")
    private Date approveTime;
    /**
     * 审批意见
     */
    @ApiModelProperty(name = "审批意见")
    private String approveOpinion;
    /**
     * 导入sap是否成功
     */
    @ApiModelProperty(name = "导入sap是否成功")
    private Integer sapImport;
    /**
     * 导入SAP失败时，记录的错误信息
     */
    @ApiModelProperty(name = "错误信息")
    private String errorMessage;
    /**
     * 提交来源
     */
    @ApiModelProperty(name = "提交来源")
    private String submitSource;

}
