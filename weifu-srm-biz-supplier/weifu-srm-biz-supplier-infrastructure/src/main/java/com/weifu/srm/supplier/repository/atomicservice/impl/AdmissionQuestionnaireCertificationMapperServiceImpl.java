package com.weifu.srm.supplier.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.repository.atomicservice.AdmissionQuestionnaireCertificationMapperService;
import com.weifu.srm.supplier.repository.mapper.AdmissionQuestionnaireCertificationMapper;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireCertificationPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:47
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class AdmissionQuestionnaireCertificationMapperServiceImpl extends ServiceImpl<AdmissionQuestionnaireCertificationMapper, AdmissionQuestionnaireCertificationPO>
        implements AdmissionQuestionnaireCertificationMapperService {
    private final AdmissionQuestionnaireCertificationMapper certificationMapper;
    @Override
    public Boolean removeByAdmissionNo(String admissionNo) {
        return certificationMapper.deleteByAdmissionNo(admissionNo);
    }

    @Override
    public List<AdmissionQuestionnaireCertificationPO> findByAdmissionNo(String admissionNo) {
        return this.list(new LambdaQueryWrapper<AdmissionQuestionnaireCertificationPO>()
                .eq(AdmissionQuestionnaireCertificationPO::getAdmissionNo, admissionNo)
                .eq(AdmissionQuestionnaireCertificationPO::getIsDelete, YesOrNoEnum.NO.getCode()));
    }
}
