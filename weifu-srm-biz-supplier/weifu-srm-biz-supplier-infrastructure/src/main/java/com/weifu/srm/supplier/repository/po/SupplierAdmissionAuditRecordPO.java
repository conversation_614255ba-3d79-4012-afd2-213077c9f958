package com.weifu.srm.supplier.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "supplier_admission_audit_record")
public class SupplierAdmissionAuditRecordPO extends BaseEntity {
    /** 准入编号 */
    @ApiModelProperty(name = "准入编号",notes = "")
    private String admissionNo ;
    /** 工单号 */
    @ApiModelProperty(name = "工单号",notes = "")
    private String ticketNo ;
    /** 批产件准入审批中的 五大类评审结果-经营管理 */
    @ApiModelProperty(name = "五大类评审结果-经营管理",notes = "")
    private String operatingManagementAbility ;
    /** 批产件准入审批中的 五大类评审结果-研发能力 */
    @ApiModelProperty(name = "五大类评审结果-研发能力",notes = "")
    private String researchDevelopmentAbility ;
    /** 批产件准入审批中的 五大类评审结果-制造能力 */
    @ApiModelProperty(name = "五大类评审结果-经营管理",notes = "")
    private String manufacturingAbility ;
    /** 批产件准入审批中的 五大类评审结果-交付能力 */
    @ApiModelProperty(name = "五大类评审结果-交付能力",notes = "")
    private String deliverAbility ;
    /** 批产件准入审批中的 五大类评审结果-经营管理 */
    @ApiModelProperty(name = "五大类评审结果-质量能力",notes = "")
    private String qualityAbility ;
    /** 批产件准入审批中的 五大类评审结果 */
    @ApiModelProperty(name = "五大类评审结果 0:完全准入 1: 条件准入 2: 拒绝准入",notes = "")
    private String appraisalResult ;
    /** 五大类评审意见 */
    @ApiModelProperty(name = "五大类评审意见",notes = "")
    private String appraisalDesc ;
}
