package com.weifu.srm.supplier.repository.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum SupplierUserRoleEnum {
    SUPPLIER_REGISTER_CONTACTS("SUPPLIER_REGISTER_CONTACTS", "Normal Supplier", "供应商注册联系人"),
    SUPPLIER_BUSINESS_CONTACTS("SUPPLIER_BUSINESS_CONTACTS", "Special Supplier", "供应商商务联系人"),
    SUPPLIER_FINANCIAL_CONTACTS("SUPPLIER_FINANCIAL_CONTACTS", "Customer Parts Supplier", "供应商财务联系人"),
    SUPPLIER_QUALITY_CONTACTS("SUPPLIER_QUALITY_CONTACTS", "Trade Service Supplier", "供应商质量联系人");

    private String code;

    private String englishName;

    private String chineseName;


    private SupplierUserRoleEnum(String code, String englishName, String chineseName) {
        this.code = code;
        this.englishName = englishName;
        this.chineseName = chineseName;
    }

    private static final Map<String, SupplierUserRoleEnum> lookup = new HashMap<>();

    static {
        for (SupplierUserRoleEnum mode : values()) {
            lookup.put(mode.getCode(), mode);
        }
    }
    public static SupplierUserRoleEnum getByCode(String code) {
        return lookup.get(code);
    }
    public static String getChineseName(String code) {
        return lookup.get(code).chineseName;
    }
}
