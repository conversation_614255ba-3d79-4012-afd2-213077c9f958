package com.weifu.srm.supplier.repository.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.repository.po.SupplierGreyListPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListRecordDetailPO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyReqDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SupplierGreyListMapper extends BaseMapper<SupplierGreyListPO> {

    List<SupplierGreyListPO> searchGreyList(@Param("list")List<SupplierGreyListApplyReqDTO.GreyListDetail> list);

    void removeGreyList(@Param("list")List<SupplierGreyListRecordDetailPO> list);

    void removeBySupplierCode(@Param("supplierCode") String supplierCode);
}
