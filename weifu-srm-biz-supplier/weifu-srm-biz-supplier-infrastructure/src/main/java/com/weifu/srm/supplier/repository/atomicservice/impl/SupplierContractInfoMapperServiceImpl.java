package com.weifu.srm.supplier.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.repository.atomicservice.SupplierContractInfoMapperService;
import com.weifu.srm.supplier.repository.mapper.SupplierContractInfoMapper;
import com.weifu.srm.supplier.repository.po.SupplierContractInfoPO;
import org.springframework.stereotype.Service;

@Service
public class SupplierContractInfoMapperServiceImpl extends ServiceImpl<SupplierContractInfoMapper, SupplierContractInfoPO> implements SupplierContractInfoMapperService {
}
