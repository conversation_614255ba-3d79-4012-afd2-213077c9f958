package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnairePersonnelPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:58
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnairePersonnelMapperService extends IService<AdmissionQuestionnairePersonnelPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnairePersonnelPO> findByAdmissionNo(String admissionNo);
}
