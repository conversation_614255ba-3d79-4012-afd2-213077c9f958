package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierGradeAdjustApplyItemPO;
import com.weifu.srm.supplier.request.SupplierGetAllHistoryGradeReqDTO;
import com.weifu.srm.supplier.request.SupplierGradeHistoryReqDTO;
import com.weifu.srm.supplier.response.SupplierGetAllHistoryGradeRespDTO;
import com.weifu.srm.supplier.response.SupplierGradeHistoryRespDTO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface SupplierGradeAdjustApplyItemMapperService extends IService<SupplierGradeAdjustApplyItemPO> {

    Page<SupplierGradeHistoryRespDTO> queryHistoryList(Page<SupplierGradeHistoryReqDTO> page,
                                                       SupplierGradeHistoryReqDTO supplierGradeHistoryReqDTO);

    List<SupplierGradeHistoryRespDTO> queryExportHistoryList(SupplierGradeHistoryReqDTO supplierGradeHistoryReqDTO);

    List<SupplierGetAllHistoryGradeRespDTO> getAllHistoryGrade(SupplierGetAllHistoryGradeReqDTO reqDTO);
}
