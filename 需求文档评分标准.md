对BA（业务分析师）岗位的需求文档进行评分，需从**专业性、完整性、可执行性、协作性**四大核心维度切入，结合具体指标和场景化要求。以下是一套可落地的评分框架，附评分标准和权重参考：


### 一、专业性维度（30%）
**核心评估点**：文档是否符合行业规范、逻辑是否严谨、术语是否统一。

| 评分指标         | 评分标准（1-5分）                                                                 | 权重 |
|------------------|------------------------------------------------------------------------------------|------|
| **格式规范性**   | - 1分：无目录/版本混乱，随意使用标点/符号<br>- 3分：有基础目录，格式统一但缺乏细节（如字体/缩进无标准）<br>- 5分：采用公司标准模板（含页眉/页脚/变更日志），章节编号清晰，引用标注规范 | 8%   |
| **术语一致性**   | - 1分：同一概念出现多种表述（如“用户”“客户”混用）<br>- 3分：关键术语基本统一，但存在少量歧义（如未定义缩写“API”）<br>- 5分：附《术语表》，全文术语100%统一，非通用概念均备注解释               | 7%   |
| **逻辑严谨性**   | - 1分：需求描述前后矛盾（如功能A与功能B逻辑冲突）<br>- 3分：主流程清晰但分支场景缺失（如仅描述“正常提交”未提“提交失败”）<br>- 5分：采用“业务目标→功能流程→规则约束”分层描述，逻辑闭环无断层                   | 8%   |
| **需求溯源性**   | - 1分：无需求来源记录（如未标注“源自客户会议纪要XX页”）<br>- 3分：部分需求标注来源但不完整（如仅标记“业务部门提出”无具体负责人）<br>- 5分：每条需求关联明确的业务痛点/决策依据（附原始文档索引）                     | 7%   |  


### 二、完整性维度（30%）
**核心评估点**：是否覆盖业务全场景、是否遗漏关键要素。

| 评分指标         | 评分标准（1-5分）                                                                 | 权重 |
|------------------|------------------------------------------------------------------------------------|------|
| **业务场景覆盖** | - 1分：仅描述主干流程（如“用户下单”未提“库存不足”“支付异常”）<br>- 3分：覆盖80%常规场景，但缺乏极端情况（如“高并发下的接口限流”）<br>- 5分：包含正常流、异常流、逆向流程（如订单取消/退款），附状态机图或泳道图                | 10%  |
| **功能要素齐全** | - 1分：缺失核心功能（如电商需求未提“购物车”）<br>- 3分：功能描述笼统（如“实现数据统计”未明确统计维度/频率）<br>- 5分：按“功能点→操作步骤→输入输出→规则校验”四要素拆分，无模糊表述（如“金额精度保留2位小数”）          | 10%  |
| **非功能需求**   | - 1分：完全未提及性能/安全/合规要求（如“未说明系统响应时间需≤3秒”）<br>- 3分：提及但无量化标准（如“需保证数据安全”未明确加密方式）<br>- 5分：独立章节描述性能指标（如“支持2000TPS”）、安全策略（如“用户密码MD5加盐”）、合规要求（如“符合GDPR”） | 10%  |  


### 三、可执行性维度（25%）
**核心评估点**：需求是否可被开发、测试、验收团队准确理解和落地。

| 评分指标         | 评分标准（1-5分）                                                                 | 权重 |
|------------------|-------------------------------------------------------------------------------------|------|
| **技术可行性**   | - 1分：提出不可实现的需求（如“无需后端支持实现实时数据同步”）<br>- 3分：未与技术团队对齐（如要求“1天内开发复杂报表功能”）<br>- 5分：提前评估技术约束（如“基于现有中台能力实现XX功能”），附技术方案预沟通记录                  | 8%   |
| **验收标准明确** | - 1分：无验收指标（如“实现用户管理功能”未定义“管理”具体指什么）<br>- 3分：验收标准模糊（如“界面美观”）<br>- 5分：按“功能正确性（如‘点击删除按钮后数据状态变更为已删除’）+ 性能达标（如‘批量导入10万条数据≤5分钟’）”量化定义 | 9%   |
| **可视化辅助**   | - 1分：纯文字描述复杂逻辑（如用段落描述多层级审批流程）<br>- 3分：有流程图但不完整（如缺少角色节点）<br>- 5分：结合用例图、原型图、字段字典（如Axure原型+ER图），关键交互标注动效规则（如“按钮置灰条件：未勾选协议”）       | 8%   |  


### 四、协作性维度（15%）
**核心评估点**：需求文档是否促进跨团队共识，减少沟通成本。

| 评分指标         | 评分标准（1-5分）                                                                 | 权重 |
|------------------|-------------------------------------------------------------------------------------|------|
| **stakeholder对齐** | - 1分：未同步关键方（如开发/测试/业务方未参与需求评审）<br>- 3分：仅口头同步但无书面确认（如评审后未更新文档）<br>- 5分：组织至少2轮跨团队评审，附各角色确认意见（如业务方签字、技术方批注“方案可行”）               | 8%   |
| **版本管理**     | - 1分：无版本记录（如文档多次修改后无法追溯变更点）<br>- 3分：有版本号但变更说明笼统（如“优化部分内容”）<br>- 5分：按“V1.0（初版）→V1.1（根据测试意见补充异常流）”记录，变更内容用红色标注并说明影响范围                | 7%   |  


### 五、加分项与扣分项（浮动±5%）
- **加分项**：
    - 提前识别潜在风险并提出替代方案（如“建议分阶段实现XX功能以降低技术债务”）。
    - 文档被用作团队模板或培训材料（如其他BA参考复用率＞3次）。
- **扣分项**：
    - 需求反复变更导致返工（如同一功能在开发阶段修改≥3次）。
    - 因文档歧义引发生产问题（如开发误解需求导致功能上线后业务方投诉）。


### 评分应用场景
1. **新人考核**：侧重“格式规范性”“术语一致性”“协作性”，帮助建立标准化工作习惯。
2. **项目复盘**：重点分析“完整性”“可执行性”，如某模块验收通过率低时，追溯需求文档是否遗漏验收标准。
3. **晋升评估**：关注“专业性”中的需求溯源能力和“协作性”中的跨团队影响力，如是否主导过复杂需求的全流程闭环管理。  

 
# 需求文档评分标准完整表格

| 维度 | 评分指标 | 评分标准（1-5分） | 权重 |
|------|----------|-------------------|------|
| **专业性维度 (30%)** | **格式规范性** | - 1分：无目录/版本混乱，随意使用标点/符号<br>- 3分：有基础目录，格式统一但缺乏细节（如字体/缩进无标准）<br>- 5分：采用公司标准模板（含页眉/页脚/变更日志），章节编号清晰，引用标注规范 | 8% |
| | **术语一致性** | - 1分：同一概念出现多种表述（如"用户""客户"混用）<br>- 3分：关键术语基本统一，但存在少量歧义（如未定义缩写"API"）<br>- 5分：附《术语表》，全文术语100%统一，非通用概念均备注解释 | 7% |
| | **逻辑严谨性** | - 1分：需求描述前后矛盾（如功能A与功能B逻辑冲突）<br>- 3分：主流程清晰但分支场景缺失（如仅描述"正常提交"未提"提交失败"）<br>- 5分：采用"业务目标→功能流程→规则约束"分层描述，逻辑闭环无断层 | 8% |
| | **需求溯源性** | - 1分：无需求来源记录（如未标注"源自客户会议纪要XX页"）<br>- 3分：部分需求标注来源但不完整（如仅标记"业务部门提出"无具体负责人）<br>- 5分：每条需求关联明确的业务痛点/决策依据（附原始文档索引） | 7% |
| **完整性维度 (30%)** | **业务场景覆盖** | - 1分：仅描述主干流程（如"用户下单"未提"库存不足""支付异常"）<br>- 3分：覆盖80%常规场景，但缺乏极端情况（如"高并发下的接口限流"）<br>- 5分：包含正常流、异常流、逆向流程（如订单取消/退款），附状态机图或泳道图 | 10% |
| | **功能要素齐全** | - 1分：缺失核心功能（如电商需求未提"购物车"）<br>- 3分：功能描述笼统（如"实现数据统计"未明确统计维度/频率）<br>- 5分：按"功能点→操作步骤→输入输出→规则校验"四要素拆分，无模糊表述 | 10% |
| | **非功能需求** | - 1分：完全未提及性能/安全/合规要求<br>- 3分：提及但无量化标准（如"需保证数据安全"未明确加密方式）<br>- 5分：独立章节描述性能指标、安全策略、合规要求 | 10% |
| **可执行性维度 (25%)** | **技术可行性** | - 1分：提出不可实现的需求（如"无需后端支持实现实时数据同步"）<br>- 3分：未与技术团队对齐（如要求"1天内开发复杂报表功能"）<br>- 5分：提前评估技术约束，附技术方案预沟通记录 | 8% |
| | **验收标准明确** | - 1分：无验收指标（如"实现用户管理功能"未定义"管理"具体指什么）<br>- 3分：验收标准模糊（如"界面美观"）<br>- 5分：按"功能正确性+性能达标"量化定义 | 9% |
| | **可视化辅助** | - 1分：纯文字描述复杂逻辑<br>- 3分：有流程图但不完整（如缺少角色节点）<br>- 5分：结合用例图、原型图、字段字典，关键交互标注动效规则 | 8% |
| **协作性维度 (15%)** | **stakeholder对齐** | - 1分：未同步关键方（如开发/测试/业务方未参与需求评审）<br>- 3分：仅口头同步但无书面确认<br>- 5分：组织至少2轮跨团队评审，附各角色确认意见 | 8% |
| | **版本管理** | - 1分：无版本记录<br>- 3分：有版本号但变更说明笼统<br>- 5分：按版本记录变更内容，并用红色标注说明影响范围 | 7% |

注：总分计算方式为各维度得分与权重的加权和，另有±5%的加减分项。