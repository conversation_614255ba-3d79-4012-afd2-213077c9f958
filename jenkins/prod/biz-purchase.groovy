def createVersion() {
    // 定义一个版本号作为当次构建的版本
    return new Date().format('yyyyMMddHHmmss') + "_${env.BUILD_ID}"
}

pipeline {

    agent {label 'maven-3.6.3-jdk-11'}

    options {
        // 设置整个 Pipeline 的超时时间为 30分钟
        timeout(time: 30, unit: 'MINUTES')
        // 保持构建的最大个数
        buildDiscarder(logRotator(numToKeepStr: '10'))
    }

    parameters {
        gitParameter branchFilter: 'origin/(.*)', defaultValue: 'prod', name: 'TAG', type: 'PT_TAG'
    }

    environment {
        // 当前构建环境
        ENV='prod'
        REGISTRY='repository.weifu.com.cn'
        DOCKERHUB_NAMESPACE='srm'
        DOCKER_CREDENTIAL_ID='************************************'
        TAG=createVersion()
        APP_NAME ='weifu-srm-biz-purchase'
        CURRENT_ENV='proddevops'
        K8S_NAME_NAMESPACE='c-8nq24:p-z78h4'
        DOCKER_NAME_DIR='weifu-srm-biz-purchase/weifu-srm-biz-purchase-start/dockerfile'
    }

    stages {
        stage('代码初始化'){
            steps {
                echo "TAG = ${params.TAG}"
                checkout([
                        $class: 'GitSCM',
                        branches: [[name:"refs/tags/${params.TAG}"]],
                        userRemoteConfigs:[[
                                                   url: 'http://source.weifu.com.cn:81/srm/weifu-srm-biz-services.git',
                                                   credentialsId: '19d1342e-e53e-4368-98c4-d36fe43f74a5',
                                           ]]
                ])
            }
        }

        stage('编译'){
            steps {
                container('maven'){
                    sh 'pwd; ls -all'
                    sh 'mvn -f $APP_NAME clean package -Dmaven.test.skip=true'
                }
            }
        }

        stage('制作docker镜像'){
            steps {
                container ('maven') {
                    sh 'cat $DOCKER_NAME_DIR'
                    sh 'docker build -f $DOCKER_NAME_DIR -t $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:${ENV}_$TAG .'
                    withCredentials([usernamePassword(passwordVariable : 'DOCKER_PASSWORD' ,usernameVariable : 'DOCKER_USERNAME' ,credentialsId : "$DOCKER_CREDENTIAL_ID" ,)]) {
                        sh 'echo "$DOCKER_PASSWORD" | docker login $REGISTRY -u "$DOCKER_USERNAME" --password-stdin'
                        sh 'docker push  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:${ENV}_$TAG'
                        sh 'docker rmi  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:${ENV}_$TAG'
                    }
                }
            }
        }

        stage('收集') {
            steps {
                archiveArtifacts artifacts: '**/target/*.jar', fingerprint: true
            }
        }

        stage('deploy to rancher') {
            steps {
                container ('maven') {
                    rancherRedeploy alwaysPull: true, credential: 'rancher-jenkins-token', images: "$REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:${ENV}_$TAG", workload: "/project/$K8S_NAME_NAMESPACE/workload/deployment:srm-prod:$APP_NAME"
                }
            }
        }

        stage('Build Data submit') {
            steps {
                script{
                    getDatabaseConnection(type: 'GLOBAL') {
                        def result = sql(sql: "INSERT INTO public.jenkins_build_info( job_name,job_url, build_number, duration, duration_string, description, current_result, collect_time,build_from)	VALUES ('$env.JOB_NAME', '$env.JOB_URL','$env.BUILD_NUMBER', '$currentBuild.duration', '$currentBuild.durationString','$currentBuild.description', '$currentBuild.currentResult', CURRENT_TIMESTAMP,'$env.CURRENT_ENV');")
                        println "Class of result: ${result.getClass().toString()}"
                        println "Value of result: ${result}"
                    }
                }
            }
        }
    }
}
