def createVersion() {
    // 定义一个版本号作为当次构建的版本
    return new Date().format('yyyyMMddHHmmss') + "_${env.BUILD_ID}"
}

def createTime() {
    // 获取当前时间
    return new Date().format('yyyyMMddHHmmss')
}
def getAppName (){
    String name = "weifu-srm-biz-supplier-performance"
    println(name)
    return name.toLowerCase()
}

pipeline {

    agent {label 'maven-3.6.3-jdk-11'}

    options {
        // 设置整个 Pipeline 的超时时间为 30分钟
        timeout(time: 30, unit: 'MINUTES')
        // 保持构建的最大个数
        buildDiscarder(logRotator(numToKeepStr: '10'))
    }

    parameters {
        gitParameter branchFilter: 'origin/(.*)', defaultValue: 'test', name: 'BRANCH', type: 'PT_BRANCH'
    }

    environment {
        // 当前构建环境
        ENV = 'test'
        REGISTRY = 'repository.weifu.com.cn'
        DOCKERHUB_NAMESPACE = 'msa-snapshot/srm'
        DOCKER_CREDENTIAL_ID = '************************************'
        TAG=createVersion()
        TIME=createTime()
        APP_NAME = getAppName()
        CURRENT_ENV='testdevops'
    }

    stages {

        stage('代码初始化'){

            steps {
                container ('maven') {
                    withSonarQubeEnv('sonar') {
                        //仅为初始化Tag信息 不做编译 fssc.manage的编译在下方
                        echo "BRANCH = ${params.BRANCH}"
                        git branch: "test", credentialsId: '************************************', url: 'http://source.weifu.com.cn:81/srm/weifu-srm-biz-services.git'
                    }
                }
            }


        }
        stage('编译'){
            steps {
                container('maven'){
                    sh 'pwd; ls -all'

                    sh 'mvn -f weifu-srm-biz-supplier-performance clean package -Dmaven.test.skip=true'

                    //  withSonarQubeEnv('sonar') {
                    //      sh 'mvn sonar:sonar -Dsonar.projectKey=srm -pl weifu-srm-biz-supplier -am '

                    //  }


                }
            }


        }

        stage('制作docker镜像'){
            steps {
                container ('maven') {
                    sh 'cat weifu-srm-biz-supplier-performance/weifu-srm-biz-supplier-performance-start/dockerfile'

                    sh 'docker build -f weifu-srm-biz-supplier-performance/weifu-srm-biz-supplier-performance-start/dockerfile -t $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:${ENV}_$TAG .'
                    withCredentials([usernamePassword(passwordVariable : 'DOCKER_PASSWORD' ,usernameVariable : 'DOCKER_USERNAME' ,credentialsId : "$DOCKER_CREDENTIAL_ID" ,)]) {
                        sh 'echo "$DOCKER_PASSWORD" | docker login $REGISTRY -u "$DOCKER_USERNAME" --password-stdin'
                        sh 'docker push  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:${ENV}_$TAG'
                        sh 'docker rmi  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:${ENV}_$TAG'
                    }

                }
            }
        }
        stage('收集') {
            steps {

                archiveArtifacts artifacts: '**/target/*.jar', fingerprint: true
            }

        }
        stage('deploy to rancher') {
            steps {
                container ('maven') {


                    rancherRedeploy alwaysPull: true, credential: 'rancher-jenkins-token', images: "$REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:${ENV}_$TAG", workload: "/project/c-2mf7m:p-8kprd/workload/deployment:srm-pre:weifu-srm-biz-supplier-performance"
                }
            }
        }





        stage('Build Data submit') {
            steps {
                script{
                    getDatabaseConnection(type: 'GLOBAL') {

                        def result = sql(sql: "INSERT INTO public.jenkins_build_info( job_name,job_url, build_number, duration, duration_string, description, current_result, collect_time,build_from)  VALUES ('$env.JOB_NAME', '$env.JOB_URL','$env.BUILD_NUMBER', '$currentBuild.duration', '$currentBuild.durationString','$currentBuild.description', '$currentBuild.currentResult', CURRENT_TIMESTAMP,'$env.CURRENT_ENV');")

                        println "Class of result: ${result.getClass().toString()}"
                        println "Value of result: ${result}"

                    }
                }
            }
        }


    }
}
