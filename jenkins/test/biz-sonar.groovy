


pipeline {

    agent {label 'maven-3.6.3-jdk-11'}

    parameters {
        gitParameter branchFilter: 'origin/(.*)', defaultValue: 'test', name: 'BRANCH', type: 'PT_BRANCH'
    }

    environment {
        CURRENT_ENV = 'testdevops'
    }

    stages {
        stage('代码初始化') {
            steps {
                container('maven') {
                    withSonarQubeEnv('sonar') {
                        git branch: "test", credentialsId: '19d1342e-e53e-4368-98c4-d36fe43f74a5', url: 'http://source.weifu.com.cn:81/srm/weifu-srm-biz-services.git'
                    }
                }
            }
        }

        stage('编译&扫描') {
            steps {
                container('maven') {
                    sh 'pwd; ls -all'
                    sh 'mvn -f weifu-srm-biz-bff-common clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-bff-common -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-bff-common"
                    }
                    sh 'mvn -f weifu-srm-biz-bff-internal clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-bff-internal -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-bff-internal"
                    }
                    sh 'mvn -f weifu-srm-biz-bff-supplier clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-bff-supplier -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-bff-supplier"
                    }
                    sh 'mvn -f weifu-srm-biz-price clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-price -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-price"
                    }
                    sh 'mvn -f weifu-srm-biz-purchase clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-purchase -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-purchase"
                    }
                    sh 'mvn -f weifu-srm-biz-requirement clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-requirement -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-requirement"
                    }
                    sh 'mvn -f weifu-srm-biz-sourcing clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-sourcing -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-sourcing"
                    }
                    sh 'mvn -f weifu-srm-biz-supplier clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-supplier -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-supplier"
                    }
                    sh 'mvn -f weifu-srm-biz-supplier-cio clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-supplier-cio -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-supplier-cio"
                    }
                    sh 'mvn -f weifu-srm-biz-supplier-performance clean package -Dmaven.test.skip=true'
                    withSonarQubeEnv('sonar') {
                        sh "mvn ${SONAR_MAVEN_GOAL} -Dsonar.projectKey=weifu-srm-biz-supplier-performance -Dsonar.projectDescription='[SRM]' -f weifu-srm-biz-supplier-performance"
                    }
                }
            }
        }

        stage('Build Data submit') {
            steps {
                script {
                    getDatabaseConnection(type: 'GLOBAL') {

                        def result = sql(sql: "INSERT INTO public.jenkins_build_info( job_name,job_url, build_number, duration, duration_string, description, current_result, collect_time,build_from)	VALUES ('$env.JOB_NAME', '$env.JOB_URL','$env.BUILD_NUMBER', '$currentBuild.duration', '$currentBuild.durationString','$currentBuild.description', '$currentBuild.currentResult', CURRENT_TIMESTAMP,'$env.CURRENT_ENV');")

                        println "Class of result: ${result.getClass().toString()}"
                        println "Value of result: ${result}"

                    }
                }
            }
        }
    }
}
