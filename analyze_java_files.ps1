# 设置项目根目录
$projectDir = "C:\workspace\weifu\weifu-srm-biz-services"
$outputFile = Join-Path $projectDir "developer_code_stats.csv"

# 清空或创建输出文件
if (Test-Path $outputFile) { Remove-Item $outputFile }
"Developer,File,LinesOfCode,Methods" | Out-File -FilePath $outputFile -Encoding utf8

# 获取所有 .java 文件
Get-ChildItem -Path $projectDir -Recurse -Include *.java | ForEach-Object {
    $filePath = $_.FullName
    Write-Host "Processing $filePath"

    try {
        # 获取文件总行数
        $lines = Get-Content $filePath -ErrorAction Stop | Measure-Object | Select-Object -ExpandProperty Count
    } catch {
        Write-Host "无法读取文件: $filePath"
        $lines = 0
    }

    # 获取提交者信息（使用 git blame）
    $blameOutput = git blame --porcelain $filePath 2>$null
    $authors = $blameOutput | Where-Object { $_ -match '^author ' } | ForEach-Object { $_.Split(' ', 2)[1] } | Group-Object | Sort-Object Count -Descending | Select-Object -First 1
    $topAuthor = if ($authors) { $authors.Name } else { "Unknown" }

    try {
        # 统计方法数量（简单匹配 public/private/protected 方法）
        $methodCount = Get-Content $filePath -ErrorAction Stop | Select-String -Pattern "^(public|private|protected)\s+[^\s]+\s+[^\s]+\s*\(" | Measure-Object | Select-Object -ExpandProperty Count
    } catch {
        $methodCount = 0
    }

    # 输出统计信息到 CSV
    "$topAuthor,$filePath,$lines,$methodCount" | Add-Content -Path $outputFile -Encoding utf8
}

Write-Host "Analysis completed. Results saved to $outputFile"