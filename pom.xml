<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.demo.framework</groupId>
    <artifactId>demo-framework</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>demo-framework-starter-bom</module>
        <module>demo-framework-starter-common</module>
        <module>demo-framework-starter-cache</module>
        <module>demo-framework-starter-common-util</module>
        <module>demo-framework-starter-mybatis</module>
        <module>demo-framework-starter-parent</module>
        <module>demo-framework-starter-rpc</module>
        <module>demo-framework-starter-log</module>
        <module>demo-framework-starter-id</module>
        <module>demo-framework-starter-swagger</module>
        <module>demo-framework-starter-cola</module>
        <module>demo-framework-starter-mq</module>
        <module>demo-framework-starter-test</module>
        <module>demo-framework-starter-excel</module>
        <module>demo-framework-starter-file</module>
        <module>demo-framework-starter-web</module>
        <module>demo-framework-starter-cloud</module>
        <module>demo-framework-starter-security</module>
        <module>demo-framework-starter-nacos</module>
        <module>demo-framework-example</module>
        <module>demo-framework-starter-generator</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
</project>