```markdown
# DI03_03报表注册管理 技术方案设计文档

## 目录
1. [核心需求分析](#核心需求分析)
2. [核心逻辑解释](#核心逻辑解释)
3. [流程图](#流程图)
4. [数据库表设计](#数据库表设计)
5. [ER图](#er图)
6. [时序图](#时序图)
7. [接口设计](#接口设计)

---

## 核心需求分析
### 需求点列表
1. **报表管理**  
   - 新增、查看、编辑、查询报表注册信息（支持模糊搜索、分页、状态过滤）。
   - 报表状态管理（启用/停用/待审批/审批失败）。
2. **权限控制**  
   - 按部门/地区/角色分配报表权限，支持目录树级联选择。
3. **表单校验**  
   - 必填字段校验（如标题、类别、来源）、唯一性校验（如标题重复）。
4. **文件上传**  
   - 支持上传指标说明文档（非必填）和自定义图标（必填，限制格式为JPG，大小≤300KB）。
5. **审批流程**  
   - 提交后触发业权系统审批，状态同步更新。
6. **展示与交互**  
   - 列表按注册时间倒序展示，支持PC/移动端类型过滤。

---

## 核心逻辑解释
### 1. 权限分配逻辑
- **实现方式**：  
  采用动态目录树渲染，根据用户选择的权限划分方式（部门/地区/角色）加载对应的层级数据。  
  前端通过勾选非叶子节点自动选中其所有子节点，后端存储最小粒度的权限项（如最底层部门ID）。

### 2. 表单校验逻辑
- **前端校验**：  
  实时检查必填字段是否为空、标题是否重复（调用`/api/reports/check-title`接口）。  
- **后端校验**：  
  提交时二次校验，防止绕过前端操作，返回具体错误字段及提示。

### 3. 状态流转逻辑
- **状态机规则**：  
  ```plaintext
  待审批 → 启用（审批通过）
  待审批 → 审批失败（审批驳回）
  启用 ↔ 停用（手动切换）
  ```
- **审批回调**：  
  业权系统通过Webhook通知审批结果，触发状态更新。

### 4. 搜索与分页逻辑
- **模糊搜索**：  
  后端使用数据库`LIKE`语句实现大小写不敏感的模糊匹配（如`LOWER(title) LIKE LOWER('%${keyword}%')`）。
- **分页优化**：  
  使用数据库分页（如MySQL `LIMIT offset, size`），结合缓存提升高频查询性能。

---

## 流程图
### 1. 新增报表注册流程
```mermaid
graph TD
  A[用户点击新增按钮] --> B[填写表单]
  B --> C{校验通过?}
  C -->|是| D[提交至业权系统审批]
  C -->|否| E[提示错误信息]
  D --> F[状态更新为待审批]
```

### 2. 搜索报表流程
```mermaid
graph TD
  A[用户输入搜索条件] --> B[前端拼接查询参数]
  B --> C[调用GET /api/reports接口]
  C --> D{是否有结果?}
  D -->|是| E[渲染列表]
  D -->|否| F[显示"暂无搜索结果"]
```

---

## 数据库表设计
### 1. 报表信息表（`report`）
| 字段名            | 类型         | 描述                     |
|-------------------|--------------|--------------------------|
| id                | BIGINT       | 主键                     |
| title             | VARCHAR(255) | 报表标题（唯一）         |
| code              | VARCHAR(50)  | 自动生成的报表编码       |
| category          | VARCHAR(100) | 报表类别                 |
| source            | VARCHAR(50)  | 来源（观远BI/QuickBI等） |
| status            | VARCHAR(20)  | 状态（启用/停用等）      |
| created_time      | DATETIME     | 注册时间                 |
| updated_time      | DATETIME     | 最后更新时间             |

### 2. 权限分配表（`report_permission`）
| 字段名       | 类型        | 描述               |
|--------------|-------------|--------------------|
| id           | BIGINT      | 主键               |
| report_id    | BIGINT      | 关联报表ID         |
| type         | VARCHAR(20) | 权限类型（部门/地区/角色） |
| value        | VARCHAR(50) | 权限值（如部门ID） |

### 3. 图标表（`icon`）
| 字段名       | 类型        | 描述               |
|--------------|-------------|--------------------|
| id           | BIGINT      | 主键               |
| report_id    | BIGINT      | 关联报表ID         |
| type         | VARCHAR(10) | 类型（默认/自定义）|
| path         | VARCHAR(255)| 图标存储路径       |

---

## ER图
```mermaid
erDiagram
  report ||--o{ report_permission : "1:N"
  report ||--o{ icon : "1:N"
  report {
    BIGINT id
    VARCHAR title
    VARCHAR code
    VARCHAR category
    VARCHAR source
    VARCHAR status
    DATETIME created_time
  }
  report_permission {
    BIGINT id
    BIGINT report_id
    VARCHAR type
    VARCHAR value
  }
  icon {
    BIGINT id
    BIGINT report_id
    VARCHAR type
    VARCHAR path
  }
```

---

## 时序图
### 提交报表审批时序
```mermaid
sequenceDiagram
  participant 用户
  participant 前端
  participant 后端
  participant 业权系统
  用户->>前端: 填写表单并提交
  前端->>后端: POST /api/reports
  后端->>后端: 校验数据
  后端->>业权系统: 调用审批接口
  业权系统-->>后端: 返回审批ID
  后端-->>前端: 返回提交成功
  业权系统->>后端: Webhook通知审批结果
  后端->>后端: 更新报表状态
```

---

## 接口设计
### 1. 报表列表查询
**端点**：`GET /api/reports`  
**参数**：
```json
{
  "title": "销售报表",
  "category": "财务",
  "status": "启用",
  "page": 1,
  "size": 10
}
```
**响应**：
```json
{
  "data": [
    {
      "id": 1,
      "title": "销售报表",
      "code": "RPT-001",
      "status": "启用"
    }
  ],
  "total": 100
}
```

### 2. 提交报表审批
**端点**：`POST /api/reports/{id}/submit`  
**请求体**：无  
**响应**：
```json
{
  "code": 200,
  "message": "已提交审批，当前状态：待审批"
}
```

### 3. 权限分配接口
**端点**：`POST /api/reports/{id}/permissions`  
**请求体**：
```json
{
  "type": "部门",
  "values": ["dept-001", "dept-002"]
}
```
```



```markdown
 

---

## 数据库表设计（补充）
### 4. 文件附件表（`attachment`）
| 字段名         | 类型         | 描述                     |
|----------------|--------------|--------------------------|
| id             | BIGINT       | 主键                     |
| report_id      | BIGINT       | 关联报表ID               |
| type           | VARCHAR(20)  | 附件类型（指标说明/图标）|
| file_name      | VARCHAR(255) | 文件名                   |
| file_path      | VARCHAR(255) | 存储路径（OSS或本地）    |
| file_size      | INT          | 文件大小（单位：KB）     |
| upload_time    | DATETIME     | 上传时间                 |

### 5. 审批记录表（`approval`）
| 字段名         | 类型         | 描述                     |
|----------------|--------------|--------------------------|
| id             | BIGINT       | 主键                     |
| report_id      | BIGINT       | 关联报表ID               |
| approver       | VARCHAR(100) | 审批人                   |
| action         | VARCHAR(20)  | 操作（通过/驳回）        |
| comment        | TEXT         | 审批意见                 |
| approve_time   | DATETIME     | 审批时间                 |

---

## ER图（补充）
```mermaid
erDiagram
  report ||--o{ attachment : "1:N"
  report ||--o{ approval : "1:N"
  report {
    BIGINT id
    VARCHAR title
    VARCHAR code
    VARCHAR category
    VARCHAR source
    VARCHAR status
    DATETIME created_time
  }
  attachment {
    BIGINT id
    BIGINT report_id
    VARCHAR type
    VARCHAR file_name
    VARCHAR file_path
    INT file_size
  }
  approval {
    BIGINT id
    BIGINT report_id
    VARCHAR approver
    VARCHAR action
    TEXT comment
  }
```

---

## 流程图（补充）
### 3. 权限分配目录树勾选逻辑
```mermaid
graph TD
  A[用户选择权限类型] --> B[加载目录树层级数据]
  B --> C[勾选非叶子节点]
  C --> D[自动选中所有子节点]
  D --> E[后端存储最小粒度权限项]
```

### 4. 文件上传流程
```mermaid
graph TD
  A[用户点击上传按钮] --> B[前端校验文件格式与大小]
  B --> C{校验通过?}
  C -->|是| D[上传至文件服务器]
  C -->|否| E[提示错误]
  D --> F[返回文件存储路径]
  F --> G[保存路径到数据库]
```

---

## 时序图（补充）
### 文件上传时序
```mermaid
sequenceDiagram
  participant 用户
  participant 前端
  participant 后端
  participant 文件存储服务
  用户->>前端: 选择文件并上传
  前端->>后端: POST /api/attachments (含文件流)
  后端->>文件存储服务: 上传文件
  文件存储服务-->>后端: 返回文件路径
  后端->>数据库: 保存附件记录
  后端-->>前端: 返回上传成功
```

### 审批回调时序
```mermaid
sequenceDiagram
  participant 业权系统
  participant 后端
  participant 数据库
  业权系统->>后端: POST /api/webhook/approval (审批结果)
  后端->>后端: 解析审批状态
  后端->>数据库: 更新report.status
  后端->>数据库: 插入approval记录
  后端-->>业权系统: 返回200 OK
```

---

## 接口设计（补充）
### 4. 文件上传接口
**端点**：`POST /api/attachments`  
**请求体**：`multipart/form-data`  
**参数**：
```json
{
  "reportId": 1,
  "type": "指标说明",
  "file": "<文件二进制流>"
}
```
**响应**：
```json
{
  "code": 200,
  "data": {
    "filePath": "oss://reports/2024/05/report1.jpg"
  }
}
```

### 5. 状态切换接口
**端点**：`PUT /api/reports/{id}/status`  
**请求体**：
```json
{
  "status": "停用",
  "operator": "admin"
}
```
**响应**：
```json
{
  "code": 200,
  "message": "状态已更新为停用"
}
```

---

## 技术细节补充
### 1. 模糊搜索优化
- **索引设计**：对`report.title`、`report.code`字段添加全文索引（如MySQL的FULLTEXT索引）。
- **查询示例**：
  ```sql
  SELECT * FROM report 
  WHERE MATCH(title) AGAINST('销售*' IN BOOLEAN MODE);
  ```

### 2. 权限分配实现
- **目录树存储**：使用闭包表（Closure Table）存储部门/地区层级关系。
- **权限校验逻辑**：  
  用户访问报表时，后端校验其所属部门/地区/角色是否在`report_permission`表中存在匹配记录。

### 3. 安全性设计
- **接口鉴权**：所有接口需携带JWT Token，通过网关统一校验。
- **文件上传限制**：  
  后端校验文件MIME类型（如`image/jpeg`）及大小，防止恶意上传。

---

## 部署架构
```mermaid
graph LR
  A[客户端] --> B[API网关]
  B --> C[报表服务]
  B --> D[文件服务]
  C --> E[(MySQL)]
  C --> F[Redis]
  D --> G[OSS/MinIO]
  C --> H[业权系统]
```

**说明**：  
- 使用Redis缓存高频查询（如报表列表）。
- 文件服务独立部署，支持横向扩展。
```