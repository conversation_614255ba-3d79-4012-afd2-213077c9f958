package com.demo.framework.web.serialize;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description
 */
public class StringToInstantDeserializer extends JsonDeserializer<Instant> {
    @Override
    public Instant deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String dateString = jsonParser.getText().trim();
        if (StringUtils.isNotBlank(dateString)) {
            return LocalDateTimeUtil.parse(dateString, DatePattern.NORM_DATETIME_PATTERN).atZone(ZoneId.systemDefault()).toInstant();
        }
        return null;
    }
}
