package com.demo.framework.web.converter;

import cn.hutool.core.date.LocalDateTimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.Nullable;

import java.time.LocalDate;
/**
 * <AUTHOR>
 * @version 1.0
 * @Description LocalDate转换器，用于转换RequestParam和PathVariable参数
 */
public class LocalDateConverter implements Converter<String, LocalDate> {
    @Override
    public LocalDate convert(@Nullable String source) {
        return  StringUtils.isEmpty(source) ? null : LocalDateTimeUtil.parseDate(source);
    }
}
