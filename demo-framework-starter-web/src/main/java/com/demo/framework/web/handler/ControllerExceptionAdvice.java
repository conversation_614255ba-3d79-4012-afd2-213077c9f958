//package com.demo.framework.web.handler;
//
//import com.demo.framework.common.core.dto.ResponseDTO;
//import com.demo.framework.common.core.enums.SystemStatusEnum;
//import com.demo.framework.common.core.exception.ServiceException;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.validation.BindException;
//import org.springframework.validation.BindingResult;
//import org.springframework.validation.FieldError;
//import org.springframework.validation.ObjectError;
//import org.springframework.web.bind.MethodArgumentNotValidException;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @Description controller异常统一处理
// */
//@Slf4j
//@ResponseBody
//@ControllerAdvice
//public class ControllerExceptionAdvice<T> {
//
//
//    /***
//     * 全局异常，如果没有匹配到上述准确的异常，都会到这里来处理
//     * @param e 没有匹配到的全局异常
//     */
//    @ExceptionHandler(Exception.class)
//    public ResponseDTO<T> all(Exception e) {
//
//        ResponseDTO dto;
//        log.error(e.getMessage(), e);
//        if (e instanceof MethodArgumentNotValidException) {
//            // 参数校验错误
//            dto = ResponseDTO.fail(SystemStatusEnum.PARAMETER_ERROR.getCode(), getValidException((MethodArgumentNotValidException) e));
//        }/* else if (e instanceof UnauthorizedException) {
//            //权限不足
//            dto = ResponseDTO.fail(SystemStatusEnum.INSUFFICIENT_AUTHORITY.getCode(), SystemStatusEnum.INSUFFICIENT_AUTHORITY.getMessage());
//        } */else if (e instanceof BindException) {
//            //参数绑定错误，数据类型不一致
//            dto = ResponseDTO.fail(SystemStatusEnum.PARAMETER_ERROR.getCode(), getBindExceptionMessage((BindException) e));
//        } else if (e instanceof ServiceException serviceException) {
//            dto = ResponseDTO.fail(serviceException.getCode(), serviceException.getMessage());
//        } else {
//            dto = ResponseDTO.fail(SystemStatusEnum.UNKNOWN_ERROR.getCode(), e.getMessage());
//        }
//        return dto;
//    }
//
//    /**
//     * 参数绑定异常提示信息
//     *
//     */
//    private String getBindExceptionMessage(BindException bindException) {
//        List<FieldError> fieldErrorList = bindException.getFieldErrors();
//        if (CollectionUtils.isEmpty(fieldErrorList)) {
//            return null;
//        }
//        StringBuffer sb = new StringBuffer();
//        for (FieldError fieldError : fieldErrorList) {
//            sb.append("[").append(fieldError).append("] ");
//        }
//        return sb.toString();
//    }
//
//    /**
//     * 格式化参数错误
//     *
//     */
//    private String getValidException(MethodArgumentNotValidException validException) {
//        BindingResult bindingResult = validException.getBindingResult();
//        List<ObjectError> objectErrorList = bindingResult.getAllErrors();
//        StringBuilder sb = new StringBuilder("数据类型错误：");
//        for (ObjectError error : objectErrorList) {
//            sb.append("[").append(error.getDefaultMessage()).append("] ");
//        }
//        return sb.toString();
//    }
//}
