# 订单中心微服务技术设计文档

## 1. 文档概述

### 1.1 文档目的
本文档旨在详细描述订单中心微服务的技术实现方案，为开发团队提供清晰的技术指导和实现规范。

### 1.2 功能需求

#### 1.2.1 订单管理功能
- 业务描述：
  - 支持订单全生命周期管理，包括创建、修改、查询、取消等操作
  - 支持多种订单类型：普通订单、预售订单、拼团订单
  - 支持订单状态流转和状态管理
  - 集成优惠券、促销活动等营销功能

```mermaid
graph TD
    A[开始] --> B[创建订单]
    B --> C{是否使用优惠}
    C -->|是| D[计算优惠金额]
    C -->|否| E[计算订单金额]
    D --> E
    E --> F[生成订单号]
    F --> G[保存订单信息]
    G --> H[结束]
```

#### 1.2.2 支付管理功能
- 业务描述：
  - 支持多种支付方式接入
  - 支持组合支付
  - 支持分期付款
  - 完整的支付流程管理

```mermaid
graph TD
    A[用户下单] --> B[创建支付订单]
    B --> C[选择支付方式]
    C --> D{支付方式}
    D -->|微信支付| E1[调用微信支付]
    D -->|支付宝| E2[调用支付宝]
    D -->|银联| E3[调用银联]
    E1 --> F[支付结果处理]
    E2 --> F
    E3 --> F
    F --> G{支付成功?}
    G -->|是| H[更新订单状态]
    G -->|否| I[支付失败处理]
```

#### 1.2.3 物流管理功能
- 业务描述：
  - 支持物流信息管理
  - 支持发货管理
  - 支持物流轨迹查询

```mermaid
graph TD
    A[确认发货] --> B[选择物流公司]
    B --> C[录入物流单号]
    C --> D[更新订单状态]
    D --> E[推送物流信息]
    E --> F[物流轨迹更新]
    F --> G[签收确认]
```

### 1.3 设计原则
1. 高可用原则
   - 系统采用分布式架构，确保服务高可用
   - 关键业务采用异步处理，提高系统吞吐量
   - 合理使用缓存，提升系统性能

2. 可扩展原则
   - 采用微服务架构，支持服务独立扩展
   - 使用领域驱动设计，保证业务逻辑清晰
   - 预留系统扩展点，支持新功能快速接入

3. 安全性原则
   - 实现完善的权限控制
   - 敏感数据加密存储
   - 关键操作日志记录

4. 一致性原则
   - 采用分布式事务确保数据一致性
   - 状态变更通过事件驱动，保证状态同步
   - 异常情况下的数据补偿机制

## 2. 架构设计

### 2.1 整体架构

```mermaid
graph TB
    subgraph 客户端层
        A1[Web前端]
        A2[移动端]
        A3[第三方系统]
    end

    subgraph 接入层
        B1[Nginx负载均衡]
        B2[API网关]
    end

    subgraph 微服务层
        C1[订单服务]
        C2[支付服务]
        C3[库存服务]
        C4[物流服务]
        C5[用户服务]
    end

    subgraph 中间件层
        D1[Redis集群]
        D2[RabbitMQ集群]
        D3[Elasticsearch]
        D4[Sentinel]
        D5[Nacos]
    end

    subgraph 存储层
        E1[MySQL主从]
        E2[MongoDB集群]
    end

    subgraph 监控层
        F1[Prometheus]
        F2[Grafana]
        F3[SkyWalking]
    end

    A1 & A2 & A3 --> B1
    B1 --> B2
    B2 --> C1 & C2 & C3 & C4 & C5
    C1 & C2 & C3 & C4 & C5 --> D1 & D2 & D3 & D4 & D5
    C1 & C2 & C3 & C4 & C5 --> E1 & E2
    C1 & C2 & C3 & C4 & C5 --> F1 & F2 & F3
```

### 2.2 技术栈选型

| 类别 | 技术选型 | 说明 |
|-----|---------|------|
| 开发框架 | Spring Cloud Alibaba | 微服务开发框架 |
| 服务注册与发现 | Nacos | 服务注册中心 |
| 配置中心 | Nacos | 配置管理中心 |
| 网关 | Spring Cloud Gateway | API网关 |
| 负载均衡 | Nginx + Ribbon | 服务端负载均衡 |
| 服务熔断 | Sentinel | 服务熔断降级 |
| 分布式事务 | Seata | 分布式事务处理 |
| 消息队列 | RabbitMQ | 消息中间件 |
| 缓存 | Redis | 分布式缓存 |
| 数据库 | MySQL 8.0 | 关系型数据库 |
| 搜索引擎 | Elasticsearch | 订单搜索服务 |
| 容器化 | Docker + K8s | 容器编排与管理 |
| 监控 | Prometheus + Grafana | 系统监控 |
| 链路追踪 | SkyWalking | 分布式追踪 |
| 日志收集 | ELK Stack | 日志管理 |

## 3. 核心流程设计

### 3.1 订单创建流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as 网关
    participant O as 订单服务
    participant P as 支付服务
    participant I as 库存服务
    participant MQ as 消息队列
    
    C->>G: 提交订单请求
    G->>O: 转发请求
    O->>I: 校验库存
    I-->>O: 返回库存状态
    
    alt 库存充足
        O->>O: 创建订单
        O->>I: 锁定库存
        O->>P: 创建支付单
        P-->>O: 返回支付信息
        O->>MQ: 发送订单创建事件
        O-->>C: 返回订单信息
    else 库存不足
        O-->>C: 返回库存不足
    end
```

流程步骤描述：
1. 客户端提交订单请求到网关
2. 网关进行身份认证和请求转发
3. 订单服务接收请求，校验订单数据
4. 调用库存服务检查库存
5. 创建订单记录
6. 锁定商品库存
7. 创建支付单
8. 发送订单创建事件
9. 返回订单信息给客户端

### 3.2 支付流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as 网关
    participant P as 支付服务
    participant O as 订单服务
    participant MQ as 消息队列
    participant T as 第三方支付

    C->>G: 发起支付请求
    G->>P: 转发支付请求
    P->>P: 校验订单支付状态
    P->>T: 调用第三方支付
    T-->>P: 返回支付结果
    
    alt 支付成功
        P->>O: 更新订单状态
        P->>MQ: 发送支付成功事件
        P-->>C: 返回支付成功
    else 支付失败
        P->>MQ: 发送支付失败事件
        P-->>C: 返回支付失败
    end
```

流程步骤描述：
1. 客户端发起支付请求
2. 支付服务校验订单状态
3. 调用第三方支付接口
4. 接收支付结果
5. 更新订单状态
6. 发送支付结果事件
7. 返回支付结果给客户端

## 4. 数据库设计

### 4.1 ER图

```mermaid
erDiagram
    ORDER ||--o{ ORDER_ITEM : contains
    ORDER ||--o{ ORDER_PAYMENT : has
    ORDER ||--o{ ORDER_LOGISTICS : has
    ORDER ||--o{ ORDER_REFUND : has
    ORDER {
        bigint id PK
        string order_no
        bigint user_id
        decimal total_amount
        decimal pay_amount
        int status
        timestamp create_time
        timestamp update_time
    }
    ORDER_ITEM {
        bigint id PK
        bigint order_id FK
        bigint product_id
        int quantity
        decimal price
        decimal amount
    }
    ORDER_PAYMENT {
        bigint id PK
        bigint order_id FK
        string payment_no
        decimal amount
        int payment_type
        int status
        timestamp pay_time
    }
    ORDER_LOGISTICS {
        bigint id PK
        bigint order_id FK
        string logistics_no
        string company_code
        int status
        timestamp ship_time
    }
    ORDER_REFUND {
        bigint id PK
        bigint order_id FK
        string refund_no
        decimal amount
        int status
        timestamp refund_time
    }
```

### 4.2 表设计

```sql
-- 订单主表
CREATE TABLE `t_order` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no` varchar(32) NOT NULL COMMENT '订单号',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
    `pay_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
    `status` tinyint(4) NOT NULL COMMENT '订单状态：0-待付款，1-已付款，2-已发货，3-已完成，4-已取消',
    `remark` varchar(255) DEFAULT NULL COMMENT '订单备注',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';

-- 订单明细表
CREATE TABLE `t_order_item` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` bigint(20) NOT NULL COMMENT '订单ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `product_name` varchar(100) NOT NULL COMMENT '商品名称',
    `quantity` int(11) NOT NULL COMMENT '购买数量',
    `price` decimal(10,2) NOT NULL COMMENT '商品单价',
    `amount` decimal(10,2) NOT NULL COMMENT '商品总价',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

-- 支付记录表
CREATE TABLE `t_order_payment` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` bigint(20) NOT NULL COMMENT '订单ID',
    `payment_no` varchar(32) NOT NULL COMMENT '支付流水号',
    `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
    `payment_type` tinyint(4) NOT NULL COMMENT '支付方式：1-微信，2-支付宝，3-银联',
    `status` tinyint(4) NOT NULL COMMENT '支付状态：0-待支付，1-支付成功，2-支付失败',
    `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_payment_no` (`payment_no`),
    KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';

-- 物流信息表
CREATE TABLE `t_order_logistics` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` bigint(20) NOT NULL COMMENT '订单ID',
    `logistics_no` varchar(32) NOT NULL COMMENT '物流单号',
    `company_code` varchar(20) NOT NULL COMMENT '物流公司编码',
    `company_name` varchar(50) NOT NULL COMMENT '物流公司名称',
    `status` tinyint(4) NOT NULL COMMENT '物流状态：0-待发货，1-已发货，2-已签收',
    `receiver_name` varchar(50) NOT NULL COMMENT '收货人姓名',
    `receiver_phone` varchar(20) NOT NULL COMMENT '收货人电话',
    `receiver_address` varchar(255) NOT NULL COMMENT '收货地址',
    `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_logistics_no` (`logistics_no`),
    KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物流信息表';

-- 退款记录表
CREATE TABLE `t_order_refund` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` bigint(20) NOT NULL COMMENT '订单ID',
    `refund_no` varchar(32) NOT NULL COMMENT '退款单号',
    `amount` decimal(10,2) NOT NULL COMMENT '退款金额',
    `reason` varchar(255) NOT NULL COMMENT '退款原因',
    `status` tinyint(4) NOT NULL COMMENT '退款状态：0-待处理，1-退款成功，2-退款失败',
    `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_no` (`refund_no`),
    KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';
```

## 5. 接口设计

### 5.1 接口规范

#### 请求格式
```json
{
    "requestId": "唯一请求ID",
    "timestamp": "请求时间戳",
    "data": {
        // 请求数据
    }
}
```

#### 响应格式
```json
{
    "code": "响应码",
    "message": "响应消息",
    "data": {
        // 响应数据
    },
    "requestId": "对应请求ID"
}
```

### 5.2 核心接口

#### 5.2.1 创建订单
- 接口路径：`/api/v1/orders`
- 请求方式：POST
- 请求参数：
```json
{
    "userId": "用户ID",
    "items": [{
        "productId": "商品ID",
        "quantity": "购买数量"
    }],
    "address": {
        "receiverName": "收货人",
        "receiverPhone": "联系电话",
        "receiverAddress": "收货地址"
    },
    "couponId": "优惠券ID",
    "remark": "订单备注"
}
```
- 响应参数：
```json
{
    "orderNo": "订单号",
    "totalAmount": "订单总额",
    "payAmount": "应付金额",
    "status": "订单状态"
}
```

#### 5.2.2 订单支付
- 接口路径：`/api/v1/payments`
- 请求方式：POST
- 请求参数：
```json
{
    "orderNo": "订单号",
    "paymentType": "支付方式",
    "amount": "支付金额"
}
```
- 响应参数：
```json
{
    "paymentNo": "支付流水号",
    "paymentUrl": "支付跳转链接"
}
```

#### 5.2.3 订单发货
- 接口路径：`/api/v1/orders/{orderNo}/ship`
- 请求方式：POST
- 请求参数：
```json
{
    "logisticsNo": "物流单号",
    "companyCode": "物流公司编码"
}
```
- 响应参数：
```json
{
    "success": true,
    "shipTime": "发货时间"
}
```

## 6. 非功能设计

### 6.1 性能设计

#### 6.1.1 缓存策略
```java
@Service
public class OrderCacheService {
    @Autowired
    private RedisTemplate redisTemplate;
    
    // 订单缓存key前缀
    private static final String ORDER_KEY_PREFIX = "order:";
    
    // 缓存订单信息
    public void cacheOrder(Order order) {
        String key = ORDER_KEY_PREFIX + order.getOrderNo();
        redisTemplate.opsForValue().set(key, order, 24, TimeUnit.HOURS);
    }
    
    // 获取缓存订单
    public Order getOrder(String orderNo) {
        String key = ORDER_KEY_PREFIX + orderNo;
        return (Order) redisTemplate.opsForValue().get(key);
    }
}
```

#### 6.1.2 索引优化
```sql
-- 订单查询索引优化
CREATE INDEX idx_user_status_time ON t_order (user_id, status, create_time);
CREATE INDEX idx_status_time ON t_order (status, create_time);
```

### 6.2 安全设计

#### 6.2.1 接口安全
```java
@Aspect
@Component
public class SecurityAspect {
    
    @Around("@annotation(securityCheck)")
    public Object checkSecurity(ProceedingJoinPoint point, SecurityCheck securityCheck) {
        // 1. 验证token
        String token = getToken();
        if (!validateToken(token)) {
            throw new SecurityException("Invalid token");
        }
        
        // 2. 验证权限
        if (!checkPermission(securityCheck.value())) {
            throw new SecurityException("No permission");
        }
        
        return point.proceed();
    }
}
```

#### 6.2.2 数据加密
```java
@Component
public class EncryptionUtil {
    
    private static final String AES_KEY = "your-secret-key";
    
    // 敏感数据加密
    public String encrypt(String data) {
        // AES加密实现
        return encryptedData;
    }
    
    // 敏感数据解密
    public String decrypt(String encryptedData) {
        // AES解密实现
        return decryptedData;
    }
}
```

### 6.3 容灾设计

#### 6.3.1 服务熔断
```java
@Service
public class OrderService {
    
    @HystrixCommand(fallbackMethod = "createOrderFallback",
        commandProperties = {
            @HystrixProperty(name = "circuitBreaker.requestVolumeThreshold", value = "20"),
            @HystrixProperty(name = "circuitBreaker.errorThresholdPercentage", value = "50"),
            @HystrixProperty(name = "circuitBreaker.sleepWindowInMilliseconds", value = "5000")
        })
    public Order createOrder(OrderDTO orderDTO) {
        // 创建订单业务逻辑
    }
    
    public Order createOrderFallback(OrderDTO orderDTO) {
        // 降级处理逻辑
    }
}
```

## 7. 部署架构

### 7.1 物理架构设计

```mermaid
graph TB
    subgraph 负载均衡层
        LB1[Nginx-1]
        LB2[Nginx-2]
    end

    subgraph 应用服务器集群
        APP1[应用节点-1]
        APP2[应用节点-2]
        APP3[应用节点-3]
    end

    subgraph 数据库集群
        DB1[主库]
        DB2[从库-1]
        DB3[从库-2]
    end

    subgraph 缓存集群
        REDIS1[Redis主]
        REDIS2[Redis从-1]
        REDIS3[Redis从-2]
    end

    subgraph 消息队列集群
        MQ1[RabbitMQ-1]
        MQ2[RabbitMQ-2]
        MQ3[RabbitMQ-3]
    end

    LB1 & LB2 --> APP1 & APP2 & APP3
    APP1 & APP2 & APP3 --> DB1
    DB1 --> DB2 & DB3
    APP1 & APP2 & APP3 --> REDIS1
    REDIS1 --> REDIS2 & REDIS3
    APP1 & APP2 & APP3 --> MQ1 & MQ2 & MQ3
```

## 8. 监控与运维

### 8.1 监控指标

| 指标类型 | 指标名称 | 指标描述 | 告警阈值 |
|---------|---------|---------|---------|
| 业务指标 | order_create_qps | 订单创建QPS | >1000 |
| 业务指标 | order_pay_success_rate | 支付成功率 | <95% |
| 系统指标 | cpu_usage | CPU使用率 | >80% |
| 系统指标 | memory_usage | 内存使用率 | >80% |
| 系统指标 | disk_usage | 磁盘使用率 | >80% |
| 接口指标 | api_response_time | 接口响应时间 | >200ms |
| 接口指标 | api_error_rate | 接口错误率 | >1% |

### 8.2 运维策略

#### 8.2.1 部署策略
- 采用蓝绿部署
- 使用Jenkins实现CI/CD
- 配置中心实现配置热更新

#### 8.2.2 扩容策略
- 应用服务器：CPU使用率>70%触发水平扩容
- 数据库：TPS>5000触发读写分离
- 缓存：内存使用率>70%触发分片扩容

#### 8.2.3 备份策略
- 数据库：每日全量备份，每小时增量备份
- 配置信息：实时备份
- 日志文件：按天归档，保留30天

## 9. 附录

### 9.1 术语表

| 术语 | 描述 |
|-----|------|
| QPS | 每秒查询率 |
| TPS | 每秒事务处理量 |
| CI/CD | 持续集成/持续部署 |
| DDD | 领域驱动设计 |

### 9.2 依赖清单

```xml
<dependencies>
    <!-- Spring Cloud -->
    <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        <version>2.2.7.RELEASE</version>
    </dependency>
    
    <!-- Spring Boot -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>2.6.3</version>
    </dependency>
    
    <!-- Database -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>8.0.28</version>
    </dependency>
    
    <!-- Cache -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>2.6.3</version>
    </dependency>
    
    <!-- MQ -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-amqp</artifactId>
        <version>2.6.3</version>
    </dependency>
</dependencies>
``` 