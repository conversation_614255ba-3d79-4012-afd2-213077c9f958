# 基础镜像
FROM  repository.weifu.com.cn/library/common/eclipse-temurin:11-jre
# author
MAINTAINER <EMAIL>

# 挂载目录
VOLUME /home/<USER>/weifu
# 创建目录
RUN mkdir -p /home/<USER>/weifu/weifu-srm-biz-bff-internal

# 指定路径
WORKDIR /home/<USER>/weifu/weifu-srm-biz-bff-internal

# 复制jar文件到路径
COPY weifu-srm-biz-bff-internal/weifu-srm-biz-bff-internal-start/target/weifu-srm-biz-bff-internal-start.jar /home/<USER>/weifu/weifu-srm-biz-bff-internal/weifu-srm-biz-bff-internal-start.jar

COPY ./skywalking/agent/ /agent/
COPY ./weifu-srm-biz-bff-internal/weifu-srm-biz-bff-internal-start/filebeat/ /filebeat/
RUN tar -xvf /filebeat/filebeat-7.17.4-linux-x86_64.tar.gz -C /filebeat \
  && mv /filebeat/filebeat-7.17.4-linux-x86_64 /filebeat/filebeat-7.17.4 \
  && rm -rf /filebeat/filebeat-7.17.4-linux-x86_64.tar.gz \
  && chmod +x /filebeat/filebeat_start.sh

ENV TZ=Asia/Shanghai \
    SW_AGENT_NAME="$SW_AGENT_NAME" \
    SW_AGENT_COLLECTOR_BACKEND_SERVICES="$SW_AGENT_COLLECTOR_BACKEND_SERVICES"
ENV LANG C.UTF-8

EXPOSE 80
# 启动网关服务
# ENTRYPOINT ["java","-jar","weifu-srm-biz-bff-internal-start.jar"]
CMD /filebeat/filebeat_start.sh && java -javaagent:/agent/skywalking-agent.jar -jar weifu-srm-biz-bff-internal-start.jar