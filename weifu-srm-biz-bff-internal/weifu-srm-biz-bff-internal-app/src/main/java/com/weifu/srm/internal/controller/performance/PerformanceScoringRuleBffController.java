package com.weifu.srm.internal.controller.performance;

import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.log.annotation.OperationLog;
import com.weifu.srm.log.constants.BehaviorConstants;
import com.weifu.srm.supplier.performance.api.PerformanceScoringRuleApi;
import com.weifu.srm.supplier.performance.request.rule.PerformanceScoringRuleEditReqDTO;
import com.weifu.srm.supplier.performance.response.PerformanceScoringRuleRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 绩效指标算分规则 前端控制器
 * @Date 2024-09-20
 */
@Slf4j
@Validated
@Api(tags = "绩效指标算分规则")
@RequiredArgsConstructor
@RestController
public class PerformanceScoringRuleBffController {

    private final PerformanceScoringRuleApi performanceScoringRuleApi;

    @ApiOperation("查看绩效指标算分规则")
    @GetMapping("/performance/score/rule/detail")
    ApiResponse<List<PerformanceScoringRuleRespDTO>> detail() {
        return performanceScoringRuleApi.detail();
    }

    @OperationLog(behaviorCode = BehaviorConstants.MODIFY_PERFORMANCE_INDICATOR_SCORING_RULE)
    @ApiOperation("修改绩效指标算分规则")
    @PostMapping("/performance/score/rule/edit")
    ApiResponse<Void> edit(@RequestBody @Valid PerformanceScoringRuleEditReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return performanceScoringRuleApi.edit(reqDTO);
    }
}

