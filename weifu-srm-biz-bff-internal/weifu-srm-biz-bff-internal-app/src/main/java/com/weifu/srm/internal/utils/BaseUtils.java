package com.weifu.srm.internal.utils;

import cn.hutool.core.collection.CollUtil;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.util.Collection;

@Slf4j
public class BaseUtils {

    public static void batchDownloadFile(Response bizRes, HttpServletResponse response) {
        try {
            response.setContentType(getHeader(bizRes, "Content-Type"));
            response.setHeader("Content-Disposition", getHeader(bizRes, "Content-Disposition"));
            response.setHeader("Pragma","no-cache");
            Response.Body body = bizRes.body();
            InputStream in = body.asInputStream();
            try( BufferedInputStream bufferedIn = new BufferedInputStream(in);
                 BufferedOutputStream bufferedOutput = new BufferedOutputStream(response.getOutputStream())){
                IOUtils.copy(bufferedIn, bufferedOutput);
                bufferedOutput.flush();
            }catch (Exception e){
                log.error("导出文件异常", e);
                throw new RuntimeException(e);
            } finally {
                IOUtils.closeQuietly(in);
            }
        } catch (Exception e) {
            log.error("导出文件异常", e);
            throw new RuntimeException(e);
        }
    }


    public static String getHeader(Response response, String headerKey) {
        Collection<String> headerVals = response.headers().get(headerKey);
        if (CollUtil.isEmpty(headerVals)) {
            return null;
        }
        return headerVals.toString().replace("[", "").replace("]", "");
    }


    public static HttpServletResponse getHttpServletResponse(){
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        return attributes.getResponse();
    }
}
