package com.weifu.srm.internal.controller.cio;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.google.common.collect.Lists;
import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.log.annotation.OperationLog;
import com.weifu.srm.log.constants.BehaviorConstants;
import com.weifu.srm.log.enums.ActionTypeEnum;
import com.weifu.srm.supplier.api.SupplierBasicApi;
import com.weifu.srm.supplier.cio.api.SupplierQualityAuditTaskApi;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualityAuditTaskApproveReqDTO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualityAuditTaskPageReqDTO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualityAuditTaskReportPageReqDTO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualityAuditTaskResultReqDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskExcelRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskImportRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskReportRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskRespDTO;
import com.weifu.srm.supplier.response.SupplierBasicSimpleInfoRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核任务 前端控制器
 * @Version 1.0
 */
@Slf4j
@Validated
@Api(tags = "供应商质量审核任务")
@RequiredArgsConstructor
@RestController
public class SupplierQualityAuditTaskBffController extends ExportController {
    private final SupplierQualityAuditTaskApi supplierQualityAuditTaskApi;
    private final SupplierBasicApi supplierBasicApi;

    @ApiOperation("供应商质量审核任务-分页查询")
    @PostMapping("/supplier/cio/quality/audit/task/page")
    ApiResponse<PageResponse<SupplierQualityAuditTaskRespDTO>> page(@RequestBody @Valid SupplierQualityAuditTaskPageReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        return supplierQualityAuditTaskApi.page(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.EXPORT_SUPPLIER_QUALITY_AUDIT_TASK, actionType = ActionTypeEnum.EXPORT)
    @ApiOperation("导出供应商质量审核任务")
    @PostMapping("/supplier/cio/quality/audit/task/export")
    ApiResponse<Void> export(@RequestBody @Valid SupplierQualityAuditTaskPageReqDTO reqDTO) {
        HttpServletResponse response = getHttpServletResponse();
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        ApiResponse<List<SupplierQualityAuditTaskExcelRespDTO>> apiResponse = supplierQualityAuditTaskApi.listByQuery(reqDTO);
        export("供应商质量审核任务", apiResponse.getData(), SupplierQualityAuditTaskExcelRespDTO.class, response);
        return null;
    }

    @ApiOperation("供应商质量审核任务-列表查询")
    @PostMapping("/supplier/cio/quality/audit/task/listByQuery")
    ApiResponse<List<SupplierQualityAuditTaskExcelRespDTO>> listByQuery(@RequestBody @Valid SupplierQualityAuditTaskPageReqDTO reqDTO) {
        return supplierQualityAuditTaskApi.listByQuery(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.SUBMIT_SUPPLIER_QUALITY_AUDIT_TASK_RESULTS, businessNo = "#reqDTO.id")
    @ApiOperation("提交供应商质量审核任务结果")
    @PostMapping("/supplier/cio/quality/audit/task/submitResult")
    ApiResponse<Void> submitResult(@RequestBody @Valid SupplierQualityAuditTaskResultReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return supplierQualityAuditTaskApi.submitResult(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.REVIEW_SUPPLIER_QUALITY_AUDIT_TASK_RESULTS, businessNo = "#reqDTO.id")
    @ApiOperation("审核供应商质量审核任务结果")
    @PostMapping("/supplier/cio/quality/audit/task/approveResult")
    ApiResponse<Void> approveResult(@RequestBody @Valid SupplierQualityAuditTaskApproveReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return supplierQualityAuditTaskApi.approveResult(reqDTO);
    }

    @ApiOperation("供应商质量审核任务-报表分页查询")
    @PostMapping("/supplier/cio/quality/audit/task/pageReport")
    ApiResponse<PageResponse<SupplierQualityAuditTaskReportRespDTO>> pageReport(@RequestBody @Valid SupplierQualityAuditTaskReportPageReqDTO reqDTO) {
        return supplierQualityAuditTaskApi.pageReport(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.EXPORT_SUPPLIER_QUALITY_AUDIT_TASK_RESULTS, actionType = ActionTypeEnum.EXPORT)
    @ApiOperation("导出供应商质量审核任务结果")
    @PostMapping("/supplier/cio/quality/audit/task/exportReport")
    ApiResponse<Void> exportReport(@RequestBody @Valid SupplierQualityAuditTaskReportPageReqDTO reqDTO) {
        HttpServletResponse response = getHttpServletResponse();
        reqDTO.setPageSize(NO_PAGE_SIZE);
        ApiResponse<PageResponse<SupplierQualityAuditTaskReportRespDTO>> apiResponse = supplierQualityAuditTaskApi.pageReport(reqDTO);
        export("供应商质量审核任务报表", apiResponse.getData(), SupplierQualityAuditTaskReportRespDTO.class, response);
        return null;
    }

    @ApiOperation("从Excel解析供应商质量审核任务")
    @PostMapping(value = "/supplier/cio/quality/audit/task/import", consumes = "multipart/form-data")
    ApiResponse<List<SupplierQualityAuditTaskImportRespDTO>> importData(@RequestPart(value = "file") MultipartFile file) {
        List<SupplierQualityAuditTaskImportRespDTO> dtoList = Lists.newArrayList();
        try {
            EasyExcelFactory.read(file.getInputStream(), SupplierQualityAuditTaskImportRespDTO.class,
                    new PageReadListener<SupplierQualityAuditTaskImportRespDTO>(
                            objList -> {
                                List<SupplierQualityAuditTaskImportRespDTO> rowList = validData(objList);
                                if (CollectionUtils.isNotEmpty(rowList)) {
                                    dtoList.addAll(rowList);
                                }
                            }
                    )).sheet().doRead();
        } catch (Exception e) {
            log.error("read excel failed.", e);
        }
        return ApiResponse.success(dtoList);
    }

    @GetMapping("/supplier/cio/quality/audit/task/listSupplierCodeByAuditPlanNo")
    ApiResponse<List<String>> listSupplierCodeByAuditPlanNo(@RequestParam("auditPlanNo") String auditPlanNo) {
        return supplierQualityAuditTaskApi.listSupplierCodeByAuditPlanNo(auditPlanNo);
    }

    private List<SupplierQualityAuditTaskImportRespDTO> validData(List<SupplierQualityAuditTaskImportRespDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        List<String> supplierCodeList = dtoList.stream().map(SupplierQualityAuditTaskImportRespDTO::getSupplierCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        ApiResponse<List<SupplierBasicSimpleInfoRespDTO>> apiResponse = supplierBasicApi.listBySupplierCodes(supplierCodeList);
        List<SupplierBasicSimpleInfoRespDTO> respList = apiResponse.getData();
        if (CollectionUtils.isEmpty(apiResponse.getData())) {
            respList = Lists.newArrayList();
        }
        Map<String, SupplierBasicSimpleInfoRespDTO> respMap = respList.stream().collect(Collectors.toMap(SupplierBasicSimpleInfoRespDTO::getSapSupplierCode, obj -> obj, (k1, k2) -> k2));
        List<SupplierQualityAuditTaskImportRespDTO> resultList = Lists.newArrayList();
        for (SupplierQualityAuditTaskImportRespDTO dto : dtoList) {
            SupplierBasicSimpleInfoRespDTO supplier = respMap.get(dto.getSupplierCode());
            dto.setSuccess(respMap.containsKey(dto.getSupplierCode()) && isValid(dto));
            if (supplier != null && Boolean.TRUE.equals(dto.getSuccess())) {
                dto.setSupplierName(supplier.getSupplierName());
                dto.setSupplierShortName(supplier.getSupplierShortName());
            }
            resultList.add(dto);
        }
        return resultList;
    }

    private boolean isValid(SupplierQualityAuditTaskImportRespDTO dto) {
        if (dto == null) {
            return false;
        }
        try {
            Date predictCompleteDate = DateUtil.parse(dto.getPredictCompleteDate(), DatePattern.NORM_DATE_PATTERN);
            Date now = DateUtil.date();
            if (predictCompleteDate.before(now) && !DateUtil.isSameDay(predictCompleteDate, now)) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
        return !StringUtils.isBlank(dto.getSupplierCode()) &&
                !StringUtils.isBlank(dto.getAuditProductSeries()) &&
                !StringUtils.isBlank(dto.getAuditProductRange()) &&
                !StringUtils.isBlank(dto.getDivisionCode()) &&
                dto.getPredictCompleteDate() != null;
    }

}
