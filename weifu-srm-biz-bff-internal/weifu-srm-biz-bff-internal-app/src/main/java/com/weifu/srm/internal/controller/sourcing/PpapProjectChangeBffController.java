package com.weifu.srm.internal.controller.sourcing;

import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.log.annotation.OperationLog;
import com.weifu.srm.log.constants.BehaviorConstants;
import com.weifu.srm.sourcing.api.PpapProjectChangeApi;
import com.weifu.srm.sourcing.request.ppap.PpapProjectChangePageReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapProjectChangeSaveReqDTO;
import com.weifu.srm.sourcing.response.AttachmentRecordRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapProjectChangeDetailRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapProjectChangeRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 工程变更管理 前端控制器
 * @Version 1.0
 */
@Slf4j
@Validated
@Api(tags = "工程变更管理")
@RequiredArgsConstructor
@RestController
public class PpapProjectChangeBffController {
    private final PpapProjectChangeApi ppapProjectChangeApi;


    @ApiOperation("工程变更-分页查询")
    @PostMapping("/source/ppap/project/change/page")
    ApiResponse<PageResponse<PpapProjectChangeRespDTO>> page(@RequestBody @Valid PpapProjectChangePageReqDTO reqDTO) {
        reqDTO.setOperateBy(SecurityContextHolder.getUserId());
        return ppapProjectChangeApi.page(reqDTO);
    }

    @ApiOperation("工程变更-详情")
    @GetMapping("/source/ppap/project/change/detail")
    ApiResponse<PpapProjectChangeDetailRespDTO> detail(@RequestParam("id") Long id) {
        return ppapProjectChangeApi.detail(id);
    }

    @ApiOperation("工程变更-工程变更轨迹")
    @GetMapping("/source/ppap/project/change/listByReleaseNo")
    ApiResponse<PageResponse<PpapProjectChangeDetailRespDTO>> listByReleaseNo(PpapProjectChangePageReqDTO reqDTO) {
        return ppapProjectChangeApi.listByReleaseNo(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.CREATE_ENGINEERING_CHANGE, businessNo = "#reqDTO.releaseNo")
    @ApiOperation("创建工程变更")
    @PostMapping("/source/ppap/project/change/create")
    ApiResponse<Void> create(@RequestBody @Valid PpapProjectChangeSaveReqDTO reqDTO) {
        reqDTO.setOperateBy(SecurityContextHolder.getUserId());
        reqDTO.setOperateName(SecurityContextHolder.getRealName());
        return ppapProjectChangeApi.create(reqDTO);
    }

    @ApiOperation("查看批产放行批产放行前所有附件")
    @GetMapping("/source/ppap/project/change/listBeforeChangeAnnex")
    ApiResponse<List<AttachmentRecordRespDTO>> listBeforeChangeAnnex(@RequestParam("changeNo") String changeNo) {
        return ppapProjectChangeApi.listBeforeChangeAnnex(changeNo);
    }

}
