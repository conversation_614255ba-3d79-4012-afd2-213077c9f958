package com.weifu.srm.internal.controller.user;

import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.log.annotation.OperationLog;
import com.weifu.srm.log.constants.BehaviorConstants;
import com.weifu.srm.user.api.SysFactoryApi;
import com.weifu.srm.user.request.FactoryQueryReqDTO;
import com.weifu.srm.user.request.SysFactoryQueryReqDTO;
import com.weifu.srm.user.request.factory.SysFactoryAddDTO;
import com.weifu.srm.user.request.factory.SysFactoryPageDTO;
import com.weifu.srm.user.request.factory.SysFactoryUpdateDTO;
import com.weifu.srm.user.response.FactoryDivisionRespDTO;
import com.weifu.srm.user.response.SysFactoryRespDTO;
import com.weifu.srm.user.response.factory.SysFactoryInfoRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/20 13:18
 * @Description
 * @Version 1.0
 */
@Api(tags = "工厂")
@Slf4j
@RestController
@RequiredArgsConstructor
public class SysFactoryBFFController {
    private final SysFactoryApi factoryApi;
    @PostMapping("/factory/list")
    ApiResponse<List<SysFactoryRespDTO>> listByLikeFactoryCode(@RequestBody SysFactoryQueryReqDTO reqDTO){
        return factoryApi.listByLikeFactoryCode(reqDTO);
    }

    @ApiOperation("根据关键字模糊查询（工厂编码或名称）工厂信息")
    @PostMapping("/factory/queryFactory")
    ApiResponse<List<SysFactoryRespDTO>> queryFactory(@RequestBody FactoryQueryReqDTO param){
        return factoryApi.queryFactory(param);
    }

    @ApiOperation("根据工厂编码查询工厂与事业部信息")
    @PostMapping("/factory/listByFactoryCodes")
    ApiResponse<List<FactoryDivisionRespDTO>> listByFactoryCodes(@RequestBody List<String> factoryCodes){
        return factoryApi.listByFactoryCodes(factoryCodes);
    }

    @ApiOperation("工厂管理分页查询")
    @PostMapping("/factory/manager/pageFactory")
    ApiResponse<PageResponse<SysFactoryInfoRespDTO>> pageFactory(@RequestBody @Valid SysFactoryPageDTO reqDTO) {
        return factoryApi.pageFactory(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.ADD_FACTORY, businessNo = "#reqDTO.factoryCode")
    @ApiOperation("新增工厂")
    @PostMapping("/factory/manager/addFactory")
    ApiResponse<Long> addFactory(@RequestBody @Valid SysFactoryAddDTO reqDTO) {
        reqDTO.setOperateBy(SecurityContextHolder.getUserId());
        reqDTO.setOperateName(SecurityContextHolder.getRealName());
        return factoryApi.addFactory(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.EDIT_FACTORY, businessNo = "#reqDTO.factoryCode")
    @ApiOperation("修改工厂")
    @PostMapping("/factory/manager/updateFactory")
    ApiResponse<Long> updateFactory(@RequestBody @Valid SysFactoryUpdateDTO reqDTO) {
        reqDTO.setOperateBy(SecurityContextHolder.getUserId());
        reqDTO.setOperateName(SecurityContextHolder.getRealName());
        return factoryApi.updateFactory(reqDTO);
    }
}
