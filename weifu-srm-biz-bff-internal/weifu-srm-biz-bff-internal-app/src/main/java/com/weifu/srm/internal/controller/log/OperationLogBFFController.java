package com.weifu.srm.internal.controller.log;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.log.api.OperationLogApi;
import com.weifu.srm.log.request.OperationLogPageReqDTO;
import com.weifu.srm.log.response.OperationLogPageRespDTO;
import com.weifu.srm.log.response.OperationLogRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Api(tags = "操作日志管理")
@Slf4j
@RestController
@RequiredArgsConstructor
public class OperationLogBFFController {

    private final OperationLogApi operationLogApi;

    @ApiOperation("分页查询操作日志")
    @PostMapping("/log/operationLog/page")
    public ApiResponse<PageResponse<OperationLogPageRespDTO>> page(@RequestBody OperationLogPageReqDTO req) {
        return operationLogApi.page(req);
    }

    @ApiOperation("查询操作日志详情")
    @GetMapping("/log/operationLog/detail")
    public ApiResponse<OperationLogRespDTO> detail(@RequestParam("id") Long id) {
        return operationLogApi.detail(id);
    }

}
