package com.weifu.srm.internal.controller.user;

import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.security.auth.AuthUtil;
import com.weifu.srm.common.utils.SecurityUtils;
import com.weifu.srm.internal.utils.BaseUtils;
import com.weifu.srm.log.annotation.OperationLog;
import com.weifu.srm.log.constants.BehaviorConstants;
import com.weifu.srm.supplier.api.SupplierAccountApi;
import com.weifu.srm.supplier.request.account.SupplierAccountOptDTO;
import com.weifu.srm.supplier.request.account.SupplierAccountSaveReqDTO;
import com.weifu.srm.supplier.response.supplierAccount.SupplierAccountOpsResDTO;
import com.weifu.srm.supplier.response.supplierAccount.SupplierByUserResDTO;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.request.*;
import com.weifu.srm.user.response.BaseGroupedSysUserRespDTO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import com.weifu.srm.user.response.SysUserDetailRespDTO;
import com.weifu.srm.user.response.SysUserSimpleInfoRespDTO;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Api(tags = "系统用户服务")
@Slf4j
@RestController
@RequiredArgsConstructor
public class SysUserBFFController {

    private final SysUserApi sysUserServiceApi;

    @ApiOperation(value = "根据userId查询用户详情信息")
    @GetMapping("/user/details/{userId}")
    public ApiResponse<SysUserDetailRespDTO> findUserDetails(@PathVariable(value = "userId") Long userId) {
        return sysUserServiceApi.findUserDetails(userId);
    }

    @ApiOperation(value = "分页查询用户")
    @PostMapping("/user/page")
    public ApiResponse<PageResponse<BaseSysUserRespDTO>> findByPage(@RequestBody SysUserQueryReqDTO paramDTO) {
        //返回部门全路径名称 部门1|部门2|部门3 外部用户返回用户所属供应商 供应商名称1（供应商编码1）|供应商名称2（供应商编码2）
        return sysUserServiceApi.findByPage(paramDTO);
    }

    @ApiOperation(value = "批量查询用户")
    @PostMapping("/user/userPage")
    public ApiResponse<List<BaseSysUserRespDTO>> findUserPageByRoleId(@RequestBody SysRoleIdPageQueryParamReqDTO paramDTO) {
        return sysUserServiceApi.findUserPageByRoleId(paramDTO);
    }

    @ApiOperation(value = "用户姓名模糊查询")
    @PostMapping("/user/nameLike")
    public ApiResponse<List<BaseSysUserRespDTO>> nameLike(@RequestBody SysUserQueryReqDTO paramDTO) {
        return sysUserServiceApi.nameLike(paramDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.MODIFY_USER_ROLE)
    @ApiOperation("修改用户角色")
    @PostMapping("/user/editUserRole")
    public ApiResponse<Void> editUserRole(@RequestBody UpdateUserRoleReqDTO req) {
        req.setOperateBy(SecurityContextHolder.getUserId());
        req.setOperateName(SecurityContextHolder.getRealName());
        return sysUserServiceApi.editUserRole(req);
    }

    @OperationLog(behaviorCode = BehaviorConstants.MODIFY_USER_ROLE)
    @ApiOperation("批量修改用户角色")
    @PostMapping("/user/batchEditUserRole")
    public ApiResponse<Void> batchEditUserRole(@RequestBody BatchUpdateUserRoleReqDTO paramDTO) {
        paramDTO.setOperateBy(SecurityContextHolder.getUserId());
        paramDTO.setOperateName(SecurityContextHolder.getRealName());
        return sysUserServiceApi.batchEditUserRole(paramDTO);
    }

    @ApiOperation("模糊分页查询用户")
    @GetMapping({"/user/match/page"})
    ApiResponse<PageResponse<BaseSysUserRespDTO>> findByNameOrDomain(@RequestParam("keyword") String keyword,
                                                                     @RequestParam("pageNum") Integer pageNum,
                                                                     @RequestParam("pageSize") Integer pageSize) {
        return sysUserServiceApi.findByNameOrDomain(keyword, pageNum, pageSize);
    }

    @ApiOperation(value = "根据real name查询内部激活用户")
    @GetMapping("/user/findInternalActiveUser")
    public ApiResponse<List<BaseSysUserRespDTO>> findInternalActiveUser(@RequestParam("realName") String realName) {
        return sysUserServiceApi.findInternalActiveUser(realName);
    }

    @ApiOperation(value = "根据角色编码查询用户，并根据角色编码分组")
    @GetMapping("/user/findGroupedUserByRoleIds")
    public ApiResponse<List<BaseGroupedSysUserRespDTO>> findGroupedUserByRoleIds(@RequestParam("roleIds") String roleIds) {
        return sysUserServiceApi.findGroupedUserByRoleIds(roleIds);
    }

    /**
     * 品类管理新增修改-关键字查询通知人
     * 预留分页查询的位置
     * 若不传入limit限制条数 默认返回200条
     * 2024-11-26 返回域账号信息
     */
    @ApiOperation("品类管理新增修改-关键字查询通知人")
    @GetMapping("/user/findUserSimpleInfoByKeyword")
    ApiResponse<List<SysUserSimpleInfoRespDTO>> findUserSimpleInfoByKeyword(
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "limit", required = false)Long limit) {
        return sysUserServiceApi.findUserSimpleInfoByKeyword(keyword,limit);
    }

    @ApiOperation("根据条件查询用户")
    @PostMapping("/user/findUser")
    public ApiResponse<List<BaseSysUserRespDTO>> findUser(@RequestBody SysUserQueryReqDTO paramDTO) {
        return sysUserServiceApi.findUser(paramDTO);
    }

    @ApiOperation("根据realname和role查询bp系和品类系负责的事业部负责人")
    @GetMapping({"/user/findUserByRealNameAndRole"})
    public ApiResponse<List<BaseSysUserRespDTO>> findUserByRealNameAndRole(@RequestParam("realName") String realName,
                                                                           @RequestParam("role") String role) {
        return sysUserServiceApi.findUserByRealNameAndRole(realName, SecurityContextHolder.getUserId(), role);
    }

    @ApiOperation(value = "根据role和事业部查询同组角色人员")
    @PostMapping("/user/findUserByRole")
    ApiResponse<List<BaseSysUserRespDTO>> findUserByDivisionAndRole(@RequestBody SysUserFindByRoleReqDTO reqDTO) {
        reqDTO.setUserId(SecurityContextHolder.getUserId());
        return sysUserServiceApi.findUserByDivisionAndRole(reqDTO);
    }

    private final SupplierAccountApi supplierAccountApi;

    /**
     * 供应商多账户 - 编辑
     *
     * @return
     */
    @ApiOperation("编辑供应商账号")
    @PostMapping("/user/editSupplier")
    @OperationLog(behaviorCode = BehaviorConstants.EDIT_SUPPLIER_ACCOUNT, businessNo = "#req.orgCode")
    ApiResponse<SupplierAccountOpsResDTO> editAccountByOutUser(@RequestBody SupplierAccountSaveReqDTO req, HttpServletRequest request) {
        ApiResponse<SupplierAccountOpsResDTO> opsResDTOApiResponse = supplierAccountApi.editAccountByOutUser(req);
        if (opsResDTOApiResponse.getSucc()) {
            // 退出当前用户
            if (opsResDTOApiResponse.getSucc() && opsResDTOApiResponse.getData().getIsLogout()) {
                String token = SecurityUtils.getToken(request);
                AuthUtil.logoutByToken(token);
            }
            return ApiResponse.success();
        } else {
            return ApiResponse.fail(opsResDTOApiResponse.getCode(), opsResDTOApiResponse.getMsg());
        }
    }

    @ApiOperation("解绑操作下，外部用户关联的供应商信息")
    @PostMapping("/user/outUserSupplier")
    ApiResponse<List<SupplierByUserResDTO>> outUserSupplier(@RequestBody SupplierAccountOptDTO req) {
        req.setOperationUserId(SecurityContextHolder.getUserId());
        req.setOperationUser(SecurityContextHolder.getRealName());
        return supplierAccountApi.outUserSupplier(req);
    }

    @ApiOperation("外部用解绑供应商")
    @PostMapping("/user/outUserUntieSupplier")
    ApiResponse<Void> outUserUntieSupplier(@RequestBody SupplierAccountOptDTO req) {
        req.setOperationUserId(SecurityContextHolder.getUserId());
        req.setOperationUser(SecurityContextHolder.getRealName());
        return supplierAccountApi.outUserUntieSupplier(req);
    }

    @ApiOperation("删除外部用户")
    @PostMapping("/user/outUserDel")
    ApiResponse<Void> outUserDel(@RequestBody SupplierAccountOptDTO req) {
        req.setOperationUserId(SecurityContextHolder.getUserId());
        req.setOperationUser(SecurityContextHolder.getRealName());
        return supplierAccountApi.outUserDel(req);
    }

    @ApiOperation("新增外部用户")
    @PostMapping("/user/outUserAdd")
    ApiResponse<Void> outUserAdd(@RequestBody SupplierAccountOptDTO req) {
        req.setOperationUserId(SecurityContextHolder.getUserId());
        req.setOperationUser(SecurityContextHolder.getRealName());
        return supplierAccountApi.outUserAdd(req);
    }

    @ApiOperation(value = "导出用户角色")
    @PostMapping("/user/exportUserRole")
    ApiResponse<Void> exportUserRole(@RequestBody SysUserQueryReqDTO param){
        HttpServletResponse response = BaseUtils.getHttpServletResponse();
        Response bizRes = sysUserServiceApi.exportUserRole(param);
        BaseUtils.batchDownloadFile(bizRes, response);
        return null;
    }
}
