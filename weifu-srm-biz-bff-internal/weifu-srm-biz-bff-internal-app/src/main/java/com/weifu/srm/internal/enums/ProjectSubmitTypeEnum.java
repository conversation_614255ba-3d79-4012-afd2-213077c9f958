package com.weifu.srm.internal.enums;

public enum ProjectSubmitTypeEnum {

    PROJECT_SUBMIT_PURCHASE("PROJECT_PURCHASE_BP", "采购员"),
    PROJECT_SUBMIT_PURCHASE_LEADER("PROJECT_PURCHASE_BP_MASTER", "采购组长"),
    PROJECT_SUBMIT_CATEGORY("CPE", "品类管理员"),
    PROJECT_SUBMIT_CATEGORY_LEADER( "CPE_MASTER", "品类组长");


    private final String key;
    private final String value;

    private ProjectSubmitTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static ProjectSubmitTypeEnum statOf(String key) {
        for (ProjectSubmitTypeEnum projecSubmitTypeEnum : values()){
            if (projecSubmitTypeEnum.getKey().equals(key))
                return projecSubmitTypeEnum;
        }
        return null;
    }
}
