package com.weifu.srm.internal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.internal.service.SupplierBasicInfoExportService;
import com.weifu.srm.masterdata.api.CurrencyApi;
import com.weifu.srm.masterdata.api.DictDataApi;
import com.weifu.srm.masterdata.response.CurrencyRespDTO;
import com.weifu.srm.masterdata.response.DictDataShowResDTO;
import com.weifu.srm.supplier.api.QualificationChangeWeifuApi;
import com.weifu.srm.supplier.api.SupplierBasicApi;
import com.weifu.srm.supplier.constants.ServiceConstants;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.performance.api.computation.PerformanceComputationResultApi;
import com.weifu.srm.supplier.performance.enums.ScoringRuleCycleEnum;
import com.weifu.srm.supplier.performance.response.computation.ComputationInfoResDTO;
import com.weifu.srm.supplier.request.QualificationChangeQueryBasicReqDTO;
import com.weifu.srm.supplier.request.SupplierExportReqDTO;
import com.weifu.srm.supplier.request.SupplierGetAllHistoryGradeReqDTO;
import com.weifu.srm.supplier.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierBasicInfoExportServiceImpl implements SupplierBasicInfoExportService  {

    public static final String QUERY_RESULT = "供应商360查询结果";
    public static final String BASIC = "基本信息";
    public static final String USER = "人员信息";
    public static final String FINANCIAL = "财务信息";
    public static final String ASSOCIATION = "关联方信息";
    public static final String CERTIFICATION = "资质证书信息";
    public static final String CATEGORY = "准入品类";
    public static final String PERFORMANCE = "历史绩效";
    public static final String HISTORY_GRADE = "历史分级";
    public static final String CONTRACT = "协议签署信息";
    public static final String LIFECYCLE = "生命周期";
    public static final String EXPORT_ERROR = "export.supplier.contract.export.error";

    private final SupplierBasicApi supplierBasicApi;
    private final QualificationChangeWeifuApi qualificationChangeWeifuApi;
    private final DictDataApi dictDataApi;
    private final CurrencyApi currencyApi;
    private final LocaleMessage localeMessage;
    private final PerformanceComputationResultApi computationResultApi;

    @Override
    public void export(SupplierExportReqDTO reqDTO, ExcelWriter writer) {
        int num = 0;
        ApiResponse<PageResponse<SupplierBasicInfoListRespDTO>> pageResponseApiResponse = supplierBasicApi.searchSupplierBasicInfoList(reqDTO);
        PageResponse<SupplierBasicInfoListRespDTO> data = pageResponseApiResponse.getData();
        List<Long> supplierIds = data.getList().stream().map(SupplierBasicInfoListRespDTO::getId).collect(Collectors.toList());
        List<String> supplierCodeList= data.getList().stream().map(SupplierBasicInfoListRespDTO::getSapSupplierCode).collect(Collectors.toList());
        reqDTO.setSupplierCodes(supplierCodeList);
        reqDTO.setSupplierIds(supplierIds);
        // 列表信息导出
        num = listInfo(reqDTO, writer, supplierIds, num);
        // 基本信息导出
        num = baseInfo(reqDTO, writer, num);
        // 人员导出
        num = userInfo(reqDTO, writer, data, num);
        // 财务导出
        num = financialInfo(reqDTO, writer, num);
        // 关联方导出
        num = associationInfo(reqDTO, writer, num);
        // 资质证书导出
        num = certificationInfo(reqDTO, writer, num);
        // 品类导出
        num = categoryInfo(reqDTO, writer, supplierCodeList, num);
        // 绩效导出
        num = supplierHistoryScore(reqDTO, writer, num);
        // 历史分级导出
        num = historyGrade(reqDTO, writer, supplierCodeList, num);
        // 协议信息导出
        num = contractInfo(reqDTO, writer, supplierCodeList, num);
        // 生命周期导出
        lifecycle(reqDTO, writer, supplierIds, num);
    }

    private int supplierHistoryScore(SupplierExportReqDTO reqDTO, ExcelWriter writer, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsPerformance()) && reqDTO.getIsPerformance() == 1) {
            List<SupplierGetHistoryPerformanceRespDTO> result = new ArrayList<>();
            ApiResponse<List<ComputationInfoResDTO>> scoreList = computationResultApi.findSupplierHistoryScoreList(reqDTO.getSupplierCodes());
            List<ComputationInfoResDTO> computationInfoResDTOList = scoreList.getData();
            if (CollUtil.isNotEmpty(computationInfoResDTOList)) {
                for (ComputationInfoResDTO dto : computationInfoResDTOList){
                    log.debug("ComputationInfoResDTO {}",dto);
                    SupplierGetHistoryPerformanceRespDTO historyPerformanceRespDTO = new SupplierGetHistoryPerformanceRespDTO();
                    historyPerformanceRespDTO.setSupplierName(dto.getSupplierName());
                    historyPerformanceRespDTO.setSupplierCode(dto.getSupplierCode());
                    historyPerformanceRespDTO.setPerformanceType(ScoringRuleCycleEnum.getNameByCode(dto.getPerformanceType()));
                    historyPerformanceRespDTO.setSupplierScore(dto.getSupplierScore());
                    historyPerformanceRespDTO.setFullScore(dto.getFullScore());
                    if(ObjectUtil.isNotNull(dto.getScoringRate())){
                        historyPerformanceRespDTO.setScoringRate(dto.getScoringRate().toString());
                    }
                    historyPerformanceRespDTO.setPublishTime(dto.getPublishTime());
                    historyPerformanceRespDTO.setThemes(dto.getThemes());

                    result.add(historyPerformanceRespDTO);
                }
            }
            try {
                log.info("绩效导出============>{}", result);
                WriteSheet sheet = EasyExcel.writerSheet(num, PERFORMANCE).build();
                sheet.setClazz(SupplierGetHistoryPerformanceRespDTO.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export association list log error:",e);
                throw new BizFailException("export association list log  fail");
            }
        }
        return num;
    }

    private int contractInfo(SupplierExportReqDTO reqDTO, ExcelWriter writer, List<String> supplierCodeList, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsFramework()) && reqDTO.getIsFramework() == 1) {
            List<SupplierGetContractDetailRespDTO.FrameworkContractDetail> result = new ArrayList<>();
            supplierCodeList.forEach(supplierCode -> {
                SupplierGetAllHistoryGradeReqDTO supplierGetAllHistoryGradeReqDTO = new SupplierGetAllHistoryGradeReqDTO();
                supplierGetAllHistoryGradeReqDTO.setSupplierCode(supplierCode);
                ApiResponse<SupplierGetContractDetailRespDTO> apiResponse = supplierBasicApi.getContractDetail(supplierGetAllHistoryGradeReqDTO);
                if (ObjectUtil.isNotNull(apiResponse.getData().getFrameworkContractDetailList())) {
                    result.addAll(apiResponse.getData().getFrameworkContractDetailList());
                }
            });
            try {
                log.info("协议签署导出数据为============>{}", result);
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, CONTRACT).build();
                sheet.setClazz(SupplierGetContractDetailRespDTO.FrameworkContractDetail.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export contract list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
        return num;
    }

    private void lifecycle(SupplierExportReqDTO reqDTO, ExcelWriter writer, List<Long> supplierIds, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsLifeCycle()) && reqDTO.getIsLifeCycle() == 1) {
            List<SupplierGetLifeCycleRespDTO> result = new ArrayList<>();
            supplierIds.forEach(supplierId -> {
                ApiResponse<List<SupplierGetLifeCycleRespDTO>> listApiResponse = supplierBasicApi.getLifeCycle(supplierId);
                if (ObjectUtil.isNotNull(listApiResponse.getData())) {
                    result.addAll(listApiResponse.getData());
                }
            });
            try {
                log.info("生命周期导出数据为============>{}", result);
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, LIFECYCLE).build();
                sheet.setClazz(SupplierGetLifeCycleRespDTO.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export lifecycle list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
    }

    private int historyGrade(SupplierExportReqDTO reqDTO, ExcelWriter writer, List<String> supplierCodeList, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsHistoryGrade()) && reqDTO.getIsHistoryGrade() == 1) {
            List<SupplierGetAllHistoryGradeRespDTO> result = new ArrayList<>();
            supplierCodeList.forEach(supplierCode -> {
                SupplierGetAllHistoryGradeReqDTO supplierGetAllHistoryGradeReqDTO = new SupplierGetAllHistoryGradeReqDTO();
                supplierGetAllHistoryGradeReqDTO.setSupplierCode(supplierCode);
                ApiResponse<List<SupplierGetAllHistoryGradeRespDTO>> listApiResponse = supplierBasicApi.getAllHistoryGrade(supplierGetAllHistoryGradeReqDTO);
                if (ObjectUtil.isNotNull(listApiResponse.getData())) {
                    result.addAll(listApiResponse.getData());
                }
            });
            // 分级类型
            ApiResponse<Map<String, List<DictDataShowResDTO>>> gradeTypeDictResp = dictDataApi.getDictDataList(ServiceConstants.SUPPLIER_GRADE_TYPE, LocaleContextHolder.getLocale().toString());
            // 将字典值换成中文
            if (Boolean.TRUE.equals(gradeTypeDictResp.getSucc())){
                Map<String, List<DictDataShowResDTO>> gradeTypeDicDataMap = gradeTypeDictResp.getData();
                List<DictDataShowResDTO> gradeTypeDictList = gradeTypeDicDataMap.get(ServiceConstants.SUPPLIER_GRADE_TYPE);
                result.forEach(r-> gradeTypeDictList.stream()
                        .filter(a -> Objects.equals(a.getDataValue(), r.getSupplierGradeAfter()))
                        .findFirst().ifPresent(k->r.setSupplierGradeAfter(k.getDataShowName())));
            }
            try {
                log.info("历史分级导出数据为============>{}", result);
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, HISTORY_GRADE).build();
                sheet.setClazz(SupplierGetAllHistoryGradeRespDTO.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export history grade list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
        return num;
    }

    private int categoryInfo(SupplierExportReqDTO reqDTO, ExcelWriter writer, List<String> supplierCodeList, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsCategory()) && reqDTO.getIsCategory() == 1) {
            List<SupplierGetAllCategoryRespDTO> result = new ArrayList<>();
            supplierCodeList.forEach(supplierCode -> {
                categoryInfoOther(supplierCode, result);
            });
            try {
                log.info("品类导出数据为============>{}", result);
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, CATEGORY).build();
                sheet.setClazz(SupplierGetAllCategoryRespDTO.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export category list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
        return num;
    }

    private void categoryInfoOther(String supplierCode, List<SupplierGetAllCategoryRespDTO> result) {
        SupplierGetAllHistoryGradeReqDTO supplierGetAllHistoryGradeReqDTO = new SupplierGetAllHistoryGradeReqDTO();
        supplierGetAllHistoryGradeReqDTO.setSupplierCode(supplierCode);
        ApiResponse<List<SupplierGetAllCategoryRespDTO>> apiResponse = supplierBasicApi.getAllCategory(supplierGetAllHistoryGradeReqDTO);
        if (ObjectUtil.isNotNull(apiResponse.getData())) {
            result.addAll(apiResponse.getData());
        }
        for (SupplierGetAllCategoryRespDTO supplierGetAllCategoryRespDTO : result) {
            if(supplierGetAllCategoryRespDTO == null){
                continue;
            }
            SupplierCategoryStatusEnum supplierCategoryStatusEnum = SupplierCategoryStatusEnum.getByCode(supplierGetAllCategoryRespDTO.getCategoryStatus());
            supplierGetAllCategoryRespDTO.setCategoryStatusDesc(supplierCategoryStatusEnum == null ? null : supplierCategoryStatusEnum.getChineseName());
        }
    }

    private int certificationInfo(SupplierExportReqDTO reqDTO, ExcelWriter writer, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsCertification()) && reqDTO.getIsCertification() == 1) {
            List<QualificationExportCertificationRespDTO> result = new ArrayList<>();
            Map<String, DictDataShowResDTO> dictMap = new HashMap<>();
            try {
                ApiResponse<Map<String, List<DictDataShowResDTO>>> dictListResp = dictDataApi.getDictDataList(ServiceConstants.TYPE_ACCREDITATION, LocaleContextHolder.getLocale().toString());
                if (Boolean.TRUE.equals(dictListResp.getSucc())) {
                    Map<String, List<DictDataShowResDTO>> dicDataMap = dictListResp.getData();
                    List<DictDataShowResDTO> dictList = dicDataMap.get(ServiceConstants.TYPE_ACCREDITATION);
                    dictMap = dictList.stream()
                            .collect(Collectors.toMap(DictDataShowResDTO::getDataValue,v->v ,(v1, v2) -> v1));
                }
            }catch (Exception e){
                log.error("查询字典失败失败");
            }

            Map<String, DictDataShowResDTO> finalDictMap = dictMap;

            reqDTO.getSupplierIds().forEach(supplierId -> {
                QualificationChangeQueryBasicReqDTO qualificationChangeQueryBasicReqDTO = new QualificationChangeQueryBasicReqDTO();
                qualificationChangeQueryBasicReqDTO.setSupplierId(supplierId);
                ApiResponse<List<QualificationChangeQueryCertificationRespDTO>> listApiResponse = qualificationChangeWeifuApi.queryCertificationWeifu(qualificationChangeQueryBasicReqDTO);
                List<QualificationChangeQueryCertificationRespDTO> data = listApiResponse.getData();
                if (CollUtil.isNotEmpty(data)) {
                    for (QualificationChangeQueryCertificationRespDTO dto : data){
                        QualificationExportCertificationRespDTO certificationDTO = BeanUtil.copyProperties(dto, QualificationExportCertificationRespDTO.class);
                        DictDataShowResDTO dictDataShowResDTO = finalDictMap.get(dto.getCertificationType());
                        certificationDTO.setCertificationType(dictDataShowResDTO.getDataShowName());
                        certificationDTO.setHasQualityCertification(ObjectUtil.equals(YesOrNoEnum.YES.getCode(),dto.getHasQualityCertification())?YesOrNoEnum.YES.getDesc():YesOrNoEnum.NO.getDesc());
                        result.add(certificationDTO);
                    }
                }
            });
            try {
                log.info("资质证书导出数据为============>{}", result);
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, CERTIFICATION).build();
                sheet.setClazz(QualificationExportCertificationRespDTO.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export certificate list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
        return num;
    }

    private int associationInfo(SupplierExportReqDTO reqDTO, ExcelWriter writer, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsAssociation()) && reqDTO.getIsAssociation() == 1) {
            List<QualificationChangeQueryAssociationRespDTO> result = new ArrayList<>();
            reqDTO.getSupplierIds().forEach(supplierId -> {
                QualificationChangeQueryBasicReqDTO qualificationChangeQueryBasicReqDTO = new QualificationChangeQueryBasicReqDTO();
                qualificationChangeQueryBasicReqDTO.setSupplierId(supplierId);
                ApiResponse<QualificationChangeQueryAssociationRespDTO> apiResponse = qualificationChangeWeifuApi.queryAssociationWeifu(qualificationChangeQueryBasicReqDTO);
                if (ObjectUtil.isNotNull(apiResponse.getData())) {
                    result.add(apiResponse.getData());
                }
            });
            result.forEach(r -> r.setIsWeifuRelatedPartyDesc(YesOrNoEnum.YES.equalsCode(r.getIsWeifuRelatedParty())?YesOrNoEnum.YES.getDesc() : YesOrNoEnum.NO.getDesc()));
            try {
                log.info("关联方导出数据为============>{}", result);
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, ASSOCIATION).build();
                sheet.setClazz(QualificationChangeQueryAssociationRespDTO.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export association list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
        return num;
    }

    private int financialInfo(SupplierExportReqDTO reqDTO, ExcelWriter writer, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsFinancial()) && reqDTO.getIsFinancial() == 1) {
            ApiResponse<Map<String, List<DictDataShowResDTO>>> bankMessage = dictDataApi.getDictDataList(ServiceConstants.TYPE_BANK_SERVICE,LocaleContextHolder.getLocale().toString());
            List<DictDataShowResDTO> dictDataShowResDTOS = bankMessage.getData().get(ServiceConstants.TYPE_BANK_SERVICE);

            List<QualificationChangeQueryFinancialRespDTO> result = new ArrayList<>();
            reqDTO.getSupplierIds().forEach(supplierId -> {
                QualificationChangeQueryBasicReqDTO qualificationChangeQueryBasicReqDTO = new QualificationChangeQueryBasicReqDTO();
                qualificationChangeQueryBasicReqDTO.setSupplierId(supplierId);
                ApiResponse<List<QualificationChangeQueryFinancialRespDTO>> listApiResponse = qualificationChangeWeifuApi.queryFinancialWeifu(qualificationChangeQueryBasicReqDTO);
                if (ObjectUtil.isNotNull(listApiResponse.getData())) {
                    result.addAll(listApiResponse.getData());
                }
            });
            result.forEach(r-> dictDataShowResDTOS.stream()
                    .filter(a -> Objects.equals(a.getDataValue(), r.getBankAccountType()))
                    .findFirst().ifPresent(k->r.setBankAccountType(k.getDataShowName())));
            try {
                log.info("财务导出数据为============>{}", result);
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, FINANCIAL).build();
                sheet.setClazz(QualificationChangeQueryFinancialRespDTO.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export financial list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
        return num;
    }

    private int userInfo(SupplierExportReqDTO reqDTO, ExcelWriter writer, PageResponse<SupplierBasicInfoListRespDTO> data, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsUser()) && reqDTO.getIsUser() == 1) {
            List<QualificationChangeQueryUserRespDTO> result = new ArrayList<>();
            reqDTO.getSupplierIds().forEach(supplierId -> {
                userInfoOther(data, supplierId, result);
            });
            result.forEach(this::setRoleType);
            try {
                log.info("用户导出数据为============>{}", result);
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, USER).build();
                sheet.setClazz(QualificationChangeQueryUserRespDTO.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export user list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
        return num;
    }

    private void userInfoOther(PageResponse<SupplierBasicInfoListRespDTO> data, Long supplierId, List<QualificationChangeQueryUserRespDTO> result) {
        Optional<SupplierBasicInfoListRespDTO> supplierInfo = data.getList().stream().filter(dto -> Objects.equals(dto.getId(), supplierId)).findFirst();
        QualificationChangeQueryBasicReqDTO qualificationChangeQueryBasicReqDTO = new QualificationChangeQueryBasicReqDTO();
        qualificationChangeQueryBasicReqDTO.setSupplierId(supplierId);
        ApiResponse<List<QualificationChangeQueryUserRespDTO>> apiResponse = qualificationChangeWeifuApi.queryUserWeifu(qualificationChangeQueryBasicReqDTO);
        if (ObjectUtil.isNotNull(apiResponse.getData())) {
            List<QualificationChangeQueryUserRespDTO> userData = apiResponse.getData();
            for (QualificationChangeQueryUserRespDTO user : userData) {
                supplierInfo.ifPresent(r -> {
                    user.setSupplierCode(r.getSapSupplierCode());
                    user.setSupplierName(r.getSupplierName());
                });
            }
            result.addAll(userData);
        }
    }

    private void setRoleType(QualificationChangeQueryUserRespDTO r) {
        if (r.getType().equals("LEGAL_PERSON")) {
            r.setType("法人");
        }
        if (r.getType().equals("REGISTER_CONTACT")) {
            r.setType("注册联系人");
        }
        if (r.getType().equals("BUSINESS_CONTACT")) {
            r.setType("商务联系人");
        }
        if (r.getType().equals("QUALITY_CONTACT")) {
            r.setType("质量联系人");
        }
        if (r.getType().equals("FINANCE_CONTACT")) {
            r.setType("财务联系人");
        }
    }

    private int baseInfo(SupplierExportReqDTO reqDTO, ExcelWriter writer, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsBasic()) && reqDTO.getIsBasic() == 1) {
            List<QualificationChangeQueryBasicRespDTO> result = new ArrayList<>();
            reqDTO.getSupplierIds().forEach(supplierId -> {
                QualificationChangeQueryBasicReqDTO qualificationChangeQueryBasicReqDTO = new QualificationChangeQueryBasicReqDTO();
                qualificationChangeQueryBasicReqDTO.setSupplierId(supplierId);
                ApiResponse<QualificationChangeQueryBasicRespDTO> apiResponse = qualificationChangeWeifuApi.queryBasicWeifu(qualificationChangeQueryBasicReqDTO);
                QualificationChangeQueryBasicRespDTO qualificationChangeQueryBasicRespDTOApiResponse = apiResponse.getData();
                if (ObjectUtil.isNotNull(qualificationChangeQueryBasicRespDTOApiResponse)) {
                    result.add(qualificationChangeQueryBasicRespDTOApiResponse);
                }
            });
            // 机构性质
            ApiResponse<Map<String, List<DictDataShowResDTO>>> characterStructureDictResp = dictDataApi.getDictDataList(ServiceConstants.CHARACTER_STRUCTURE,LocaleContextHolder.getLocale().toString());
            // 企业类型
            ApiResponse<Map<String, List<DictDataShowResDTO>>> businessTypeDictResp = dictDataApi.getDictDataList(ServiceConstants.TYPE_BUSINESS,LocaleContextHolder.getLocale().toString());
            // 查询币种信息
            ApiResponse<List<CurrencyRespDTO>> listApiResponse = currencyApi.queryAllActiveCurrency();
            Map<String, String> codeMapNameForCurrency = listApiResponse.getData().stream().collect(Collectors.toMap(CurrencyRespDTO::getCurrencyCode, CurrencyRespDTO::getCurrencyName, (dto1, dto2) -> dto1));
            // 将字典值换成中文
            if (Boolean.TRUE.equals(characterStructureDictResp.getSucc()) && Boolean.TRUE.equals(businessTypeDictResp.getSucc())){
                Map<String, List<DictDataShowResDTO>> characterStructureDicDataMap = characterStructureDictResp.getData();
                List<DictDataShowResDTO> characterStructureDictList = characterStructureDicDataMap.get(ServiceConstants.CHARACTER_STRUCTURE);
                Map<String, List<DictDataShowResDTO>> businessTypeDicDataMap = businessTypeDictResp.getData();
                List<DictDataShowResDTO> businessTypeDictList = businessTypeDicDataMap.get(ServiceConstants.TYPE_BUSINESS);
                // 机构性质
                result.forEach(r-> characterStructureDictList.stream()
                        .filter(a -> Objects.equals(a.getDataValue(), r.getOrganizationNature()))
                        .findFirst().ifPresent(k->r.setOrganizationNature(k.getDataShowName())));
                // 企业类型
                result.forEach(r-> businessTypeDictList.stream()
                        .filter(a -> Objects.equals(a.getDataValue(), r.getEnterpriseType()))
                        .findFirst().ifPresent(k->r.setEnterpriseType(k.getDataShowName())));
                // 币种-是否境外机构
                result.forEach(r -> {
                            r.setRegisteredCurrency(codeMapNameForCurrency.get(r.getRegisteredCurrency()));
                            r.setOverseasDesc(YesOrNoEnum.YES.equalsCode(r.getIsOverseas())?"境外机构":"境内机构");
                        }
                );}
            try {
                log.info("基本信息导出数据为============>{}", result);
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, BASIC).build();
                sheet.setClazz(QualificationChangeQueryBasicRespDTO.class);
                writer.write(result, sheet);
                num++;
            } catch (Exception e) {
                log.error("export basic list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
        return num;
    }

    private int listInfo(SupplierExportReqDTO reqDTO, ExcelWriter writer, List<Long> supplierIds, int num) {
        if (ObjectUtil.isNotNull(reqDTO.getIsQueryResult()) && reqDTO.getIsQueryResult() == 1) {
            ApiResponse<List<SupplierBasicInfoListRespExportDTO>> result = supplierBasicApi.getDetailById(supplierIds);
            // 分级类型
            ApiResponse<Map<String, List<DictDataShowResDTO>>> gradeTypeDictResp = dictDataApi.getDictDataList(ServiceConstants.SUPPLIER_GRADE_TYPE,LocaleContextHolder.getLocale().toString());
            // 供应商状态
            ApiResponse<Map<String, List<DictDataShowResDTO>>> statusTypeDictResp = dictDataApi.getDictDataList(ServiceConstants.SUPPLIER_BASIC_STATUS,LocaleContextHolder.getLocale().toString());
            // 供应商标签
            ApiResponse<Map<String, List<DictDataShowResDTO>>> labelTypeDictResp = dictDataApi.getDictDataList(ServiceConstants.SUPPLIER_LABEL,LocaleContextHolder.getLocale().toString());
            // 将字典值换成中文
            if (Boolean.TRUE.equals(gradeTypeDictResp.getSucc()) && Boolean.TRUE.equals(statusTypeDictResp.getSucc()) && Boolean.TRUE.equals(labelTypeDictResp.getSucc())){
                Map<String, List<DictDataShowResDTO>> gradeTypeDicDataMap = gradeTypeDictResp.getData();
                List<DictDataShowResDTO> gradeTypeDictList = gradeTypeDicDataMap.get(ServiceConstants.SUPPLIER_GRADE_TYPE);
                Map<String, List<DictDataShowResDTO>> statusTypeDicDataMap = statusTypeDictResp.getData();
                List<DictDataShowResDTO> statusTypeDictList = statusTypeDicDataMap.get(ServiceConstants.SUPPLIER_BASIC_STATUS);
                Map<String, List<DictDataShowResDTO>> labelTypeDictDataMap = labelTypeDictResp.getData();
                List<DictDataShowResDTO> labelTypeDictList = labelTypeDictDataMap.get(ServiceConstants.SUPPLIER_LABEL);
                // 供应商分级
                result.getData().forEach(r-> gradeTypeDictList.stream()
                        .filter(a -> Objects.equals(a.getDataValue(), r.getSupplierClassification()))
                        .findFirst().ifPresent(k->r.setSupplierClassification(k.getDataShowName())));
                // 供应商状态
                result.getData().forEach(r-> statusTypeDictList.stream()
                        .filter(a -> Objects.equals(a.getDataValue(), r.getStatus()))
                        .findFirst().ifPresent(k->r.setStatus(k.getDataShowName())));
                checkTag(result, labelTypeDictList);
            }
            try {
                log.info("列表导出数据为============>{}", result.getData());
                WriteSheet sheet = EasyExcelFactory.writerSheet(num, QUERY_RESULT).build();
                sheet.setClazz(SupplierBasicInfoListRespExportDTO.class);
                writer.write(result.getData(), sheet);
                num++;
            } catch (Exception e) {
                log.error("export query result list log error:",e);
                throw new BizFailException(localeMessage.getMessage(EXPORT_ERROR));
            }
        }
        return num;
    }

    private static void checkTag(ApiResponse<List<SupplierBasicInfoListRespExportDTO>> result, List<DictDataShowResDTO> labelTypeDictList) {
        // Tag展示处理
        result.getData().forEach(r->
                labelTypeDictList.forEach(dictDataShowResDTO -> {
                    if(ObjectUtil.isNotEmpty(r.getTags())){
                        if (r.getTags().contains(dictDataShowResDTO.getDataValue())) {
                            r.setTags(r.getTags().replaceAll(dictDataShowResDTO.getDataValue(), dictDataShowResDTO.getDataShowName()));
                        }
                    }
                }));
        // 灰/黑名单展示处理
        result.getData().forEach(r -> {
            r.setGreyListStatusDesc(YesOrNoEnum.YES.equalsCode(r.getGreyListStatus())?YesOrNoEnum.YES.getDesc():YesOrNoEnum.NO.getDesc());
            r.setBlackListStatusDesc(YesOrNoEnum.YES.equalsCode(r.getBlackListStatus())?YesOrNoEnum.YES.getDesc():YesOrNoEnum.NO.getDesc());
        });
    }
}
