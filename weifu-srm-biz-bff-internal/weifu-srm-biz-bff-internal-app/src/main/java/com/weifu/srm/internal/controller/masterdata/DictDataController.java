package com.weifu.srm.internal.controller.masterdata;

import cn.hutool.core.collection.CollUtil;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.masterdata.api.DictDataApi;
import com.weifu.srm.masterdata.response.DictDataShowResDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/16 10:36
 * @Description
 * @Version 1.0
 */
@Slf4j
@Validated
@Api(tags = "字典表")
@RequiredArgsConstructor
@RestController
public class DictDataController {
    private final DictDataApi dictDataApi;
    private static final String APPLICABLE_TYPE = "APPLICABLE_TYPE";
    private static final String SAMPLE_CODE = "SAMPLE_SUPPLIER";
    private static final String TEMP_CODE = "TMP_SUPPLIER";
    @ApiOperation("适用类型")
    @RequestMapping(value = "/master/dict/getApplicableType", method = RequestMethod.GET)
    public ApiResponse<Map<String, List<DictDataShowResDTO>>> getApplicableType(){
        ApiResponse<Map<String, List<DictDataShowResDTO>>> response = dictDataApi.getDictDataList(APPLICABLE_TYPE, LocaleContextHolder.getLocale().toString());
        if (Boolean.TRUE.equals(response.getSucc()) && !CollUtil.isEmpty(response.getData())) {
            List<DictDataShowResDTO> list = response.getData().get(APPLICABLE_TYPE);
            List<DictDataShowResDTO> finalList = Optional.ofNullable(list)
                    .map(r->r.stream()
                            .filter(j->!SAMPLE_CODE.equals(j.getDataValue())
                                    && !TEMP_CODE.equals(j.getDataValue()))
                            .collect(Collectors.toList()))
                    .orElse(null);
            response.getData().put(APPLICABLE_TYPE,finalList);
        }
        return response;
    }
}
