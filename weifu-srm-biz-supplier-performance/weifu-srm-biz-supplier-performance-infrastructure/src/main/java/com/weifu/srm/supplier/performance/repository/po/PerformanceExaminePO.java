package com.weifu.srm.supplier.performance.repository.po;

import com.weifu.srm.mybatis.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * @Description 绩效考核
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("performance_examine")
public class PerformanceExaminePO extends BaseEntity {


    /**绩效编号*/
    private String performanceNo;

    /**绩效主题*/
    private String themes;

    /**绩效类型*/
    private String performanceType;

    /**考核周期-年*/
    private String examineCycleYear;

    /**考核周期-季度*/
    private String examineCycleQuarter;

    /**绩效状态*/
    private String status;

    /**采用的预计算编号*/
    private String preComputationNo;

    /**发布后的评价方案*/
    private String evaluateNo;

    /**发布时间*/
    private Date publishTime;

    /**绩效任务开始日期*/
    private Date performanceStartDate;

    /**绩效任务终止日期*/
    private Date performanceEndDate;

    /**计算额度*/
    private String calculateAmount;


}
