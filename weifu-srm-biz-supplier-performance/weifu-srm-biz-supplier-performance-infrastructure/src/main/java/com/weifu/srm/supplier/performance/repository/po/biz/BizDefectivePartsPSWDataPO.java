package com.weifu.srm.supplier.performance.repository.po.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 不良零件投诉 添加psw和三级品类后的时间表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_defective_parts_psw_data")
public class BizDefectivePartsPSWDataPO extends BaseEntity {

    /** 外部系统ID */
    private String externalId;
    /** 数据来源：FR - 帆软系统 */
    private String dataSource;
    /** 事业部/子公司 */
    private String divisionCode;
    /** 投诉日期 */
    private Date complaintDate;
    /** SAP10位料号 */
    private String materialCode;
    /** 零件名称 */
    private String partName;
    /** 供应商sap编码 */
    private String supplierCode;
    /** 供应商名称 */
    private String supplierName;
    /** 失效现象描述 */
    private String complaintFailureSituationResume;
    /** 问题/不符合描述 */
    private String complaintProblemDescription;
    /** 投诉数量(产线/客户发现) */
    private String partComplaintQty;
    /** 投诉点 */
    private String complaintDiscoverySite;
    /** 班组 */
    private String teamAndGroup;
    /** pu确认数量 */
    private Long puConfirmsNum;
    /** pu确认说明 */
    private String puConfirmsDesc;
    /** 上游系统单据号 */
    private String relationNo;

    /**
     * PSW签署时间
     */
    @ApiModelProperty(name = "PSW签署时间", notes = "")
    private Date pswTime;

    @ApiModelProperty("三级品类编码")
    private String threeCategoryCode;

    @ApiModelProperty("三级品类编码")
    private String twoCategoryCode;

    @ApiModelProperty("一级品类编码")
    private String oneCategoryCode;
}
