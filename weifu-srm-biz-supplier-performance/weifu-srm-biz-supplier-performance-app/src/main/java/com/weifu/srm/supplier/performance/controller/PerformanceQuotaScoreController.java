package com.weifu.srm.supplier.performance.controller;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.performance.api.PerformanceQuotaScoreApi;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScoreCreateReqDTO;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScorePageReqDTO;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScoreQueryReqDTO;
import com.weifu.srm.supplier.performance.request.EnableOrDisableStatusReqDTO;
import com.weifu.srm.supplier.performance.response.PerformanceQuotaScoreRespDTO;
import com.weifu.srm.supplier.performance.service.PerformanceQuotaScoreService;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * @Description 供应商绩效指标加减分配置 前端控制器
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Validated
@Api("供应商绩效指标加减分配置")
@RequiredArgsConstructor
@RestController
public class PerformanceQuotaScoreController implements PerformanceQuotaScoreApi {

    private final PerformanceQuotaScoreService performanceQuotaScoreService;

    @Override
    public ApiResponse<PageResponse<PerformanceQuotaScoreRespDTO>> page(PerformanceQuotaScorePageReqDTO reqDTO) {
        return ApiResponse.success(performanceQuotaScoreService.page(reqDTO));
    }

    @Override
    public ApiResponse<Long> create(PerformanceQuotaScoreCreateReqDTO reqDTO) {
        return ApiResponse.success(performanceQuotaScoreService.create(reqDTO));
    }

    @Override
    public ApiResponse<Void> changeStatus(EnableOrDisableStatusReqDTO reqDTO) {
        performanceQuotaScoreService.changeStatus(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<List<PerformanceQuotaScoreRespDTO>> selectList(PerformanceQuotaScoreQueryReqDTO reqDTO) {
        return ApiResponse.success(performanceQuotaScoreService.selectList(reqDTO));
    }
}

