package com.weifu.srm.supplier.performance.manager.computation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.supplier.performance.enums.IndicatorLogicStatusEnum;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.PreComputeQueueMapperService;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationPreComputeQueuePO;
import com.weifu.srm.supplier.performance.request.precomputation.PreComputationReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PreComputationQueueManager {

    private final PreComputeQueueMapperService queueMapperService;
    private final PreComputationManager preComputationManager;
    private final LocaleMessage localeMessage;

    @Async
    public void runQueue(){
        log.info("开始预计算...");
        ComputationPreComputeQueuePO dutyNext = getNextDuty();
        String perComputationNo = null;
        if(ObjectUtil.isNull(dutyNext.getPerComputationNo())){
            log.info("开始预计算...没有找到等待中的计算任务");
            return;
        }else {
            perComputationNo = dutyNext.getPerComputationNo();
        }
        log.info("开始预计算, duty info is 【{}】", dutyNext);
        updateDutyToQueue(perComputationNo, IndicatorLogicStatusEnum.DOING);
        try {
            preComputationManager.preComputationScore(dutyNext);
            updateDutyToQueue(perComputationNo, IndicatorLogicStatusEnum.FINISH);
        }catch (Exception e){
            log.error("预计算异常, duty info is 【{}】", dutyNext,e);
            updateDutyToQueue(perComputationNo, IndicatorLogicStatusEnum.ERROR);
        }
        log.info("开始预计算,读取下一次计算任务...");
        runQueue();
    }

    @Async
    public void runHasErrorQueue(String performanceNo){
        log.info("开始重新计算...");
        ComputationPreComputeQueuePO dutyNext = new ComputationPreComputeQueuePO();
        dutyNext.setPerformanceNo(performanceNo);
        log.info("开始重新计算, duty info is 【{}】", dutyNext);
        try {
            preComputationManager.preComputationScore(dutyNext);
            updateDutyToQueue(dutyNext.getPerComputationNo(), IndicatorLogicStatusEnum.FINISH);
        }catch (Exception e){
            log.error("开始重新计算异常, duty info is 【{}】", dutyNext,e);
            throw new BizFailException(e.getMessage(),e);
        }
    }

    public boolean checkQueueHasDuty(){
        // 判断是否有其他正在进行计算中的任务
        log.info("检查正在进行中的计算任务...");
        List<ComputationPreComputeQueuePO> list = queueMapperService.lambdaQuery()
                .eq(ComputationPreComputeQueuePO::getStatus, IndicatorLogicStatusEnum.DOING.getType())
                .list();
        log.info("正在进行中的计算任务...【{}】", list);
        return CollUtil.isNotEmpty(list);
    }

    private ComputationPreComputeQueuePO getNextDuty(){
        // 获取下一次的任务
        List<ComputationPreComputeQueuePO> list = queueMapperService.lambdaQuery()
                .eq(ComputationPreComputeQueuePO::getStatus, IndicatorLogicStatusEnum.NOT_STARTED.getType())
                .list();
        if (CollUtil.isEmpty(list)){
            log.info("没有读取到新预计算任务...");
            return new ComputationPreComputeQueuePO();
        }
        ComputationPreComputeQueuePO duty = list.get(0);
        log.info("读取到新预计算任务，duty is 【{}】", duty);
        return duty;
    }

    public void addDutyToQueue(PreComputationReqDTO duty){
        // 一个绩效考核-不能对同一个方案进行多次预计算
        // 获取下一次的任务
        Integer count = queueMapperService.lambdaQuery()
                .eq(ComputationPreComputeQueuePO::getStatus, IndicatorLogicStatusEnum.FINISH.getType())
                .eq(ComputationPreComputeQueuePO::getEvaluateNo, duty.getEvaluateNo())
                .eq(ComputationPreComputeQueuePO::getPerformanceNo, duty.getPerformanceNo())
                .count();
        if(count != 0){
            throw new BizFailException(localeMessage.getMessage("performance.evaluation.error"));
        }
        // 添加一个任务
        log.info("添加任务到队列，duty is 【{}】", duty);
        ComputationPreComputeQueuePO po = new ComputationPreComputeQueuePO();
        po.setPerComputationNo("PRE_"+ (new Date().getTime()));
        po.setEvaluateNo(duty.getEvaluateNo());
        po.setStatus(IndicatorLogicStatusEnum.NOT_STARTED.getType());
        po.setPerformanceNo(duty.getPerformanceNo());
        po.setPerformanceType(duty.getPerformanceType());
        queueMapperService.save(po);
    }

    @Async
    public void updateDutyToQueue(String perComputationNo, IndicatorLogicStatusEnum statusEnum){
        // 添加一个任务
        log.info("更新任务执行状态，duty is 【{}】,【{}】", perComputationNo, statusEnum.getType());

        queueMapperService.lambdaUpdate()
                .set(ComputationPreComputeQueuePO::getStatus, statusEnum.getType())
                .eq(ComputationPreComputeQueuePO::getPerComputationNo, perComputationNo)
                .set(ObjectUtil.equals(statusEnum, IndicatorLogicStatusEnum.DOING), ComputationPreComputeQueuePO::getStartTime, new Date())
                .set(ObjectUtil.equals(statusEnum, IndicatorLogicStatusEnum.FINISH),ComputationPreComputeQueuePO::getEndTime, new Date())
                .update();
    }

    private void removeDutyForQueue(PreComputationReqDTO duty){
        // 删除任务
        LambdaUpdateWrapper<ComputationPreComputeQueuePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ComputationPreComputeQueuePO::getPerComputationNo, duty.getPreComputationNo());
        queueMapperService.remove(wrapper);
        log.info("删除任务到队列中的任务，duty is 【{}】", duty);
    }
}
