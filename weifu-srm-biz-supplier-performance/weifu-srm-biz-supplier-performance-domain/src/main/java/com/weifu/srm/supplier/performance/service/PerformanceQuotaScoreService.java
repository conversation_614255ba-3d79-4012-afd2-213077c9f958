package com.weifu.srm.supplier.performance.service;

import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScoreCreateReqDTO;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScorePageReqDTO;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScoreQueryReqDTO;
import com.weifu.srm.supplier.performance.request.EnableOrDisableStatusReqDTO;
import com.weifu.srm.supplier.performance.response.PerformanceQuotaScoreRespDTO;

import java.util.List;

/**
 * <p>
 * 加减分项目维护-服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceQuotaScoreService {

    /**
     * 分页查询
     */
    PageResponse<PerformanceQuotaScoreRespDTO> page(PerformanceQuotaScorePageReqDTO reqDTO);

    /**
     * 新增
     */
    Long create(PerformanceQuotaScoreCreateReqDTO reqDTO);

    /**
     * 修改状态
     */
    void changeStatus(EnableOrDisableStatusReqDTO reqDTO);

    /**
     * 列表查询
     */
    List<PerformanceQuotaScoreRespDTO> selectList(PerformanceQuotaScoreQueryReqDTO reqDTO);
}
