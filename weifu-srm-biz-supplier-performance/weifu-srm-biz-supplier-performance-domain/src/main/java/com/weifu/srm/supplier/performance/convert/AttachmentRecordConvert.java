package com.weifu.srm.supplier.performance.convert;


import com.weifu.srm.supplier.performance.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.performance.request.AttachmentRecordReqDTO;
import com.weifu.srm.supplier.performance.response.AttachmentRecordRespDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 附件
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface AttachmentRecordConvert {
    List<AttachmentRecordRespDTO> toAttachmentMessageRespDTO(List<AttachmentRecordPO> poList);

    List<AttachmentRecordReqDTO> toAttachmentMessageReqDTO(List<AttachmentRecordRespDTO> respList);

}
