package com.weifu.srm.supplier.performance.response.order;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 质量指标数据
 * @Date 2024-09-20
 */
@Data
@ApiModel("质量指标数据")
public class PerformanceOrderQualityRespDTO  {

    @ExcelProperty(value = "编号")
    @ApiModelProperty("编号")
    private Long id;

    @ExcelProperty(value = "供应商编码")
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ExcelProperty(value = "供应商名称")
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ExcelProperty(value = "物料编码")
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ExcelProperty(value = "物料名称")
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ExcelProperty(value = "三级品类")
    @ApiModelProperty(value = "三级品类名称")
    private String categoryName;

    @ExcelProperty(value = "问题地点")
    @ApiModelProperty(value = "问题地点")
    private String problemLocationName;

    @ExcelProperty(value = "问题描述")
    @ApiModelProperty(value = "问题描述")
    private String problemDescription;

    @ExcelProperty(value = "反馈日期")
    @ApiModelProperty(value = "反馈日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date feedbackDate;

    @ExcelProperty(value = "数量")
    @ApiModelProperty(value = "数量")
    private Integer quantity;

    @ExcelProperty(value = "关闭日期")
    @ApiModelProperty(value = "关闭日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date closeDate;

    @ExcelProperty(value = "是否需要纳入绩效考核")
    @ApiModelProperty(value = "是否需要纳入绩效考核")
    private String isNeedExamineName;


    @ExcelProperty(value = "创建人名称")
    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ExcelProperty(value = "创建时间")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelIgnore
    @ApiModelProperty(value = "供应商名称英文")
    private String supplierNameEn;

    @ExcelIgnore
    @ApiModelProperty(value = "三级品类编码")
    private String categoryCode;

    @ExcelIgnore
    @ApiModelProperty(value = "三级品类英文名称")
    private String categoryNameEn;

    @ExcelIgnore
    @ApiModelProperty(value = "事业部|子公司编码")
    private String divisionId;

    @ExcelIgnore
    @ApiModelProperty(value = "事业部|子公司名称")
    private String divisionName;

    @ExcelIgnore
    @ApiModelProperty(value = "年月")
    private String yearMonthStr;

    @ExcelIgnore
    @ApiModelProperty(value = "供应商来料总（批次）数")
    private Integer incomingQuantity;

    @ExcelIgnore
    @ApiModelProperty(value = "威孚制造过程投诉（批次）数")
    private Integer complaintQuantity;

    @ExcelIgnore
    @ApiModelProperty(value = "问题地点名称")
    private String problemLocation;

    @ExcelIgnore
    @ApiModelProperty(value = "原因分析")
    private String causeAnalysis;

    @ExcelIgnore
    @ApiModelProperty(value = "改进措施")
    private String improvementMeasures;

    @ExcelIgnore
    @ApiModelProperty(value = "索赔进度")
    private String claimantProgress;

    @ExcelIgnore
    @ApiModelProperty(value = "处理进展及结论")
    private String processingProgress;

    @ExcelIgnore
    @ApiModelProperty(value = "材料牌号")
    private String materialGrade;

    @ExcelIgnore
    @ApiModelProperty(value = "炉号")
    private String heatNumber;

    @ExcelIgnore
    @ApiModelProperty(value = "采购技术标准")
    private String procurementStandards;

    @ExcelIgnore
    @ApiModelProperty(value = "问题性质")
    private String problemNature;

    @ExcelIgnore
    @ApiModelProperty(value = "是否索赔: 0否，1是")
    private Integer isClaimant;

    @ExcelIgnore
    @ApiModelProperty(value = "备注")
    private String remarks;

    @ExcelIgnore
    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ExcelIgnore
    @ApiModelProperty(value = "是否需要纳入绩效考核: 0否，1是")
    private Integer isNeedExamine;
}
