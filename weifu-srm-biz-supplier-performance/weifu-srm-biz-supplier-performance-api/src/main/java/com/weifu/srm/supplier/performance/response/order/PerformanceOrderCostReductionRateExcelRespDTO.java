package com.weifu.srm.supplier.performance.response.order;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 绩效工单-降本目标达成-目标降本率-excel
 * @Date 2024-09-20
 */
@Data
@ApiModel("绩效工单-降本目标达成-目标降本率-excel")
public class PerformanceOrderCostReductionRateExcelRespDTO {

    @ExcelProperty("供应商编码")
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ExcelProperty("供应商名称")
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ExcelProperty("目标降本率(%)")
    @ApiModelProperty(value = "目标降本率")
    private String costReductionRateStr;

    @ExcelProperty(value = "返利（元）")
    @ApiModelProperty(value = "返利")
    private String rebateAmountStr;

    @ExcelProperty(value = "技术降本（元）")
    @ApiModelProperty(value = "技术降本")
    private String costReductionAmountStr;

    @ApiModelProperty("是否成功")
    private Boolean success;
}
