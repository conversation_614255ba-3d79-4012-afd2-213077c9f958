package com.weifu.srm.supplier.performance.response.computation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ComputationInfoListResDTO {

    @ApiModelProperty(value = "评价方案编号")
    private String evaluateNo;

    @ApiModelProperty(value = "绩效主题")
    private String themes;

    @ApiModelProperty(value = "预计算完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

}
