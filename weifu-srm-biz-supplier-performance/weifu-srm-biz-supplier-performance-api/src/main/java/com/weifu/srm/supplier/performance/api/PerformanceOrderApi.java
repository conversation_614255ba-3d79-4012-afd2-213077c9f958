package com.weifu.srm.supplier.performance.api;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.performance.constants.ServiceConstants;
import com.weifu.srm.supplier.performance.request.*;
import com.weifu.srm.supplier.performance.request.order.*;
import com.weifu.srm.supplier.performance.response.ImportResultRespDTO;
import com.weifu.srm.supplier.performance.response.order.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 绩效工单 前端控制器
 * @Date 2024-09-20
 */
@Api("绩效工单")
@FeignClient(value = ServiceConstants.APPLICATION_NAME)
public interface PerformanceOrderApi {

    @ApiOperation("绩效工单分页查询")
    @PostMapping("/performance/order/page")
    ApiResponse<PageResponse<PerformanceOrderRespDTO>> page(@RequestBody @Valid PerformanceOrderPageReqDTO reqDTO);

    @ApiOperation("绩效工单数量统计查询")
    @GetMapping("/performance/order/statistics")
    ApiResponse<PerformanceOrderStatisticsRespDTO> statisticsByPerformanceNo(@RequestParam("performanceNo") String performanceNo);

    @ApiOperation("查看绩效工单详情")
    @GetMapping("/performance/order/detail")
    ApiResponse<PerformanceOrderDetailRespDTO> detail(@RequestParam("orderNo") String orderNo);

    @ApiOperation("提交绩效工单")
    @PostMapping("/performance/order/submit")
    ApiResponse<Void> submit(@RequestBody @Valid PerformanceOrderSubmitReqDTO reqDTO);

    @ApiOperation("核验绩效工单")
    @PostMapping("/performance/order/verification")
    ApiResponse<Void> verification(@RequestBody @Valid PerformanceOrderVerificationReqDTO reqDTO);

    @ApiOperation("退回绩效工单")
    @PostMapping("/performance/order/back")
    ApiResponse<Void> back(@RequestBody @Valid PerformanceIdReqDTO reqDTO);


    @ApiOperation("绩效工单日常服务意愿分页查询")
    @PostMapping("/performance/order/daily/willingness/page")
    ApiResponse<PageResponse<PerformanceOrderDailyWillingnessRespDTO>> pageDailyWillingness(@RequestBody @Valid PerformanceOrderNoPageReqDTO reqDTO);

    @ApiOperation("导入绩效工单日常服务意愿")
    @PostMapping(value = "/performance/order/daily/willingness/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<ImportResultRespDTO<PerformanceOrderDailyWillingnessExcelRespDTO>> importDailyWillingness(@RequestPart(value = "file") MultipartFile file,
                                                                                           @RequestParam(value = "orderNo") String orderNo,
                                                                                           @RequestParam(value = "operationBy") Long operationBy,
                                                                                           @RequestParam(value = "operationName") String operationName);


    @ApiOperation("绩效工单降本目标达成度供应商范围分页查询")
    @PostMapping("/performance/order/cost/reduction/supplier/page")
    ApiResponse<PageResponse<PerformanceOrderCostReductionSupplierRespDTO>> pageCostReductionSupplier(@RequestBody @Valid PerformanceOrderNoPageReqDTO reqDTO);

    @ApiOperation("绩效工单降本目标达成度降本物料清单分页查询")
    @PostMapping("/performance/order/cost/reduction/material/page")
    ApiResponse<PageResponse<PerformanceOrderCostReductionMaterialRespDTO>> pageCostReductionMaterial(@RequestBody @Valid PerformanceOrderNoPageReqDTO reqDTO);

    @ApiOperation("删除绩效工单降本目标达成度降本物料清单")
    @PostMapping("/performance/order/cost/reduction/material/delete")
    ApiResponse<Void> deleteCostReductionMaterial(@RequestBody @Valid PerformanceOrderNoReqDTO reqDTO);

    @ApiOperation("导入绩效工单降本目标达成度降本物料清单")
    @PostMapping(value = "/performance/order/cost/reduction/material/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<ImportResultRespDTO<OrderCostReductionMaterialExcelRespDTO>> importCostReductionMaterial(@RequestPart(value = "file") MultipartFile file,
                                                                                                         @RequestParam(value = "orderNo") String orderNo,
                                                                                                         @RequestParam(value = "operationBy") Long operationBy,
                                                                                                         @RequestParam(value = "operationName") String operationName);

    @ApiOperation("绩效工单降本目标达成度目标降本率分页查询")
    @PostMapping("/performance/order/cost/reduction/rate/page")
    ApiResponse<PageResponse<PerformanceOrderCostReductionRateRespDTO>> pageCostReductionRate(@RequestBody @Valid PerformanceOrderNoPageReqDTO reqDTO);

    @ApiOperation("修改绩效工单日常服务意愿")
    @PostMapping("/performance/order/daily/willingness/save")
    ApiResponse<Void> saveDailyWillingness(@RequestBody @Valid PerformanceOrderDailyWillingnessSaveReqDTO reqDTO);

    @ApiOperation("删除绩效工单降本目标达成度目标降本率")
    @PostMapping("/performance/order/cost/reduction/rate/delete")
    ApiResponse<Void> deleteCostReductionRate(@RequestBody @Valid PerformanceOrderNoReqDTO reqDTO);

    @ApiOperation("绩效工单OTS及时送样率供应商范围分页查询")
    @PostMapping("/performance/order/ots/supplier/page")
    ApiResponse<PageResponse<PerformanceOrderOtsSupplierRespDTO>> pageOtsSupplier(@RequestBody @Valid PerformanceOrderNoPageReqDTO reqDTO);

    @ApiOperation("导入绩效工单降本目标达成度目标降本率")
    @PostMapping(value = "/performance/order/cost/reduction/rate/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<ImportResultRespDTO<PerformanceOrderCostReductionRateExcelRespDTO>> importCostReductionRate(@RequestPart(value = "file") MultipartFile file,
                                                                                             @RequestParam(value = "orderNo") String orderNo,
                                                                                             @RequestParam(value = "operationBy") Long operationBy,
                                                                                             @RequestParam(value = "operationName") String operationName);


    @ApiOperation("绩效工单OTS及时送样率分页查询")
    @PostMapping("/performance/order/ots/material/page")
    ApiResponse<PageResponse<PerformanceOrderOtsMaterialRespDTO>> pageOtsMaterial(@RequestBody @Valid PerformanceOrderNoPageReqDTO reqDTO);

    @ApiOperation("删除绩效工单OTS及时送样率")
    @PostMapping("/performance/order/ots/material/delete")
    ApiResponse<Void> deleteOtsMaterial(@RequestBody @Valid PerformanceOrderNoReqDTO reqDTO);

    @ApiOperation("导入绩效工单OTS及时送样率")
    @PostMapping(value = "/performance/order/ots/material/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<ImportResultRespDTO<PerformanceOrderOtsMaterialExcelRespDTO>> importOtsMaterial(@RequestPart(value = "file") MultipartFile file,
                                                                                 @RequestParam(value = "orderNo") String orderNo,
                                                                                 @RequestParam(value = "operationBy") Long operationBy,
                                                                                 @RequestParam(value = "operationName") String operationName);


    @ApiOperation("绩效工单交付指标数据分页查询")
    @PostMapping("/performance/order/deliver/page")
    ApiResponse<PageResponse<PerformanceOrderDeliverRespDTO>> pageDeliverData(@RequestBody @Valid PerformanceOrderNoPageReqDTO reqDTO);

    @ApiOperation("删除绩效工单交付指标数据")
    @PostMapping("/performance/order/deliver/delete")
    ApiResponse<Void> deleteDeliverData(@RequestBody @Valid PerformanceIdReqDTO reqDTO);

    @ApiOperation("导入绩效工单交付指标数据")
    @PostMapping(value = "/performance/order/deliver/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<ImportResultRespDTO<PerformanceOrderDeliverExcelRespDTO>> importDeliverData(@RequestPart(value = "file") MultipartFile file,
                                                                             @RequestParam(value = "orderNo") String orderNo,
                                                                             @RequestParam(value = "operationBy") Long operationBy,
                                                                             @RequestParam(value = "operationName") String operationName);

    @ApiOperation("绩效工单质量指标数据分页查询")
    @PostMapping("/performance/order/quality/page")
    ApiResponse<PageResponse<PerformanceOrderQualityRespDTO>> pageQualityData(@RequestBody @Valid PerformanceOrderNoPageReqDTO reqDTO);

    @ApiOperation("刷新绩效工单质量指标数据")
    @PostMapping("/performance/order/quality/refresh")
    ApiResponse<Void> refreshQualityData(@RequestBody @Valid PerformanceOrderNoPageReqDTO reqDTO);

    @ApiOperation("统计待办的绩效工单")
    @GetMapping("/performance/order/statisticsTodoOrder")
    ApiResponse<Integer> statisticsTodoOrder(@SpringQueryMap OperatorBaseReqDTO reqDTO);
}

