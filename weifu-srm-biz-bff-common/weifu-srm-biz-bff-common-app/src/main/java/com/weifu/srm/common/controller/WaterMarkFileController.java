package com.weifu.srm.common.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.request.DownloadFileReqDTO;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.file.api.FileOnlyUploadApi;
import com.weifu.srm.file.request.BatchDownloadFileReqDTO;
import com.weifu.srm.sourcing.api.BusinessDesignationOrderApi;
import com.weifu.srm.sourcing.response.AttachmentRecordSimpleRespDTO;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Api(tags = "水印文件Controller")
@Slf4j
@RestController
@RequiredArgsConstructor
public class WaterMarkFileController {

    private final SysUserApi sysUserApi;
    private final BusinessDesignationOrderApi businessDesignationOrderApi;
    private final FileOnlyUploadApi fileOnlyUploadApi;

    @ApiOperation("获取文件下载的文件流并添加水印")
    @PostMapping({"file/get/stream/watermark"})
    public ResponseEntity<Void> getFileStreamWatermark(@RequestBody DownloadFileReqDTO req, HttpServletResponse response) {
        if (StringUtils.isEmpty(req.getOriginalName())) {
            req.setOriginalName(req.getFileName());
        }
        String watermark = buildWatermark(req.getFileName(), req.getSampleOrderNoWaterMark());
        Response bizRes = fileOnlyUploadApi.downloadFileWatermark(req.getFileName(), req.getOriginalName(), watermark);
        batchDownloadFile(bizRes, response);
        return null;
    }

    @ApiOperation("批量下载文件并添加水印")
    @PostMapping({"file/get/batch/watermark"})
    public ResponseEntity<Void> batchDownloadFileWatermark(@RequestBody List<BatchDownloadFileReqDTO> req, HttpServletResponse response) {
        if (CollectionUtils.isNotEmpty(req)) {
            req.forEach(o -> o.setWatermark(buildWatermark(o.getFileName(), "")));
        }
        Response bizRes = fileOnlyUploadApi.batchDownloadFile(req);
        batchDownloadFile(bizRes, response);
        return null;
    }

    @ApiOperation("获取文件下载的文件流并添加水印-图片和pdf")
    @PostMapping({"file/get/stream/download/watermark"})
    public ResponseEntity<Void> downloadFileStreamWatermark(@RequestBody BatchDownloadFileReqDTO req, HttpServletResponse response) {
        if (StringUtils.isEmpty(req.getOriginalName())) {
            req.setOriginalName(req.getFileName());
        }
        String watermark = addWatermarkSuffix(req.getWatermark());
        Response bizRes = fileOnlyUploadApi.downloadFileAddWatermark(req.getFileName(), req.getOriginalName(), watermark);
        batchDownloadFile(bizRes, response);
        return null;
    }

    private String addWatermarkSuffix(String watermark) {
        if (StringUtils.isBlank(watermark)) {
            return null;
        }
        String domain = getUserDomain(SecurityContextHolder.getUserId());
        String now = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN);
        return watermark + " " + domain + " " + now;
    }

    private void batchDownloadFile(Response bizRes, HttpServletResponse response) {
        try {
            response.setContentType(getHeader(bizRes, "Content-Type"));
            response.setHeader("Content-Disposition", getHeader(bizRes, "Content-Disposition"));
            response.setHeader("Pragma", "no-cache");
            Response.Body body = bizRes.body();
            InputStream in = body.asInputStream();
            try (BufferedInputStream bis = new BufferedInputStream(in);
                 BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream());) {
                IOUtils.copy(bis, bos);
                bos.flush();
            } catch (Exception e) {
                log.error("导出文件异常", e);
                throw new RuntimeException(e);
            } finally {
                IOUtils.closeQuietly(in);
            }
        } catch (Exception ex) {
            log.error("导出文件异常", ex);
            throw new RuntimeException(ex);
        }
    }

    private String getHeader(Response response, String headerKey) {
        Collection<String> headerVals = response.headers().get(headerKey);
        if (CollUtil.isEmpty(headerVals)) {
            return null;
        }
        return headerVals.toString().replace("[", "").replace("]", "");
    }

    private String buildWatermark(String fileName, String sampleOrderNo) {
        String domain = getUserDomain(SecurityContextHolder.getUserId());
        String now = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN);
        if (StringUtils.isNotBlank(sampleOrderNo)) {
            return domain + " " + now + " " + sampleOrderNo;
        } else {
            String supplierCode = getSupplierCode(fileName);
            return domain + " " + supplierCode + " " + now;
        }
    }

    private String getUserDomain(Long userId) {
        ApiResponse<BaseSysUserRespDTO> response = sysUserApi.findByUserId(userId);
        if (Objects.equals(Boolean.TRUE, response.getSucc())) {
            return response.getData().getUserName();
        }
        return "";
    }

    private String getSupplierCode(String fileName) {
        ApiResponse<AttachmentRecordSimpleRespDTO> response = businessDesignationOrderApi.queryNotificationFileByFileName(fileName);
        if (Objects.equals(Boolean.TRUE, response.getSucc())) {
            return response.getData().getBusinessSubNo();
        }
        return "";
    }
}
