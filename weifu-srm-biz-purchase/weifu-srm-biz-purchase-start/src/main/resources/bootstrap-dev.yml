spring:
  cloud:
    nacos:
      server-addr: ${NACOS_IP:************}:${NACOS_PORT:30048}
      config:
        namespace: srm-dev
        file-extension: yml
        prefix: ${spring.application.name}
        shared-configs:
          - dataId: common-config
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        namespace: srm-dev
        group: DEFAULT_GROUP
# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-dynamic-parameter: true
    enable-filter-multipart-api-method-type: POST
    enable-request-cache: true

# LOG_LEVEL用于控制输出到日志文件里的日志级别，在logback.xml中使用
LOG_LEVEL: DEBUG
logging:
  level:
    com.weifu: DEBUG
    org.springframework.kafka: DEBUG
    com.baomidou.mybatisplus: DEBUG
