package com.weifu.srm.purchase.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseUserReqDTO {
    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "用户真实姓名", hidden = true)
    private String userRealName;

    @ApiModelProperty(value = "操作时间", hidden = true)
    private Date operationTime;

    public BaseUserReqDTO(Long userId) {
        this.userId = userId;
    }

    public BaseUserReqDTO(Long userId, String userRealName) {
        this.userId = userId;
        this.userRealName = userRealName;
    }
}
