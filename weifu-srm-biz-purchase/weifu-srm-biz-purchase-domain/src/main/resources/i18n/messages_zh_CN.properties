## 公共
common.option.result.failure=操作失败
common.option.result.success=操作成功
common.option.result.version.error=数据已被更新，请刷新重试
common.query.result.failure=查询失败
common.query.result.success=查询成功
common.save.result.success=保存成功
common.submit.result.success=提交成功
common.query.result.not.find=查询的数据不存在
common.query.not.value=暂无数据
common.data.already.exists=记录已存在
common.params.cannot.be.empty={0}不能为空
common.template.is.inconformity=模板不匹配
common.data.is.empty=数据为空
common.data.is.duplicate=数据重复
common.params.failure=参数错误
common.update.result.not.find=修改的数据不存在
common.delete.result.fail=删除数据失败
## 信用额度
credit.limit.params.error=信用额度申请单参数错误
credit.limit.not.found=该信用额度申请单不存在

## 合作伙伴
new.code.not.exist=新编码不存在，请核实后重试
old.code.not.exist=原编码不存在，请核实后重试
new.code.status.not.effective=新编码的状态不是已生效，不能合并
this.partner.not.exist=当前合作伙伴不存在
partner.cannot.be.disabled=请检查合作伙伴{0}在合同、销售提单、供应商发货指令中的使用情况

## 商品主数据
product.data.null=商品主数据不存在，请确认
product.option.result.part.failure=部分数据操作失败，请刷新重试！
product.chinese.name.exists=中文名称已存在，请重新输入！
product.chinese.name.duplicate=中文名称有重复，请重新输入！
product.display.name.exists=显示名称已存在，请重新输入！
product.display.name.duplicate=显示名称有重复，请检查！
product.import.title=商品主数据
product.import.sheet=商品信息
product.excel.empty=excel内容为空，请填写后再导入!
product.history.not.find=未找到操作历史！
product.customs.multiple=海关编码存在多个，请检查！
product.customs.not.find=海关编码不存在，请检查！
product.group.category.not.find=集团产品分类不存在，请检查！
product.basic.unit.not.find=基本单位不存在，请检查！
product.value.added.taxRate.not.find=进项税不存在，请检查！
product.value.added.salesTaxRate.not.find=销项税不存在，请检查！
merge.query.result.not.find=要合并的商品明细编码不存在或状态不是已生效
product.cannot.be.disabled=请检查商品{0}子类状态及在合同、销售提单、业务库存中的使用情况
product.company.or.department.not.find=数据权限公司或部门不存在，请检查！

## 币种
currency.list.empty=币种列表为空
currency.code.cannot.be.empty=币种代码不能为空
currency.name.cannot.be.empty=币种名称不能为空
currency.code.cannot.be.duplicate=币种代码不能重复
currency.code.already.exists=币种代码{0}已经存在
currency.name.already.exists=币种名称{0}已经存在
currency.not.exist=币种不存在
currency.cannot.be.disabled=币种{0}是某个账套的本位币，不允许禁用

## 会计日历
calendar.list.empty=会计日历列表为空
calendar.code.cannot.be.empty=日历代码不能为空
calendar.type.cannot.be.empty=日历类型不能为空
calendar.desc.cannot.be.empty=日历说明不能为空
calendar.init.open.year=初始打开年度不能为空
calendar.code.cannot.be.duplicate=日历代码不能重复
calendar.code.already.exists=日历代码{0}已经存在
calendar.not.exist=日历不存在

## 账套
ledger.code.cannot.be.empty=分类账代码不能为空
ledger.name.cannot.be.empty=分类账名称不能为空
ledger.chart.cannot.be.empty=会计科目表不能为空
ledger.calendar.cannot.be.empty=会计日历不能为空
ledger.currency.code.cannot.be.empty=本位币不能为空
ledger.must.contain.legal=账套必须包含法人
ledger.code.already.exists=分类账代码{0}已经存在
ledger.not.exist=账套不存在
ledger.default.exchange.type.cannot.be.empty=默认汇率类型不能为空
ledger.init.open.period.cannot.be.empty=初始打开期间不能为空
ledger.legal.code.cannot.be.empty=法人主体代码不能为空
ledger.legal.name.cannot.be.empty=法人主体不能为空
ledger.legal.country.cannot.be.empty=国家(地区)不能为空
ledger.legal.credit.code.cannot.be.empty=统一社会信用代码不能为空
ledger.legal.address.cannot.be.empty=注册地址不能为空
ledger.legal.code.cannot.be.duplicate=法人主体不能重复
ledger.legal.code.already.exists=法人主体{0}已经存在

## 人员主数据
person.sequence.too.long=序列优先级不能大于999
person.id.exists=身份证号码重复，请查看对应人员数据

## 导入
excel.import.data.not.found=内容为空，请填写内容后再导入
excel.import.result.success=导入成功
excel.import.template.incorrect=导入的模板不正确
excel.import.analytic.anomaly=解析异常,请使用Excel标准模板
excel.import.result.failure=导入失败

## 权限
account.code.result.save=该员工账号已存在,请重新选择
application.code.result.delete=应用下面含有菜单不能删除
application.code.result.save=应用名称已存在

## 组织主数据
company.code.result.save=当前数据已被其他人修改，请刷新后重试
company.code.result.import.null=请填写数据
company.code.result.order=序列优先级已到最大值，请重新输入
company.code.result.check=公司名称已存在
nationality.not.find=国籍不存在
department.name.repeat=当前公司下部门名称重复
company.prohibit=当前公司下存在人员，不能禁用
storage.code.result.check=仓库名称已存在
department.code.result.check=部门名称已存在
department.code.result.enable=请先启用父级部门
department.prohibit=当前部门或子部门下存在人员，不能禁用
storage.person.result=该账号绑定的人员异常
control.area.existence.department=所选部门已存在其他控制范围，请重新选择
storage.cannot.be.disabled=请检查仓库{0}在供应商发货指令、物流入库、业务入库、物流出库、采购退换货、销售退换货中的使用情况

## 合作伙伴
partner.bank.not.match=银行信息不匹配
partner.person.not.match=人员信息不匹配

## 汇率
currency.exchange.date.repeat = 日期重复
currency.exchange.database.exists = 数据库已存在相同数据，请修改后重试
currency.exchange.rate.query.not.value=请维护当日汇率
currency.exchange.to.cannot.be.empty=币种至不能为空

## 外部价格
external.price.data.repeat = 相同价格日期，相同产品，相同价格类型，只能存在一条数据

## 供应链主体
supplier.status.exception=草稿、已驳回、已生效状态下才能修改
partner.name.exist=合作伙伴名称已存在
approval.error=审批失败，错误码
supplier.cannot.be.disabled=请检查供应链主体{0}在合同中的使用情况

## MDG
data.send.mdg.error=同步MDG失败:{0}
data.send.mdg.return.error=MDG返回报文异常
data.send.mdg.response.error=MDG响应异常
data.send.mdg.response.success=推送MDG成功

## ORDER
supplier.category.relation.not.exist=供应商品类关系不存在
supplier.category.tmp.over=临时供应商超出临时限制时间范围
supplier.category.sample.over=样件供应商超出样件限制额度范围
supplier.honest.contract.not.exist=该供应商尚未签署有效的廉洁协议，请签署后再进行订单提交
order.status.can.not.update=订单状态不支持修改
order.category.not.support=供应商品类与物料号不匹配
requirement.had.order=该批次订单中的零件需求已在其它订单中进行编制，不允许提交
material.code.price.not.same=同一物料号在当前订单中的未税单价必须相同
material.code.factory.not.same=同一样件订单中的物料行工厂必须一致
tmp.status.can.not.be.null=临时供应商品类的物料行，价格有效期不能为空
sample.order.currency.rate.not.same=样件订单物料行中的币种，税率，税码必须一致
sample.order.can.not.be.remove=该订单，不可被删除
material.line.not.exist=物料行不存在
material.line.can.not.syn.to.sap=物料行不能同步至SAP
this.category.have.no.cpe.master=该品类未配置有效的cpe组张r或者cpe-处长
request.supplier.server.error=请求供应商服务失败
query.sample.order.not.exist=查询的样件订单不存在
request.master.data.error=请求主数据服务失败
supplier.have.no.finance.user=未找到供应商有效的财务联系人
division.code.factory.not.same=事业部工厂与物料行工厂不一致

## quota management 2024-10-21
purchase.quota.data.not.exist=数据不存在！
purchase.quota.cant.delete=配额申请单状态不支持删除操作！
purchase.quota.apply.not.exist=申请单不存在！
purchase.quota.only.support.status=仅支持导入失败状态操作！
purchase.quota.detail.not.null=配额申请单明细数据不能为空！
purchase.quota.supplier.repeat=供应商重复！
purchase.quota.total.equals=配额（%）总和需为100%！
purchase.quota.material.not.have.factory=物料未维护工厂！
purchase.quota.material.notIn.factory=物料号{0}未维护在工厂{1}下！
purchase.quota.material.not.exists=物料不存在！
purchase.quota.type.not.match=特殊获取与采购类型不匹配！
purchase.quota.supplier.is.internal=供应商{0}是内部采购！
purchase.quota.supplier.is.external=供应商{0}是外部采购！
purchase.quota.factory.identical=工厂需保持一致！
purchase.quota.material.code.not.exists=物料{0}不存在！
purchase.quota.material.code.not.have.factory=物料{0}未维护工厂信息！
purchase.quota.supplier.not.exists=供应商{0}不存在！
purchase.quota.supplier.not.repeat.in.material=物料{0}下供应商{1}不能重复！
purchase.quota.material.total.equals=物料{0} 配额（%）总和需为100%！
purchase.quota.material.effective.start.date.identical=物料{0}配额有效期（从）需保持一致！
purchase.quota.material.effective.end.date.identical=物料{0}配额有效期（至）需保持一致！
purchase.quota.material.effective.date.relationship=物料{0} 配额有效期（从）不能大于配额有效期（至）！
purchase.quota.material.min.qty.identical=物料{0}最小数量需保持一致！
purchase.quota.material.not.have.category=物料未维护品类关系！
purchase.quota.material.code.not.have.category=物料{0}未维护品类关系！
purchase.quota.material.category.not.self=物料{0}品类不是自己负责！
purchase.quota.is.excel=文件需为Excel！
purchase.quota.not.empty=配额数据不能为空！
purchase.quota.file.export.fail=文件导出失败！
purchase.quota.apply.save.fail=保存申请单信息失败！
purchase.quota.query.data.permission.fail=查询用户数据权限失败！
purchase.quota.file.upload.exception=文件上传异常{0}！
purchase.quota.date.format.error=第{0}行{1}不是有效的日期格式！
purchase.quota.date.format.error1=第{0}行数据'{1}'不是有效的日期格式！
purchase.quota.import.is.number=第{0}行'{1}'需为数字！
purchase.quota.import.is.number1=第{0}行数据'{1}'需为数字！
purchase.quota.import.is.integer=第{0}行'{1}'需为整数！
purchase.quota.import.is.integer1=第{0}行数据'{1}'需为整数！
purchase.quota.purchase.type=第{0}行采购类型不是有效的数据！
purchase.quota.special.obtain=第{0}行特殊获取不是有效的数据！
purchase.quota.validation=第{0}行”{1}“
purchase.quota.material.code.not.null=物料号不能为空！
purchase.quota.factory.not.null=工厂不能为空！
purchase.quota.supplier.code.not.null=供应商编码不能为空！
purchase.quota.not.null=配额不能为空！
purchase.quota.min=配额最小为0！
purchase.quota.max=配额最大为100！
purchase.quota.date.from.not.null=配额有效期（从）不能为空！
purchase.quota.date.to.not.null=配额有效期（至）不能为空！
purchase.quota.remark.limit=备注限制输入200个字符！
purchase.quota.min.qty.scale=最小数量仅支持3位小数！
purchase.quota.max.qty.scale=最大数量仅支持3位小数的数字！
purchase.quota.base.qty.scale=配额基本数量仅支持3位小数的数字！
purchase.quota.max.batch.qty.scale=最大批量仅支持3位小数的数字！
purchase.quota.min.batch.qty.scale=最小批量大小仅支持3位小数的数字！