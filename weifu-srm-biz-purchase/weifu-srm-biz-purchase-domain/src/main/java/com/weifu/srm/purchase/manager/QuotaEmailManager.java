package com.weifu.srm.purchase.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.purchase.enums.ApplyTypeEnum;
import com.weifu.srm.purchase.enums.EmailTemplateEnum;
import com.weifu.srm.purchase.request.AttachmentRecordReqDTO;
import com.weifu.srm.purchase.request.QuotaEmailReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class QuotaEmailManager {
    private final MqManager mqManager;

    public void sendEmail(QuotaEmailReqDTO param, ApplyTypeEnum type, String toEmail, AttachmentRecordReqDTO attachment) {
        EmailTemplateEnum template = EmailTemplateEnum.NEW_QUOTA;

        if (ApplyTypeEnum.BATCH_ADJUST.equals(type))
            template = EmailTemplateEnum.BATCH_ADJUST;

        Map<String, Object> map = new HashMap<>();
        String content = StrUtil.format(template.getTemplate(),
                BeanUtil.beanToMap(param, map, false, false));

        CreateSendEmailTaskMQ mq = new CreateSendEmailTaskMQ();
        mq.setContent(content);
        mq.setTitle(template.getTitle());
        mq.setBusinessType("配额管理");
        mq.setBusinessNo(param.getApplyNo());
        mq.setRecipients(toEmail);
        mq.setCreateBy(param.getUserId());
        mq.setCreateName(param.getUserRealName());

        if (Objects.nonNull(attachment)) {
            List<CreateSendEmailTaskMQ.EmailAttachmentReqDTO> mqAttachments = new ArrayList<>();

            CreateSendEmailTaskMQ.EmailAttachmentReqDTO emailAttachment = new CreateSendEmailTaskMQ.EmailAttachmentReqDTO();
            emailAttachment.setFileName(attachment.getFileOriginalName());
            emailAttachment.setFilePath(attachment.getFileName());
            emailAttachment.setFileUrl(attachment.getFileUrl());

            mqAttachments.add(emailAttachment);

            mq.setAttachments(mqAttachments);
        }

        log.info("配额管理 导入sap成功通知邮件 applyNo:{} 开始发送 mq:{}",param.getApplyNo(),mq);
        mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JSONUtil.toJsonStr(mq));
    }
}
