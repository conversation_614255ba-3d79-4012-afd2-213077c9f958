package com.weifu.srm.purchase.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.purchase.repository.atomicservice.SupplierOrderSampleOrderMapperService;
import com.weifu.srm.purchase.repository.mapper.SupplierOrderSampleOrderMapper;
import com.weifu.srm.purchase.repository.po.SupplierOrderSampleOrderPO;
import com.weifu.srm.purchase.request.order.SampleOrderListReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
@Slf4j
@Service
@AllArgsConstructor
public class SupplierOrderSampleOrderMapperServiceImpl
        extends ServiceImpl<SupplierOrderSampleOrderMapper, SupplierOrderSampleOrderPO>
        implements SupplierOrderSampleOrderMapperService {

    private final SupplierOrderSampleOrderMapper sampleOrderMapper;

    @Override
    public IPage<SupplierOrderSampleOrderPO> queryPage(Page<SupplierOrderSampleOrderPO> page,
                                                       SampleOrderListReqDTO reqDTO,
                                                       List<DataPermissionRespDTO.DataPermissionKeyRespDTO> keys) {
        QueryWrapper<SupplierOrderSampleOrderPO> queryWrapper = getSupplierOrderSampleOrderPOQueryWrapper(reqDTO, keys);
        queryWrapper.lambda()
                .le(ObjectUtils.isNotEmpty(reqDTO.getEndCreateTime()), SupplierOrderSampleOrderPO::getCreateTime, reqDTO.getEndCreateTime())
                .ge(ObjectUtils.isNotEmpty(reqDTO.getStartCreateTime()), SupplierOrderSampleOrderPO::getCreateTime, reqDTO.getStartCreateTime())
                .like(StringUtils.isNotBlank(reqDTO.getOrderNo()), SupplierOrderSampleOrderPO::getOrderNo, reqDTO.getOrderNo())
                .eq(StringUtils.isNotBlank(reqDTO.getOrderType()), SupplierOrderSampleOrderPO::getOrderType, reqDTO.getOrderType())
                .eq(StringUtils.isNotBlank(reqDTO.getStatus()), SupplierOrderSampleOrderPO::getStatus, reqDTO.getStatus())
                .in(CollectionUtils.isNotEmpty(reqDTO.getSubsidiaryCodes()), SupplierOrderSampleOrderPO::getSubsidiaryCode, reqDTO.getSubsidiaryCodes())
                .in(CollectionUtils.isNotEmpty(reqDTO.getSupplierCodes()), SupplierOrderSampleOrderPO::getSupplierCode, reqDTO.getSupplierCodes())
                .orderByDesc(SupplierOrderSampleOrderPO::getCreateTime);
        return sampleOrderMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<SupplierOrderSampleOrderPO> queryList(SampleOrderListReqDTO reqDTO,
                                                      List<DataPermissionRespDTO.DataPermissionKeyRespDTO> keys) {
        QueryWrapper<SupplierOrderSampleOrderPO> queryWrapper = getSupplierOrderSampleOrderPOQueryWrapper(reqDTO, keys);
        queryWrapper.lambda()
                .le(ObjectUtils.isNotEmpty(reqDTO.getEndCreateTime()), SupplierOrderSampleOrderPO::getCreateTime, reqDTO.getEndCreateTime())
                .ge(ObjectUtils.isNotEmpty(reqDTO.getStartCreateTime()), SupplierOrderSampleOrderPO::getCreateTime, reqDTO.getStartCreateTime())
                .like(StringUtils.isNotBlank(reqDTO.getOrderNo()), SupplierOrderSampleOrderPO::getOrderNo, reqDTO.getOrderNo())
                .eq(StringUtils.isNotBlank(reqDTO.getOrderType()), SupplierOrderSampleOrderPO::getOrderType, reqDTO.getOrderType())
                .eq(StringUtils.isNotBlank(reqDTO.getStatus()), SupplierOrderSampleOrderPO::getStatus, reqDTO.getStatus())
                .in(CollectionUtils.isNotEmpty(reqDTO.getSubsidiaryCodes()), SupplierOrderSampleOrderPO::getSubsidiaryCode, reqDTO.getSubsidiaryCodes())
                .in(CollectionUtils.isNotEmpty(reqDTO.getSupplierCodes()), SupplierOrderSampleOrderPO::getSupplierCode, reqDTO.getSupplierCodes());
        return sampleOrderMapper.selectList(queryWrapper);
    }

    private  QueryWrapper<SupplierOrderSampleOrderPO> getSupplierOrderSampleOrderPOQueryWrapper(SampleOrderListReqDTO reqDTO, List<DataPermissionRespDTO.DataPermissionKeyRespDTO> keys) {
        QueryWrapper<SupplierOrderSampleOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(
                w -> {
                    for (DataPermissionRespDTO.DataPermissionKeyRespDTO dataPermission : keys) {
                        switch (dataPermission.getKey()){
                            case "ALL":
                                w.or(children -> children.eq("1","1"));
                                break;
                            case "CREATOR_A_MANAGED_CATEGORY":
                                w.or(children -> children.lambda().in(SupplierOrderSampleOrderPO::getCategoryCode,dataPermission.getCategoryCodes()));
                                w.or(children -> children.lambda().eq(SupplierOrderSampleOrderPO::getCreateBy, reqDTO.getUserId()));
                                break;
                            case "MANAGED_CATEGORY":
                                w.or(children -> children.lambda().in(SupplierOrderSampleOrderPO::getCategoryCode,dataPermission.getCategoryCodes()));
                                break;
                            case "CREATOR":
                                w.or(children -> children.lambda().eq(SupplierOrderSampleOrderPO::getCreateBy, reqDTO.getUserId()));
                                break;
                            default:
                                log.error("not support PermissionKey={}",dataPermission);
                        }
                    }
                }
        );
        return queryWrapper;
    }
}
