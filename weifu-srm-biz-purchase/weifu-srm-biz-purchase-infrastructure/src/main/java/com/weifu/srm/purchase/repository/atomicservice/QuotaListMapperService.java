package com.weifu.srm.purchase.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.purchase.enums.EffectiveStatusEnum;
import com.weifu.srm.purchase.repository.po.QuotaListPO;
import com.weifu.srm.purchase.request.QueryQuotaReqDTO;
import com.weifu.srm.purchase.response.QuotaListRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【quota_list(配额列表)】的数据库操作Service
 * @createDate 2024-09-10 10:16:56
 */
public interface QuotaListMapperService extends IService<QuotaListPO> {

    /**
     * 根据 物料编码 和工厂查询有效状态的配额数据
     * @param materialCode 物料编码
     * @param factory 工厂
     */
    List<QuotaListPO> listByMaterialCode(String materialCode,String factory);


    /**
     * 变更配额数据有效状态
     */
    void changeStatus(List<QuotaListPO> list, EffectiveStatusEnum status);

    /**
     *配额列表
     */
    PageResponse<QuotaListRespDTO> quotaList(QueryQuotaReqDTO param);

    /**
     * 根据 物料编码 和工厂查询有效状态的配额数据
     * @param materialCodes 物料编码
     * @param factory 工厂
     */
    List<QuotaListPO> listByMaterialCodes(List<String> materialCodes,String factory);

}